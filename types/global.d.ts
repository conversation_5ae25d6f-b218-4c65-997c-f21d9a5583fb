export {}
declare global {
  interface Fn<T = any> {
    (...arg: T[]): T
  }

  type Nullable<T> = T | null

  type ElRef<T extends HTMLElement = HTMLDivElement> = Nullable<T>

  type Recordable<T = any, K = string> = Record<K extends null | undefined ? string : K, T>

  type ComponentRef<T> = InstanceType<T>

  type LocaleType = 'zh-CN' | 'en'

  declare type TimeoutHandle = ReturnType<typeof setTimeout>
  declare type IntervalHandle = ReturnType<typeof setInterval>

  type AxiosHeaders =
    | 'application/json'
    | 'application/x-www-form-urlencoded'
    | 'multipart/form-data'

  type AxiosMethod = 'get' | 'post' | 'delete' | 'put' | 'GET' | 'POST' | 'DELETE' | 'PUT'

  type AxiosResponseType = 'arraybuffer' | 'blob' | 'document' | 'json' | 'text' | 'stream'

  interface AxiosConfig {
    params?: any
    data?: any
    url?: string
    method?: AxiosMethod
    headersType?: string
    responseType?: AxiosResponseType
  }

  interface IResponse<T = any> {
    code: string
    data: T extends any ? T : T & any
  }

  interface PageParam {
    pageSize?: number
    pageNo?: number
  }

  interface Tree {
    id: number
    name: string
    children?: Tree[] | any[]
  }

  // 分页数据公共返回
  interface PageResult<T> {
    list: T // 数据
    total: number // 总量
  }

  //辅助获取接口中参数和返回数据的类型
  type GetApiParams<T> = Required<Required<Parameters<T>>[0]>['data']
  //直接获取接口返回的data中类型
  type GetApiRes<T> = Required<Awaited<ReturnType<T>>>['data']
  //通常用于获取列表中的元素类型
  type GetApiResByList<T> = Required<GetApiRes<T>>['list']
  //通常用于获取列表中的元素类型
  type GetApiResByListItem<T> = GetApiResByList<T>[number]

  const AMap:any
}
