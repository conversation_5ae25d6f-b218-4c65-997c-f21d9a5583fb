declare namespace ServiceTypeCcms {
    namespace Schemas {
        /**
         * 文件信息
         */
        export interface AccidentFaultDamageReportFileDO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            businessId?: number; // int64
            name?: string;
            address?: string;
            suffix?: string;
            type?: string;
            deptId?: number; // int64
        }
        /**
         * 数据
         */
        export interface AccidentFaultDamageReportVO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            orderId?: number; // int64
            reportType?: string;
            accidentLevel?: string;
            accidentForm?: string;
            accidentTime?: string; // date-time
            accidentAddress?: string;
            weatherCondition?: string;
            accidentHighwayItLevel?: string;
            accidentHighwayGovLevel?: string;
            accidentHighwaySection?: string;
            accidentReason?: string;
            accidentCarNumber?: string;
            accidentCarType?: string;
            accidentCarModel?: string;
            mainGoods?: string;
            driverName?: string;
            driverNumber?: string;
            accidentReasonAnalysis?: string;
            driverPhone?: string;
            drivingNumber?: string;
            faultLevel?: string;
            faultRemark?: string;
            damageLevel?: string;
            status?: string;
            declareTime?: string; // date-time
            content?: string;
            dealType?: string;
            reductionMoney?: number;
            dealUserId?: number; // int64
            dealTime?: string; // date-time
            deptId?: number; // int64
            /**
             * 处理人名称
             */
            processorsName?: string;
            /**
             * 运单号
             */
            transportNumber?: string;
        }
        /**
         * 管理后台 - 温湿度数据统计 Response VO
         */
        export interface AssertsTempHumRecordCountRespVo {
            /**
             * 平均温度值
             */
            avgTempValue?: number;
            /**
             * 最低温度值
             */
            minTempValue?: number;
            /**
             * 最高温度值
             */
            maxTempValue?: number;
            /**
             * 平均湿度值
             */
            avgHumValue?: number;
            /**
             * 最低湿度值
             */
            minHumValue?: number;
            /**
             * 最高湿度值
             */
            maxHumValue?: number;
        }
        /**
         * 数据
         */
        export interface AssetStatusVO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            saleId?: number; // int64
            assetsId?: string;
            returnStatus?: string;
            deptId?: number; // int64
            /**
             * 客户名称
             */
            customerName?: string;
            /**
             * 租售类型：租赁：lease；销售：sale；
             */
            saleType?: string;
            /**
             * 资产状态：在线：online；离线：offline
             */
            status?: string;
        }
        /**
         * assetsLatLngRecord
         */
        export interface AssetsLatLngRecordDO {
            /**
             * uid
             */
            uid?: number; // int64
            /**
             * 资产ID（设备ID）
             */
            assetId?: string;
            /**
             * 经度
             */
            latValue?: number;
            /**
             * 纬度
             */
            lngValue?: number;
            /**
             * 采集时间
             */
            collectTime?: string; // date-time
            /**
             * 创建时间
             */
            createTime?: string; // date-time
        }
        /**
         * 数据
         */
        export interface AssetsSaleDO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            customerId?: number; // int64
            customerName?: string;
            contactsName?: string;
            contactsPhone?: string;
            saleConfigId?: number; // int64
            saleType?: string;
            leaseTime?: number; // int32
            leaseStartTime?: string; // date-time
            leaseEndTime?: string; // date-time
            number?: number; // int32
            price?: number;
            totalPrice?: number;
            status?: string;
            remark?: string;
            acceptAddress?: string;
            fastMailName?: string;
            fastMailNumber?: string;
            orderTime?: string; // date-time
            deptId?: number; // int64
        }
        export interface AssetsSaleDetailDO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            saleId?: number; // int64
            assetsId?: string;
            returnStatus?: string;
            deptId?: number; // int64
        }
        /**
         * 数据
         */
        export interface AssetsSaleDetailVO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            saleId?: number; // int64
            assetsId?: string;
            /**
             * 已寄回：是：y；否：n；
             */
            returnStatus?: string;
            deptId?: number; // int64
            /**
             * 型号
             */
            assetsModel?: string;
            /**
             * 客户名称
             */
            clientName?: string;
            /**
             * 租赁开始时间
             */
            leaseStartTime?: string; // date-time
            /**
             * 租赁结束时间
             */
            leaseEndTime?: string; // date-time
        }
        /**
         * 数据
         */
        export interface AssetsSalePlanDO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            name?: string;
            assetModel?: string;
            safeTemp?: string;
            safeHumidity?: string;
            minLeaseTime?: number; // int32
            leasePrice?: number;
            salePrice?: number;
            status?: string;
            deptId?: number; // int64
        }
        /**
         * 文件列表
         */
        export interface AssetsSalePlanFileDO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            businessId?: number; // int64
            name?: string;
            address?: string;
            suffix?: string;
            type?: string;
            deptId?: number; // int64
        }
        /**
         * 租售方案详情
         */
        export interface AssetsSalePlanVO {
            fileList?: /* 文件列表 */ AssetsSalePlanFileDO[];
        }
        export interface AssetsSaleVO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            customerId?: number; // int64
            customerName?: string;
            contactsName?: string;
            contactsPhone?: string;
            saleConfigId?: number; // int64
            saleType?: string;
            leaseTime?: number; // int32
            leaseStartTime?: string; // date-time
            leaseEndTime?: string; // date-time
            number?: number; // int32
            price?: number;
            totalPrice?: number;
            status?: string;
            remark?: string;
            acceptAddress?: string;
            fastMailName?: string;
            fastMailNumber?: string;
            orderTime?: string; // date-time
            deptId?: number; // int64
            detailList?: AssetsSaleDetailDO[];
        }
        /**
         * assetsTempHumRecord
         */
        export interface AssetsTempHumRecordDO {
            /**
             * uid
             */
            uid?: number; // int64
            /**
             * 资产ID（设备ID）
             */
            assetId?: string;
            /**
             * 温度值
             */
            tempValue?: number;
            /**
             * 湿度值
             */
            humValue?: number;
            /**
             * 采集时间
             */
            collectTime?: string; // date-time
            /**
             * 创建时间
             */
            createTime?: string; // date-time
        }
        /**
         * AssetsVo
         */
        export interface AssetsVo {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            assetsId?: string;
            factoryId?: number; // int64
            assetsModel?: string;
            ssid?: string;
            imei?: string;
            simNo?: string;
            status?: string;
            remark?: string;
            lastUpdateTime?: string; // date-time
            deptId?: number; // int64
            /**
             * 厂商名称
             */
            factoryName?: string;
            /**
             * 租赁开始时间
             */
            leaseStartTime?: string; // date-time
            /**
             * 租赁结束时间
             */
            leaseEndTime?: string; // date-time
            /**
             * 客户名称
             */
            customerName?: string;
            /**
             * 数据上传周期（分钟）
             */
            activeWaitTime?: number; // int32
            /**
             * 温湿度采样周期（分钟）
             */
            intervalTime?: number; // int32
            /**
             * 温度校准值
             */
            tempCalibration?: number; // float
            /**
             * 湿度校准值
             */
            humCalibration?: number; // float
            /**
             * 周期状态
             */
            periodStatus?: string;
        }
        /**
         * 配置列表
         */
        export interface BusinessRuleConfigDO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            ruleId?: number; // int64
            configId?: number; // int64
            configType?: string;
            deptId?: number; // int64
        }
        /**
         * 数据
         */
        export interface BusinessRuleDO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            name?: string;
            userType?: string;
            remark?: string;
            status?: string;
            deptId?: number; // int64
        }
        export interface BusinessRuleVo {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            name?: string;
            userType?: string;
            remark?: string;
            status?: string;
            deptId?: number; // int64
            /**
             * 配置列表
             */
            configTypeList?: /* 配置列表 */ BusinessRuleConfigDO[];
        }
        /**
         * 数据
         */
        export interface CarInsureDO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            userId?: number; // int64
            insureNumber?: string;
            insureType?: string;
            carNumber?: string;
            insureStartTime?: string; // date-time
            insureEndTime?: string; // date-time
            vinNo?: string;
            brandModel?: string;
            carMasterName?: string;
            phone?: string;
            status?: string;
            deptId?: number; // int64
        }
        /**
         * 车辆保险VO
         */
        export interface CarInsureVO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            userId?: number; // int64
            insureNumber?: string;
            insureType?: string;
            carNumber?: string;
            insureStartTime?: string; // date-time
            insureEndTime?: string; // date-time
            vinNo?: string;
            brandModel?: string;
            carMasterName?: string;
            phone?: string;
            status?: string;
            deptId?: number; // int64
            /**
             * 电子报单
             */
            fileList?: FileDO[];
        }
        /**
         * 数据
         */
        export interface CarLengthDO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: string;
            carLength?: string;
            carWeight?: string;
            carVolume?: string;
            deptId?: number; // int64
        }
        /**
         * 数据
         */
        export interface CarModelDO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: string;
            name?: string;
            status?: string;
            deptId?: number; // int64
        }
        /**
         * 数据
         */
        export interface CarRepairFormDO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            userId?: number; // int64
            formNumber?: string;
            carNumber?: string;
            vinNo?: string;
            fixTotalPrice?: number;
            fixName?: string;
            fixPhone?: string;
            status?: string;
            dealResult?: string;
            dealRemark?: string;
            deptId?: number; // int64
        }
        /**
         * 维修报单VO
         */
        export interface CarRepairFormVO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            userId?: number; // int64
            formNumber?: string;
            carNumber?: string;
            vinNo?: string;
            fixTotalPrice?: number;
            fixName?: string;
            fixPhone?: string;
            status?: string;
            dealResult?: string;
            dealRemark?: string;
            deptId?: number; // int64
            /**
             * 电子报单
             */
            fileList?: FileDO[];
        }
        export interface CarRouteDO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            driverId?: string;
            carType?: string;
            startProvince?: string;
            startCity?: string;
            endProvince?: string;
            endCity?: string;
            deptId?: number; // int64
        }
        /**
         * 车辆路线
         */
        export interface CarRouteDTO {
            /**
             * ID
             */
            id?: number; // int64
            /**
             * 驾驶员ID
             */
            driverId: number; // int64
            /**
             * 运输类型：整车：whole_car；零担：less_car
             */
            carType: "whole_car" | "less_car";
            /**
             * 开始省
             */
            startProvince: string;
            /**
             * 开始市
             */
            startCity: string;
            /**
             * 结束省
             */
            endProvince: string;
            /**
             * 结束市
             */
            endCity: string;
        }
        /**
         * 数据
         */
        export interface CarRouteVO {
            /**
             * ID
             */
            id?: number; // int64
            /**
             * 驾驶员ID
             */
            DRIVER_ID?: string;
            /**
             * 车辆ID
             */
            carId?: string;
            /**
             * 开始省
             */
            startProvince?: string;
            /**
             * 开始市
             */
            startCity?: string;
            /**
             * 结束省
             */
            endProvince?: string;
            /**
             * 结束市
             */
            endCity?: string;
            /**
             * 创建时间
             */
            createTime?: string; // date-time
            /**
             * 车牌号
             */
            carNumber?: string;
            /**
             * 车型名称
             */
            carModelName?: string;
            /**
             * 车长
             */
            carLength?: string;
            /**
             * 车辆类别：A：A类；B：B类；C：C类；D：D类；E：E类；F：F类；G：G类；H：H类；
             */
            carCategory?: string;
            /**
             * 运输类型：整车：whole_car；零担：less_car
             */
            carRouteType?: string;
            /**
             * 运输车辆类型：normal：常规车；cold：冷藏车
             */
            carType?: string;
            /**
             * 驾驶员名称
             */
            driverName?: string;
            /**
             * 运力类型：个人：person；自营：self；承运商：carrier；
             */
            type?: string;
            /**
             * 承运商名称
             */
            carrierName?: string;
            driver_ID?: string;
        }
        /**
         * 数据
         */
        export interface CarVO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: string;
            carrierId?: string;
            driverId?: string;
            carNumber?: string;
            carType?: string;
            carCategory?: string;
            driverNumber?: string;
            carModelId?: number; // int64
            carModelName?: string;
            carLength?: string;
            carLengthId?: number; // int64
            maximumPayload?: string;
            registerTime?: string; // date-time
            type?: string;
            dataSource?: string;
            status?: string;
            deptId?: number; // int64
            /**
             * 是否已绑定驾驶员：y:是；n:否
             */
            isBind?: string;
            /**
             * 承运商名称
             */
            carrierName?: string;
            /**
             * 驾驶人名称
             */
            driverName?: string;
            /**
             * 驾驶人联系方式
             */
            driverPhone?: string;
            /**
             * 车牌颜色
             * example:
             * 2
             */
            vehiclePlateColor: string;
            /**
             * 能源类型
             * example:
             * 2
             */
            energyType: string;
            /**
             * 总重量
             * example:
             * 25000
             */
            totalWeight: number;
            /**
             * 车长
             * example:
             * 7370
             */
            vehicleLen: number;
            /**
             * 车宽
             * example:
             * 2545
             */
            vehicleWidth: number;
            /**
             * 车高
             * example:
             * 2550
             */
            vehicleHeight: number;
            /**
             * 文件列表
             */
            files?: FileDO[];
        }
        /**
         * 数据
         */
        export interface CarrierDO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: string;
            name?: string;
            contacts?: string;
            phone?: string;
            telephone?: string;
            email?: string;
            address?: string;
            status?: string;
            deptId?: number; // int64
        }
        /**
         * 冷链运输统计
         */
        export interface ColdCountVO {
            /**
             * 冷链设施总数
             */
            coldConfigNum?: number; // int64
            /**
             * 冷链监控次数
             */
            coldMonitorNum?: number; // int64
            /**
             * 异常信息数
             */
            errorNum?: number; // int64
            /**
             * 告警信息数
             */
            warnNum?: number; // int64
        }
        export interface CommonResultAssertsTempHumRecordCountRespVo {
            code?: number; // int32
            data?: /* 管理后台 - 温湿度数据统计 Response VO */ AssertsTempHumRecordCountRespVo;
            msg?: string;
        }
        export interface CommonResultAssetsSalePlanVO {
            code?: number; // int32
            data?: /* 租售方案详情 */ AssetsSalePlanVO;
            msg?: string;
        }
        export interface CommonResultAssetsSaleVO {
            code?: number; // int32
            data?: AssetsSaleVO;
            msg?: string;
        }
        export interface CommonResultAssetsVo {
            code?: number; // int32
            data?: /* AssetsVo */ AssetsVo;
            msg?: string;
        }
        export interface CommonResultBigDecimal {
            code?: number; // int32
            data?: number;
            msg?: string;
        }
        export interface CommonResultBoolean {
            code?: number; // int32
            data?: boolean;
            msg?: string;
        }
        export interface CommonResultBusinessRuleVo {
            code?: number; // int32
            data?: BusinessRuleVo;
            msg?: string;
        }
        export interface CommonResultCarInsureVO {
            code?: number; // int32
            data?: /* 车辆保险VO */ CarInsureVO;
            msg?: string;
        }
        export interface CommonResultCarRepairFormVO {
            code?: number; // int32
            data?: /* 维修报单VO */ CarRepairFormVO;
            msg?: string;
        }
        export interface CommonResultCarRouteDO {
            code?: number; // int32
            data?: CarRouteDO;
            msg?: string;
        }
        export interface CommonResultCarVO {
            code?: number; // int32
            data?: /* 数据 */ CarVO;
            msg?: string;
        }
        export interface CommonResultCarrierDO {
            code?: number; // int32
            data?: /* 数据 */ CarrierDO;
            msg?: string;
        }
        export interface CommonResultColdCountVO {
            code?: number; // int32
            data?: /* 冷链运输统计 */ ColdCountVO;
            msg?: string;
        }
        export interface CommonResultCountAnalysisMapRespVO {
            code?: number; // int32
            data?: /* 管理后台 - 大屏统计地图 Response VO */ CountAnalysisMapRespVO;
            msg?: string;
        }
        export interface CommonResultCountVO {
            code?: number; // int32
            data?: /* 统计VO */ CountVO;
            msg?: string;
        }
        export interface CommonResultCustomerCountVO {
            code?: number; // int32
            data?: /* 客户统计 */ CustomerCountVO;
            msg?: string;
        }
        export interface CommonResultCustomerVO {
            code?: number; // int32
            data?: /* 数据 */ CustomerVO;
            msg?: string;
        }
        export interface CommonResultDictDataRespDTO {
            code?: number; // int32
            data?: /* RPC 服务 - 字典数据 Response DTO */ DictDataRespDTO;
            msg?: string;
        }
        export interface CommonResultDriverCountVO {
            code?: number; // int32
            data?: /* 驾驶员统计 */ DriverCountVO;
            msg?: string;
        }
        export interface CommonResultDriverDO {
            code?: number; // int32
            data?: DriverDO;
            msg?: string;
        }
        export interface CommonResultDriverVO {
            code?: number; // int32
            data?: /* 数据 */ DriverVO;
            msg?: string;
        }
        export interface CommonResultErrorConfigDO {
            code?: number; // int32
            data?: ErrorConfigDO;
            msg?: string;
        }
        export interface CommonResultExceptionHandlingVo {
            code?: number; // int32
            data?: ExceptionHandlingVo;
            msg?: string;
        }
        export interface CommonResultExpenseManagementVo {
            code?: number; // int32
            data?: /* 数据 */ ExpenseManagementVo;
            msg?: string;
        }
        export interface CommonResultExpenseTimeDO {
            code?: number; // int32
            data?: /* 数据 */ ExpenseTimeDO;
            msg?: string;
        }
        export interface CommonResultFactoryManageDO {
            code?: number; // int32
            data?: /* 数据 */ FactoryManageDO;
            msg?: string;
        }
        export interface CommonResultFeedbackVO {
            code?: number; // int32
            data?: /* 数据 */ FeedbackVO;
            msg?: string;
        }
        export interface CommonResultFileDO {
            code?: number; // int32
            data?: FileDO;
            msg?: string;
        }
        export interface CommonResultGetStaffCashSettlementVO {
            code?: number; // int32
            data?: GetStaffCashSettlementVO;
            msg?: string;
        }
        export interface CommonResultGoodCountVO {
            code?: number; // int32
            data?: /* 货物统计 */ GoodCountVO;
            msg?: string;
        }
        export interface CommonResultHomeCountVO {
            code?: number; // int32
            data?: /* 导航页统计 */ HomeCountVO;
            msg?: string;
        }
        export interface CommonResultListAccidentFaultDamageReportFileDO {
            code?: number; // int32
            data?: /* 文件信息 */ AccidentFaultDamageReportFileDO[];
            msg?: string;
        }
        export interface CommonResultListAssetsLatLngRecordDO {
            code?: number; // int32
            data?: /* assetsLatLngRecord */ AssetsLatLngRecordDO[];
            msg?: string;
        }
        export interface CommonResultListAssetsTempHumRecordDO {
            code?: number; // int32
            data?: /* assetsTempHumRecord */ AssetsTempHumRecordDO[];
            msg?: string;
        }
        export interface CommonResultListCarRouteDO {
            code?: number; // int32
            data?: CarRouteDO[];
            msg?: string;
        }
        export interface CommonResultListCarVO {
            code?: number; // int32
            data?: /* 数据 */ CarVO[];
            msg?: string;
        }
        export interface CommonResultListCountAnalysisMapOrderTotalRespVO {
            code?: number; // int32
            data?: /* 管理后台 - 大屏统计地图订单统计 Response VO */ CountAnalysisMapOrderTotalRespVO[];
            msg?: string;
        }
        export interface CommonResultListCountAnalysisRespVO {
            code?: number; // int32
            data?: /* 管理后台 - 大屏统计 Response VO */ CountAnalysisRespVO[];
            msg?: string;
        }
        export interface CommonResultListCountMapOrderListVO {
            code?: number; // int32
            data?: /* 管理后台 - 大屏统计地图 CountMapOrderListVO */ CountMapOrderListVO[];
            msg?: string;
        }
        export interface CommonResultListCountVO {
            code?: number; // int32
            data?: /* 统计VO */ CountVO[];
            msg?: string;
        }
        export interface CommonResultListCustomerGroupDO {
            code?: number; // int32
            data?: CustomerGroupDO[];
            msg?: string;
        }
        export interface CommonResultListDictDataRespDTO {
            code?: number; // int32
            data?: /* RPC 服务 - 字典数据 Response DTO */ DictDataRespDTO[];
            msg?: string;
        }
        export interface CommonResultListDriverSignVo {
            code?: number; // int32
            data?: DriverSignVo[];
            msg?: string;
        }
        export interface CommonResultListEnum {
            code?: number; // int32
            data?: string[];
            msg?: string;
        }
        export interface CommonResultListExpenseTypeDO {
            code?: number; // int32
            data?: ExpenseTypeDO[];
            msg?: string;
        }
        export interface CommonResultListFeedbackTypeDO {
            code?: number; // int32
            data?: FeedbackTypeDO[];
            msg?: string;
        }
        export interface CommonResultListGoodsTypeDO {
            code?: number; // int32
            data?: GoodsTypeDO[];
            msg?: string;
        }
        export interface CommonResultListPackingTypeDO {
            code?: number; // int32
            data?: PackingTypeDO[];
            msg?: string;
        }
        export interface CommonResultListString {
            code?: number; // int32
            data?: string[];
            msg?: string;
        }
        export interface CommonResultListTransportOrderAssertsVO {
            code?: number; // int32
            data?: /* 订单设备及电量 */ TransportOrderAssertsVO[];
            msg?: string;
        }
        export interface CommonResultListTransportOrderCheckVO {
            code?: number; // int32
            data?: /* 运单中途抽检transportOrderCheckVo */ TransportOrderCheckVO[];
            msg?: string;
        }
        export interface CommonResultListTransportOrderChildVO {
            code?: number; // int32
            data?: /* 订单详情TransportOrderChildVO */ TransportOrderChildVO[];
            msg?: string;
        }
        export interface CommonResultListTransportOrderMergeVO {
            code?: number; // int32
            data?: /* 订单能合单的详情 */ TransportOrderMergeVO[];
            msg?: string;
        }
        export interface CommonResultLoadingStrategyConfigDO {
            code?: number; // int32
            data?: /* 数据 */ LoadingStrategyConfigDO;
            msg?: string;
        }
        export interface CommonResultMapStringListEnum {
            code?: number; // int32
            data?: {
                [name: string]: string[];
            };
            msg?: string;
        }
        export interface CommonResultPageResultAccidentFaultDamageReportVO {
            code?: number; // int32
            data?: /* 分页结果 */ PageResultAccidentFaultDamageReportVO;
            msg?: string;
        }
        export interface CommonResultPageResultAssetStatusVO {
            code?: number; // int32
            data?: /* 分页结果 */ PageResultAssetStatusVO;
            msg?: string;
        }
        export interface CommonResultPageResultAssetsSaleDO {
            code?: number; // int32
            data?: /* 分页结果 */ PageResultAssetsSaleDO;
            msg?: string;
        }
        export interface CommonResultPageResultAssetsSaleDetailVO {
            code?: number; // int32
            data?: /* 分页结果 */ PageResultAssetsSaleDetailVO;
            msg?: string;
        }
        export interface CommonResultPageResultAssetsSalePlanDO {
            code?: number; // int32
            data?: /* 分页结果 */ PageResultAssetsSalePlanDO;
            msg?: string;
        }
        export interface CommonResultPageResultAssetsVo {
            code?: number; // int32
            data?: /* 分页结果 */ PageResultAssetsVo;
            msg?: string;
        }
        export interface CommonResultPageResultBusinessRuleDO {
            code?: number; // int32
            data?: /* 分页结果 */ PageResultBusinessRuleDO;
            msg?: string;
        }
        export interface CommonResultPageResultCarInsureDO {
            code?: number; // int32
            data?: /* 分页结果 */ PageResultCarInsureDO;
            msg?: string;
        }
        export interface CommonResultPageResultCarLengthDO {
            code?: number; // int32
            data?: /* 分页结果 */ PageResultCarLengthDO;
            msg?: string;
        }
        export interface CommonResultPageResultCarModelDO {
            code?: number; // int32
            data?: /* 分页结果 */ PageResultCarModelDO;
            msg?: string;
        }
        export interface CommonResultPageResultCarRepairFormDO {
            code?: number; // int32
            data?: /* 分页结果 */ PageResultCarRepairFormDO;
            msg?: string;
        }
        export interface CommonResultPageResultCarRouteVO {
            code?: number; // int32
            data?: /* 分页结果 */ PageResultCarRouteVO;
            msg?: string;
        }
        export interface CommonResultPageResultCarVO {
            code?: number; // int32
            data?: /* 分页结果 */ PageResultCarVO;
            msg?: string;
        }
        export interface CommonResultPageResultCarrierDO {
            code?: number; // int32
            data?: /* 分页结果 */ PageResultCarrierDO;
            msg?: string;
        }
        export interface CommonResultPageResultCustomerVO {
            code?: number; // int32
            data?: /* 分页结果 */ PageResultCustomerVO;
            msg?: string;
        }
        export interface CommonResultPageResultDriverSignVo {
            code?: number; // int32
            data?: /* 分页结果 */ PageResultDriverSignVo;
            msg?: string;
        }
        export interface CommonResultPageResultDriverVO {
            code?: number; // int32
            data?: /* 分页结果 */ PageResultDriverVO;
            msg?: string;
        }
        export interface CommonResultPageResultExpenseManagementVo {
            code?: number; // int32
            data?: /* 分页结果 */ PageResultExpenseManagementVo;
            msg?: string;
        }
        export interface CommonResultPageResultExpenseTimeDO {
            code?: number; // int32
            data?: /* 分页结果 */ PageResultExpenseTimeDO;
            msg?: string;
        }
        export interface CommonResultPageResultFactoryManageDO {
            code?: number; // int32
            data?: /* 分页结果 */ PageResultFactoryManageDO;
            msg?: string;
        }
        export interface CommonResultPageResultFeedbackVO {
            code?: number; // int32
            data?: /* 分页结果 */ PageResultFeedbackVO;
            msg?: string;
        }
        export interface CommonResultPageResultFixedPointRouteDO {
            code?: number; // int32
            data?: /* 分页结果 */ PageResultFixedPointRouteDO;
            msg?: string;
        }
        export interface CommonResultPageResultLoadingStrategyConfigDO {
            code?: number; // int32
            data?: /* 分页结果 */ PageResultLoadingStrategyConfigDO;
            msg?: string;
        }
        export interface CommonResultPageResultMaintenanceShopDO {
            code?: number; // int32
            data?: /* 分页结果 */ PageResultMaintenanceShopDO;
            msg?: string;
        }
        export interface CommonResultPageResultStaffCashSettlementVO {
            code?: number; // int32
            data?: /* 分页结果 */ PageResultStaffCashSettlementVO;
            msg?: string;
        }
        export interface CommonResultPageResultStaffManageVo {
            code?: number; // int32
            data?: /* 分页结果 */ PageResultStaffManageVo;
            msg?: string;
        }
        export interface CommonResultPageResultTempRangConfigVO {
            code?: number; // int32
            data?: /* 分页结果 */ PageResultTempRangConfigVO;
            msg?: string;
        }
        export interface CommonResultPageResultTransportOrderErrorRecordDO {
            code?: number; // int32
            data?: /* 分页结果 */ PageResultTransportOrderErrorRecordDO;
            msg?: string;
        }
        export interface CommonResultPageResultTransportOrderVO {
            code?: number; // int32
            data?: /* 分页结果 */ PageResultTransportOrderVO;
            msg?: string;
        }
        export interface CommonResultPageResultTransportOrderWarnRecordDO {
            code?: number; // int32
            data?: /* 分页结果 */ PageResultTransportOrderWarnRecordDO;
            msg?: string;
        }
        export interface CommonResultPageResultTransportSchemeConfigDO {
            code?: number; // int32
            data?: /* 分页结果 */ PageResultTransportSchemeConfigDO;
            msg?: string;
        }
        export interface CommonResultPageResultTransportVO {
            code?: number; // int32
            data?: /* 分页结果 */ PageResultTransportVO;
            msg?: string;
        }
        export interface CommonResultPageResultWarnConfigDO {
            code?: number; // int32
            data?: /* 分页结果 */ PageResultWarnConfigDO;
            msg?: string;
        }
        export interface CommonResultRouteVO {
            code?: number; // int32
            data?: RouteVO;
            msg?: string;
        }
        export interface CommonResultStaffManageDO {
            code?: number; // int32
            data?: StaffManageDO;
            msg?: string;
        }
        export interface CommonResultString {
            code?: number; // int32
            data?: string;
            msg?: string;
        }
        export interface CommonResultSystemConfigDO {
            code?: number; // int32
            data?: SystemConfigDO;
            msg?: string;
        }
        export interface CommonResultSystemImgVO {
            code?: number; // int32
            data?: SystemImgVO;
            msg?: string;
        }
        export interface CommonResultTempRangConfigDetailDOVO {
            code?: number; // int32
            data?: /* 管理后台 - 温区配置详情 */ TempRangConfigDetailDOVO;
            msg?: string;
        }
        export interface CommonResultTradeCountVO {
            code?: number; // int32
            data?: /* 营业额统计 */ TradeCountVO;
            msg?: string;
        }
        export interface CommonResultTransportOrderErrorRecordDO {
            code?: number; // int32
            data?: /* 数据 */ TransportOrderErrorRecordDO;
            msg?: string;
        }
        export interface CommonResultTransportOrderVO {
            code?: number; // int32
            data?: /**
             * 订单详情
             * 数据
             */
            TransportOrderVO;
            msg?: string;
        }
        export interface CommonResultTransportOrderWarnRecordDO {
            code?: number; // int32
            data?: /* 数据 */ TransportOrderWarnRecordDO;
            msg?: string;
        }
        export interface CommonResultTransportSchemeConfigDO {
            code?: number; // int32
            data?: /* 数据 */ TransportSchemeConfigDO;
            msg?: string;
        }
        export interface CommonResultWarnConfigDO {
            code?: number; // int32
            data?: /* 数据 */ WarnConfigDO;
            msg?: string;
        }
        /**
         * 管理后台 - 大屏统计地图订单统计 Response VO
         */
        export interface CountAnalysisMapOrderTotalRespVO {
            /**
             * 发货省
             * example:
             * 省
             */
            sendProvince?: string;
            /**
             * 发货市
             * example:
             * 市
             */
            sendCity?: string;
            /**
             * 发货区县
             * example:
             * 区县
             */
            sendCounty?: string;
            /**
             * 卸货省
             * example:
             * 省
             */
            province?: string;
            /**
             * 卸货市
             * example:
             * 市
             */
            city?: string;
            /**
             * 卸货区县
             * example:
             * 区县
             */
            county?: string;
            /**
             * 车辆数量
             * example:
             * 1
             */
            number?: string;
        }
        /**
         * 管理后台 - 大屏统计地图 Response VO
         */
        export interface CountAnalysisMapRespVO {
            /**
             * 运单号
             * example:
             * YD202501010101010001
             */
            transportNumber?: string;
            /**
             * 车牌号
             * example:
             * 粤A123456
             */
            transportCarNumber?: string;
            /**
             * 运输车辆类别
             * example:
             * 运输车辆类别：A：A类；B：B类；C：C类；D：D类；E：E类；F：F类；G：G类；H：H类；
             */
            transportCarCategory?: string;
            /**
             * 温度
             * example:
             * -12℃
             */
            temp?: string;
            /**
             * 告警
             * example:
             * 当前温度高于-13℃,需要增加打冷
             */
            warn?: string;
            /**
             * 发货省
             * example:
             * 省
             */
            sendProvince?: string;
            /**
             * 发货市
             * example:
             * 市
             */
            sendCity?: string;
            /**
             * 发货区县
             * example:
             * 区县
             */
            sendCounty?: string;
            /**
             * 发货地址
             * example:
             * 地址
             */
            sendAddress?: string;
            /**
             * 卸货省
             * example:
             * 省
             */
            province?: string;
            /**
             * 卸货市
             * example:
             * 市
             */
            city?: string;
            /**
             * 卸货区县
             * example:
             * 区县
             */
            county?: string;
            /**
             * 卸货地址
             * example:
             * 地址
             */
            address?: string;
        }
        /**
         * 管理后台 - 大屏统计 Response VO
         */
        export interface CountAnalysisRespVO {
            /**
             * 统计类型
             * example:
             * 名称
             */
            type?: string;
            /**
             * 统计名称
             * example:
             * 张三
             */
            name?: string;
            /**
             * 统计值
             * example:
             * 16773
             */
            value?: string;
            /**
             * 百分比例
             * example:
             * 3.5
             */
            ratio?: string;
            /**
             * 同比
             * example:
             * 3.5
             */
            momRatio?: string;
            /**
             * 环比
             * example:
             * 3.5
             */
            qoqRatio?: string;
        }
        /**
         * 管理后台 - 大屏统计地图 CountMapOrderListVO
         */
        export interface CountMapOrderListVO {
            /**
             * 运单号
             * example:
             * YD202501010101010001
             */
            transportNumber?: string;
            /**
             * 车牌号
             * example:
             * 粤A123456
             */
            transportCarNumber?: string;
            /**
             * 运输车辆类型：normal：常规车；cold：冷藏车
             * example:
             * normal
             */
            transportCarType?: string;
        }
        /**
         * 统计VO
         */
        export interface CountVO {
            /**
             * 统计名称
             */
            name?: string;
            /**
             * 统计值
             */
            value?: number;
            /**
             * 同比=以上年同期为基期相比较，即本期某一时间段与上年某一时间段相比，如2018年4月份与2017年4月份相比较
             */
            yearOnYearRatio?: number;
            /**
             * 环比=与上一个相邻统计周期相比较，即n月与第n-1月的比较1
             */
            monthOnMonthRatio?: number;
        }
        /**
         * 客户统计
         */
        export interface CustomerCountVO {
            /**
             * 客户数量
             */
            customerNum?: number; // int64
            /**
             * 订单数量
             */
            orderNum?: number; // int64
            /**
             * 运单数量
             */
            transportNum?: number; // int64
            /**
             * 货物总重(吨)
             */
            goodWeight?: number;
            /**
             * 运单金额(万元)
             */
            transportOrderMoney?: number;
            /**
             * 接单率
             */
            acceptOrderRatio?: number;
            /**
             * 完单率
             */
            finishOrderRatio?: number;
        }
        /**
         * 附件信息
         */
        export interface CustomerFileVO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            customerId?: number; // int64
            name?: string;
            address?: string;
            suffix?: string;
            type?: string;
            deptId?: number; // int64
        }
        export interface CustomerGroupDO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: string;
            name?: string;
            remark?: string;
            status?: string;
            deptId?: number; // int64
        }
        /**
         * 状态变更记录
         */
        export interface CustomerStatusHistoryDO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            customerId?: number; // int64
            name?: string;
            status?: string;
            operator?: string;
            remark?: string;
            changeTime?: string; // date-time
            deptId?: number; // int64
        }
        /**
         * 帐号信息
         */
        export interface CustomerUserDO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            customerId?: number; // int64
            loginName?: string;
            name?: string;
            phone?: string;
            childAccountsNum?: string;
            status?: string;
            type?: string;
            deptId?: number; // int64
        }
        /**
         * 数据
         */
        export interface CustomerVO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: string;
            customerNumber?: string;
            name?: string;
            groupId?: number; // int64
            type?: string;
            status?: string;
            customerSource?: string;
            auditStatus?: string;
            applyTime?: string; // date-time
            registerTime?: string; // date-time
            creditCode?: string;
            depositBank?: string;
            bankAccount?: string;
            legalPerson?: string;
            idCard?: string;
            legalPhone?: string;
            contactsName?: string;
            contactsPhone?: string;
            telephone?: string;
            email?: string;
            industry?: string;
            companySize?: string;
            city?: string;
            address?: string;
            businessScope?: string;
            remark?: string;
            companyAddress?: string;
            deptId?: number; // int64
            customerUser?: /* 帐号信息 */ CustomerUserDO;
            /**
             * 附件信息
             */
            files?: /* 附件信息 */ CustomerFileVO[];
            /**
             * 状态变更记录
             */
            histories?: /* 状态变更记录 */ CustomerStatusHistoryDO[];
            /**
             * 客户分组名称
             */
            groupName?: string;
        }
        /**
         * RPC 服务 - 字典数据 Response DTO
         */
        export interface DictDataRespDTO {
            /**
             * 字典标签
             */
            label: string;
            /**
             * 字典值
             */
            value: string;
            /**
             * 字典类型
             * example:
             * sys_common_sex
             */
            dictType: string;
            /**
             * 状态
             * example:
             * 1
             */
            status: number; // int32
        }
        /**
         * 驾驶员统计
         */
        export interface DriverCountVO {
            /**
             * 驾驶员数量
             */
            driverNum?: number; // int64
            /**
             * 订单数量
             */
            orderNum?: number; // int64
            /**
             * 运单数量
             */
            transportNum?: number; // int64
            /**
             * 运单发车数量
             */
            transportCarNum?: number; // int64
            /**
             * 注册车辆数
             */
            registerCarNum?: number; // int64
            /**
             * 接单率
             */
            acceptOrderRatio?: number;
            /**
             * 完单率
             */
            finishOrderRatio?: number;
        }
        export interface DriverDO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: string;
            userId?: number; // int64
            carrierId?: string;
            carrierName?: string;
            name?: string;
            sex?: string;
            phone?: string;
            idCard?: string;
            driverLicense?: string;
            email?: string;
            authDriverModel?: string;
            issueDate?: string; // date
            validDate?: string; // date
            registerTime?: string; // date-time
            type?: string;
            dataSource?: string;
            status?: string;
            deptId?: number; // int64
        }
        export interface DriverSignVo {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            signTime?: string; // date-time
            driverId?: number; // int64
            carNumber?: string;
            deptId?: number; // int64
            /**
             * 驾驶人名称
             */
            driverName?: string;
            /**
             * 注册时长
             */
            durationOfRegistration?: string;
            /**
             * 今日签到
             */
            signStatus?: "already_sign" | "not_sign";
            signNumber?: number; // int32
        }
        /**
         * 数据
         */
        export interface DriverVO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: string;
            userId?: number; // int64
            carrierId?: string;
            carrierName?: string;
            name?: string;
            sex?: string;
            phone?: string;
            idCard?: string;
            driverLicense?: string;
            email?: string;
            authDriverModel?: string;
            issueDate?: string; // date
            validDate?: string; // date
            registerTime?: string; // date-time
            type?: string;
            dataSource?: string;
            status?: string;
            deptId?: number; // int64
            /**
             * 文件列表
             */
            files?: FileDO[];
        }
        export interface ErrorConfigDO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            responseError?: string;
            locationError?: string;
            status?: string;
            deptId?: number; // int64
        }
        export interface ExceptionHandlingVo {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            orderId?: number; // int64
            reportType?: string;
            accidentLevel?: string;
            accidentForm?: string;
            accidentTime?: string; // date-time
            accidentAddress?: string;
            weatherCondition?: string;
            accidentHighwayItLevel?: string;
            accidentHighwayGovLevel?: string;
            accidentHighwaySection?: string;
            accidentReason?: string;
            accidentCarNumber?: string;
            accidentCarType?: string;
            accidentCarModel?: string;
            mainGoods?: string;
            driverName?: string;
            driverNumber?: string;
            accidentReasonAnalysis?: string;
            driverPhone?: string;
            drivingNumber?: string;
            faultLevel?: string;
            faultRemark?: string;
            damageLevel?: string;
            status?: string;
            declareTime?: string; // date-time
            content?: string;
            dealType?: string;
            reductionMoney?: number;
            dealUserId?: number; // int64
            dealTime?: string; // date-time
            deptId?: number; // int64
            /**
             * 处理人名称
             */
            processorsName?: string;
            /**
             * 最终金额
             */
            finalAmount?: number;
            /**
             * 运输费用
             */
            totalMoney?: number;
        }
        /**
         * 费用配置计费时段
         */
        export interface ExpenseConfigTimeDO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            expenseId?: number; // int64
            expenseTimeId?: number; // int64
            billingType?: string;
            content?: string;
            price?: number;
            deptId?: number; // int64
        }
        /**
         * 数据
         */
        export interface ExpenseManagementVo {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            name?: string;
            expenseTypeId?: string;
            billingType?: string;
            remark?: string;
            status?: string;
            deptId?: number; // int64
            /**
             * 费用类型名称
             */
            expenseTypeName?: string;
            /**
             * 费用配置计费时段列表
             */
            expenseConfigTimeList?: /* 费用配置计费时段 */ ExpenseConfigTimeDO[];
        }
        /**
         * 数据
         */
        export interface ExpenseTimeDO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            name?: string;
            startTime?: string; // date-time
            endTime?: string; // date-time
            status?: string;
            remark?: string;
            deptId?: number; // int64
        }
        export interface ExpenseTypeDO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: string;
            name?: string;
            code?: string;
            remark?: string;
            status?: string;
            deptId?: number; // int64
        }
        /**
         * 数据
         */
        export interface FactoryManageDO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            name?: string;
            contactsName?: string;
            phone?: string;
            address?: string;
            status?: string;
            remark?: string;
            deptId?: number; // int64
        }
        /**
         * 反馈类型列表
         */
        export interface FeedbackDetailTypeDO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            feedbackId?: number; // int64
            feedbackTypeId?: number; // int64
            feedbackType?: string;
            deptId?: number; // int64
        }
        /**
         * 文件信息
         */
        export interface FeedbackFileDO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            businessId?: number; // int64
            name?: string;
            address?: string;
            suffix?: string;
            type?: string;
            deptId?: number; // int64
        }
        export interface FeedbackTypeDO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: string;
            typeName?: string;
            remark?: string;
            status?: string;
            deptId?: number; // int64
        }
        /**
         * 数据
         */
        export interface FeedbackVO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            userId?: number; // int64
            feedbackTime?: string; // date-time
            waybillNumber?: string;
            feedbackContent?: string;
            dealContent?: string;
            dealStatus?: string;
            dealUserId?: number; // int64
            deptId?: number; // int64
            /**
             * 客户名称
             */
            name?: string;
            /**
             * 处理人名称
             */
            processorsName?: string;
            /**
             * 反馈类型
             */
            feedbackType?: string;
            /**
             * 文件信息
             */
            files?: /* 文件信息 */ FeedbackFileDO[];
        }
        export interface FileDO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            businessId?: number; // int64
            name?: string;
            address?: string;
            suffix?: string;
            type?: string;
            deptId?: number; // int64
        }
        /**
         * 数据
         */
        export interface FixedPointRouteDO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            routeName?: string;
            remark?: string;
            status?: string;
            deptId?: number; // int64
        }
        /**
         * 定点路线详情列表
         */
        export interface FixedPointRouteDetailDO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            routeId?: number; // int64
            province?: string;
            city?: string;
            county?: string;
            address?: string;
            lng?: string;
            lat?: string;
            seq?: number; // int32
            deptId?: number; // int64
        }
        export interface GetStaffCashSettlementVO {
            /**
             * 其他费用
             */
            staffVos?: /* 结算信息详情 */ StaffCashSettlementDetailDO[];
            settlement?: /* 结算信息 */ StaffCashSettlementDO;
        }
        /**
         * 货物统计
         */
        export interface GoodCountVO {
            /**
             * 货物总重（吨）
             */
            goodWeight?: number;
            /**
             * 货物体积（方）
             */
            goodVolume?: number;
            /**
             * 货物类型
             */
            goodType?: number; // int64
            /**
             * 运单金额(万元)
             */
            transportOrderMoney?: number;
        }
        export interface GoodsTypeDO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: string;
            name?: string;
            expenseId?: number; // int64
            remark?: string;
            status?: string;
            deptId?: number; // int64
        }
        /**
         * 导航页统计
         */
        export interface HomeCountVO {
            /**
             * 交易额(万元)
             */
            transportOrderMoney?: number;
            /**
             * 订单数量
             */
            orderNum?: number; // int64
            /**
             * 运单数量
             */
            transportNum?: number; // int64
            /**
             * 未接订单数量
             */
            waitOrderNum?: number; // int64
            /**
             * 配送中订单数量
             */
            deliveryOrderNum?: number; // int64
            /**
             * 异常订单数量
             */
            errorOrderNum?: number; // int64
            /**
             * 完成订单数量
             */
            finishOrderNum?: number; // int64
            /**
             * 接单率
             */
            orderRatio?: number;
            /**
             * 运单完成率
             */
            transportRatio?: number;
        }
        /**
         * 数据
         */
        export interface LoadingStrategyConfigDO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            name?: string;
            remark?: string;
            includeGoods?: string;
            excludeGoods?: string;
            status?: string;
            deptId?: number; // int64
        }
        /**
         * 数据
         */
        export interface MaintenanceShopDO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            shopName?: string;
            contacts?: string;
            contactsPhone?: string;
            contactsAddress?: string;
            remark?: string;
            status?: string;
            deptId?: number; // int64
        }
        export interface PackingTypeDO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: string;
            name?: string;
            remark?: string;
            status?: string;
            deptId?: number; // int64
        }
        /**
         * 分页结果
         */
        export interface PageResultAccidentFaultDamageReportVO {
            /**
             * 数据
             */
            list: /* 数据 */ AccidentFaultDamageReportVO[];
            /**
             * 总量
             */
            total: number; // int64
        }
        /**
         * 分页结果
         */
        export interface PageResultAssetStatusVO {
            /**
             * 数据
             */
            list: /* 数据 */ AssetStatusVO[];
            /**
             * 总量
             */
            total: number; // int64
        }
        /**
         * 分页结果
         */
        export interface PageResultAssetsSaleDO {
            /**
             * 数据
             */
            list: /* 数据 */ AssetsSaleDO[];
            /**
             * 总量
             */
            total: number; // int64
        }
        /**
         * 分页结果
         */
        export interface PageResultAssetsSaleDetailVO {
            /**
             * 数据
             */
            list: /* 数据 */ AssetsSaleDetailVO[];
            /**
             * 总量
             */
            total: number; // int64
        }
        /**
         * 分页结果
         */
        export interface PageResultAssetsSalePlanDO {
            /**
             * 数据
             */
            list: /* 数据 */ AssetsSalePlanDO[];
            /**
             * 总量
             */
            total: number; // int64
        }
        /**
         * 分页结果
         */
        export interface PageResultAssetsVo {
            /**
             * 数据
             */
            list: /* AssetsVo */ AssetsVo[];
            /**
             * 总量
             */
            total: number; // int64
        }
        /**
         * 分页结果
         */
        export interface PageResultBusinessRuleDO {
            /**
             * 数据
             */
            list: /* 数据 */ BusinessRuleDO[];
            /**
             * 总量
             */
            total: number; // int64
        }
        /**
         * 分页结果
         */
        export interface PageResultCarInsureDO {
            /**
             * 数据
             */
            list: /* 数据 */ CarInsureDO[];
            /**
             * 总量
             */
            total: number; // int64
        }
        /**
         * 分页结果
         */
        export interface PageResultCarLengthDO {
            /**
             * 数据
             */
            list: /* 数据 */ CarLengthDO[];
            /**
             * 总量
             */
            total: number; // int64
        }
        /**
         * 分页结果
         */
        export interface PageResultCarModelDO {
            /**
             * 数据
             */
            list: /* 数据 */ CarModelDO[];
            /**
             * 总量
             */
            total: number; // int64
        }
        /**
         * 分页结果
         */
        export interface PageResultCarRepairFormDO {
            /**
             * 数据
             */
            list: /* 数据 */ CarRepairFormDO[];
            /**
             * 总量
             */
            total: number; // int64
        }
        /**
         * 分页结果
         */
        export interface PageResultCarRouteVO {
            /**
             * 数据
             */
            list: /* 数据 */ CarRouteVO[];
            /**
             * 总量
             */
            total: number; // int64
        }
        /**
         * 分页结果
         */
        export interface PageResultCarVO {
            /**
             * 数据
             */
            list: /* 数据 */ CarVO[];
            /**
             * 总量
             */
            total: number; // int64
        }
        /**
         * 分页结果
         */
        export interface PageResultCarrierDO {
            /**
             * 数据
             */
            list: /* 数据 */ CarrierDO[];
            /**
             * 总量
             */
            total: number; // int64
        }
        /**
         * 分页结果
         */
        export interface PageResultCustomerVO {
            /**
             * 数据
             */
            list: /* 数据 */ CustomerVO[];
            /**
             * 总量
             */
            total: number; // int64
        }
        /**
         * 分页结果
         */
        export interface PageResultDriverSignVo {
            /**
             * 数据
             */
            list: DriverSignVo[];
            /**
             * 总量
             */
            total: number; // int64
        }
        /**
         * 分页结果
         */
        export interface PageResultDriverVO {
            /**
             * 数据
             */
            list: /* 数据 */ DriverVO[];
            /**
             * 总量
             */
            total: number; // int64
        }
        /**
         * 分页结果
         */
        export interface PageResultExpenseManagementVo {
            /**
             * 数据
             */
            list: /* 数据 */ ExpenseManagementVo[];
            /**
             * 总量
             */
            total: number; // int64
        }
        /**
         * 分页结果
         */
        export interface PageResultExpenseTimeDO {
            /**
             * 数据
             */
            list: /* 数据 */ ExpenseTimeDO[];
            /**
             * 总量
             */
            total: number; // int64
        }
        /**
         * 分页结果
         */
        export interface PageResultFactoryManageDO {
            /**
             * 数据
             */
            list: /* 数据 */ FactoryManageDO[];
            /**
             * 总量
             */
            total: number; // int64
        }
        /**
         * 分页结果
         */
        export interface PageResultFeedbackVO {
            /**
             * 数据
             */
            list: /* 数据 */ FeedbackVO[];
            /**
             * 总量
             */
            total: number; // int64
        }
        /**
         * 分页结果
         */
        export interface PageResultFixedPointRouteDO {
            /**
             * 数据
             */
            list: /* 数据 */ FixedPointRouteDO[];
            /**
             * 总量
             */
            total: number; // int64
        }
        /**
         * 分页结果
         */
        export interface PageResultLoadingStrategyConfigDO {
            /**
             * 数据
             */
            list: /* 数据 */ LoadingStrategyConfigDO[];
            /**
             * 总量
             */
            total: number; // int64
        }
        /**
         * 分页结果
         */
        export interface PageResultMaintenanceShopDO {
            /**
             * 数据
             */
            list: /* 数据 */ MaintenanceShopDO[];
            /**
             * 总量
             */
            total: number; // int64
        }
        /**
         * 分页结果
         */
        export interface PageResultStaffCashSettlementVO {
            /**
             * 数据
             */
            list: /* 数据 */ StaffCashSettlementVO[];
            /**
             * 总量
             */
            total: number; // int64
        }
        /**
         * 分页结果
         */
        export interface PageResultStaffManageVo {
            /**
             * 数据
             */
            list: /* 员工管理staffManage */ StaffManageVo[];
            /**
             * 总量
             */
            total: number; // int64
        }
        /**
         * 分页结果
         */
        export interface PageResultTempRangConfigVO {
            /**
             * 数据
             */
            list: /* 数据 */ TempRangConfigVO[];
            /**
             * 总量
             */
            total: number; // int64
        }
        /**
         * 分页结果
         */
        export interface PageResultTransportOrderErrorRecordDO {
            /**
             * 数据
             */
            list: /* 数据 */ TransportOrderErrorRecordDO[];
            /**
             * 总量
             */
            total: number; // int64
        }
        /**
         * 分页结果
         */
        export interface PageResultTransportOrderVO {
            /**
             * 数据
             */
            list: /**
             * 订单详情
             * 数据
             */
            TransportOrderVO[];
            /**
             * 总量
             */
            total: number; // int64
        }
        /**
         * 分页结果
         */
        export interface PageResultTransportOrderWarnRecordDO {
            /**
             * 数据
             */
            list: /* 数据 */ TransportOrderWarnRecordDO[];
            /**
             * 总量
             */
            total: number; // int64
        }
        /**
         * 分页结果
         */
        export interface PageResultTransportSchemeConfigDO {
            /**
             * 数据
             */
            list: /* 数据 */ TransportSchemeConfigDO[];
            /**
             * 总量
             */
            total: number; // int64
        }
        /**
         * 分页结果
         */
        export interface PageResultTransportVO {
            /**
             * 数据
             */
            list: /* 数据 */ TransportVO[];
            /**
             * 总量
             */
            total: number; // int64
        }
        /**
         * 分页结果
         */
        export interface PageResultWarnConfigDO {
            /**
             * 数据
             */
            list: /* 数据 */ WarnConfigDO[];
            /**
             * 总量
             */
            total: number; // int64
        }
        /**
         * 收货地址列表
         */
        export interface ReceiverAddressDO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            orderId?: number; // int64
            province?: string;
            city?: string;
            county?: string;
            address?: string;
            lng?: string;
            lat?: string;
            receiverName?: string;
            receiverPhone?: string;
            deptId?: number; // int64
        }
        /**
         * 卸货地址DTO
         */
        export interface ReceiverAddressDTO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            orderId?: number; // int64
            province?: string;
            city?: string;
            county?: string;
            address?: string;
            lng?: string;
            lat?: string;
            receiverName?: string;
            receiverPhone?: string;
            deptId?: number; // int64
            /**
             * 货物信息
             */
            goodsList: /* 货物信息 */ ReceiverGoodsDO[];
        }
        /**
         * 卸货地址Vo
         */
        export interface ReceiverAddressVO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            orderId?: number; // int64
            province?: string;
            city?: string;
            county?: string;
            address?: string;
            lng?: string;
            lat?: string;
            receiverName?: string;
            receiverPhone?: string;
            deptId?: number; // int64
            /**
             * 货物信息
             */
            goodsList?: /* 货物信息 */ ReceiverGoodsDO[];
        }
        /**
         * 货物信息
         */
        export interface ReceiverGoodsDO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            orderId?: number; // int64
            addressId?: number; // int64
            spuName?: string;
            packingId?: number; // int64
            packingName?: string;
            goodsTypeId?: number; // int64
            goodsTypeName?: string;
            itemWeight?: string;
            number?: string;
            weight?: string;
            volume?: string;
            deptId?: number; // int64
        }
        export interface RouteVO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            routeName?: string;
            remark?: string;
            status?: string;
            deptId?: number; // int64
            /**
             * 定点路线详情列表
             */
            list?: /* 定点路线详情列表 */ FixedPointRouteDetailDO[];
        }
        /**
         * 结算信息
         */
        export interface StaffCashSettlementDO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            orderId?: number; // int64
            totalMoney?: number;
            settleType?: string;
            settleStatus?: string;
            deptId?: number; // int64
        }
        /**
         * 结算信息详情
         */
        export interface StaffCashSettlementDetailDO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            orderId?: number; // int64
            settleId?: number; // int64
            staffId?: number; // int64
            staffName?: string;
            expenseName?: string;
            expenseRemark?: string;
            expenseMoney?: number;
            deptId?: number; // int64
        }
        /**
         * 数据
         */
        export interface StaffCashSettlementVO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            orderId?: number; // int64
            totalMoney?: number;
            settleType?: string;
            settleStatus?: string;
            deptId?: number; // int64
            /**
             * 运单号
             */
            transportNumber?: string;
            /**
             * 客户名称
             */
            customerName?: string;
        }
        export interface StaffManageDO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: string;
            name?: string;
            gender?: string;
            contactsPhone?: string;
            orgId?: number; // int64
            deptId?: number; // int64
        }
        /**
         * 员工管理staffManage
         */
        export interface StaffManageVo {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: string;
            name?: string;
            gender?: string;
            contactsPhone?: string;
            orgId?: number; // int64
            deptId?: number; // int64
            /**
             * 所属部门
             */
            orgName?: string;
        }
        export interface SystemConfigDO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            name?: string;
            homeLogo?: string;
            logo?: string;
            icon?: string;
            accountType?: string;
            bankName?: string;
            account?: string;
            collectFrequency?: number; // int32
            sendFrequency?: number; // int32
            openColdHot?: string;
            cancelLimitTime?: number; // int32
            deptId?: number; // int64
        }
        export interface SystemImgVO {
            /**
             * 企业名称
             */
            name?: string;
            /**
             * 企业LOGO
             */
            logo?: string;
            /**
             * 网站ICON
             */
            icon?: string;
            /**
             * 首页LOGO
             */
            homeLogo?: string;
            /**
             * 打印LOGO
             */
            printLogo?: string;
        }
        /**
         * 温区配置详情
         */
        export interface TempRangConfigDetailDO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            configId?: number; // int64
            name?: string;
            tempSet?: string;
            humSet?: string;
            deptId?: number; // int64
        }
        /**
         * 管理后台 - 温区配置详情
         */
        export interface TempRangConfigDetailDOVO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            name?: string;
            carModelId?: number; // int64
            carLengthId?: number; // int64
            carCategory?: string;
            remark?: string;
            status?: string;
            deptId?: number; // int64
            /**
             * 温区配置详情
             */
            detailList?: /* 温区配置详情 */ TempRangConfigDetailDO[];
        }
        /**
         * 数据
         */
        export interface TempRangConfigVO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            name?: string;
            carModelId?: number; // int64
            carLengthId?: number; // int64
            carCategory?: string;
            remark?: string;
            status?: string;
            deptId?: number; // int64
            /**
             * 温区配置详情
             */
            detailList?: /* 温区配置详情 */ TempRangConfigDetailDO[];
            /**
             * 车型名称
             */
            carModelName?: string;
            /**
             * 车长名称
             */
            carLengthName?: string;
            /**
             * 温区数
             */
            num?: number; // int64
        }
        /**
         * 营业额统计
         */
        export interface TradeCountVO {
            /**
             * 运单金额(万元)
             */
            transportOrderMoney?: number;
            /**
             * 待支付金额(万元)
             */
            waitPayMoney?: number;
            /**
             * 已支付金额(万元)
             */
            payedMoney?: number;
            /**
             * 货损金额(万元)
             */
            damageMoney?: number;
        }
        /**
         * 订单设备及电量
         */
        export interface TransportOrderAssertsVO {
            /**
             * 资产ID（设备ID）
             */
            assetId?: string;
            /**
             * 电池电量
             */
            batteryLevel?: number; // int32
        }
        /**
         * 运单中途抽检transportOrderCheckVo
         */
        export interface TransportOrderCheckVO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            orderId?: number; // int64
            remark?: string;
            deptId?: number; // int64
            /**
             * 抽检名称
             */
            name?: string;
            fileList?: /* 文件列表 */ TransportOrderFileDO[];
        }
        /**
         * 订单详情TransportOrderChildVO
         */
        export interface TransportOrderChildVO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            parentId?: number; // int64
            transportNumber?: string;
            orderNumber?: string;
            customerId?: string;
            customerName?: string;
            loadingTime?: string; // date-time
            sendProvince?: string;
            sendCity?: string;
            sendCounty?: string;
            sendAddress?: string;
            sendName?: string;
            sendPhone?: string;
            sendLng?: string;
            sendLat?: string;
            transportRequire?: string;
            carModelId?: number; // int64
            carModelName?: string;
            carLengthId?: number; // int64
            carLength?: string;
            transportTemp?: string;
            transportHum?: string;
            transportType?: string;
            billMethod?: string;
            totalMoney?: number;
            reductionMoney?: number;
            payType?: string;
            payStatus?: string;
            otherNeeds?: string;
            remark?: string;
            transportCarDriverId?: string;
            transportCarNumber?: string;
            transportDriverName?: string;
            transportDriverPhone?: string;
            transportCarModelId?: number; // int64
            transportCarModelName?: string;
            transportCarLengthId?: number; // int64
            transportCarLength?: string;
            transportCarType?: string;
            transportCarCategory?: string;
            auditStatus?: string;
            orderStatus?: string;
            transportStatus?: string;
            customerLoadStatus?: string;
            driverLoadStatus?: string;
            customerUnloadStatus?: string;
            driverUnloadStatus?: string;
            mergeStrategy?: string;
            mergeOrderGenerate?: string;
            splitOrderGenerate?: string;
            mergeOrder?: string;
            splitOrder?: string;
            errorOrder?: string;
            orderTime?: string; // date-time
            transportTime?: string; // date-time
            acceptTime?: string; // date-time
            loadTime?: string; // date-time
            unloadTime?: string; // date-time
            finishTime?: string; // date-time
            payTime?: string; // date-time
            driverLoadTime?: string; // date-time
            customerLoadTime?: string; // date-time
            driverUnloadTime?: string; // date-time
            customerUnloadTime?: string; // date-time
            loadRemark?: string;
            unloadRemark?: string;
            settleStatus?: string;
            deptId?: number; // int64
            /**
             * 卸货地址列表
             */
            addressList?: /* 收货地址列表 */ ReceiverAddressDO[];
        }
        /**
         * 数据
         */
        export interface TransportOrderErrorRecordDO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            orderId?: number; // int64
            carNumber?: string;
            assetsId?: string;
            customerId?: number; // int64
            customerName?: string;
            transportDriverName?: string;
            transportDriverPhone?: string;
            recordStartTime?: string; // date-time
            recordEndTime?: string; // date-time
            errorType?: string;
            deptId?: number; // int64
        }
        /**
         * 文件列表
         */
        export interface TransportOrderFileDO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            businessId?: number; // int64
            name?: string;
            address?: string;
            suffix?: string;
            type?: string;
            deptId?: number; // int64
        }
        /**
         * 订单能合单的详情
         */
        export interface TransportOrderMergeVO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            parentId?: number; // int64
            transportNumber?: string;
            orderNumber?: string;
            customerId?: string;
            customerName?: string;
            loadingTime?: string; // date-time
            sendProvince?: string;
            sendCity?: string;
            sendCounty?: string;
            sendAddress?: string;
            sendName?: string;
            sendPhone?: string;
            sendLng?: string;
            sendLat?: string;
            transportRequire?: string;
            carModelId?: number; // int64
            carModelName?: string;
            carLengthId?: number; // int64
            carLength?: string;
            transportTemp?: string;
            transportHum?: string;
            transportType?: string;
            billMethod?: string;
            totalMoney?: number;
            reductionMoney?: number;
            payType?: string;
            payStatus?: string;
            otherNeeds?: string;
            remark?: string;
            transportCarDriverId?: string;
            transportCarNumber?: string;
            transportDriverName?: string;
            transportDriverPhone?: string;
            transportCarModelId?: number; // int64
            transportCarModelName?: string;
            transportCarLengthId?: number; // int64
            transportCarLength?: string;
            transportCarType?: string;
            transportCarCategory?: string;
            auditStatus?: string;
            orderStatus?: string;
            transportStatus?: string;
            customerLoadStatus?: string;
            driverLoadStatus?: string;
            customerUnloadStatus?: string;
            driverUnloadStatus?: string;
            mergeStrategy?: string;
            mergeOrderGenerate?: string;
            splitOrderGenerate?: string;
            mergeOrder?: string;
            splitOrder?: string;
            errorOrder?: string;
            orderTime?: string; // date-time
            transportTime?: string; // date-time
            acceptTime?: string; // date-time
            loadTime?: string; // date-time
            unloadTime?: string; // date-time
            finishTime?: string; // date-time
            payTime?: string; // date-time
            driverLoadTime?: string; // date-time
            customerLoadTime?: string; // date-time
            driverUnloadTime?: string; // date-time
            customerUnloadTime?: string; // date-time
            loadRemark?: string;
            unloadRemark?: string;
            settleStatus?: string;
            deptId?: number; // int64
            /**
             * 件数
             */
            number?: string;
            /**
             * 重量(吨)
             */
            weight?: string;
            /**
             * 收货地址列表
             */
            receiverAddressList?: /* 收货地址列表 */ ReceiverAddressDO[];
        }
        /**
         * 拆分订单卸货地址详情DTO
         */
        export interface TransportOrderSplitAddressDTO {
            /**
             * 卸货地址ID
             */
            id: number; // int64
            /**
             * 卸货商品信息
             */
            goodsList: /* 拆分订单卸货地址商品信息DTO */ TransportOrderSplitGoodsDTO[];
        }
        /**
         * 拆分订单DTO
         */
        export interface TransportOrderSplitDTO {
            /**
             * 子订单运费
             */
            totalMoney: number;
            /**
             * 车辆号码
             */
            carNumber?: string;
            /**
             * 驾驶员ID
             */
            driverId?: string;
            /**
             * 运输要求车型ID
             */
            carModelId?: number; // int64
            /**
             * 运输要求车型名称
             */
            carModelName?: string;
            /**
             * 运输要求车长ID
             */
            carLengthId?: number; // int64
            /**
             * 运输要求车长
             */
            carLength?: string;
            /**
             * 卸货地址信息
             */
            addressList: /* 拆分订单卸货地址详情DTO */ TransportOrderSplitAddressDTO[];
        }
        /**
         * 拆分订单卸货地址商品信息DTO
         */
        export interface TransportOrderSplitGoodsDTO {
            /**
             * 商品id
             */
            id: number; // int64
            /**
             * 件数
             */
            number: string;
        }
        /**
         * 订单详情
         * 数据
         */
        export interface TransportOrderVO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            parentId?: number; // int64
            transportNumber?: string;
            orderNumber?: string;
            customerId?: string;
            customerName?: string;
            loadingTime?: string; // date-time
            sendProvince?: string;
            sendCity?: string;
            sendCounty?: string;
            sendAddress?: string;
            sendName?: string;
            sendPhone?: string;
            sendLng?: string;
            sendLat?: string;
            transportRequire?: string;
            carModelId?: number; // int64
            carModelName?: string;
            carLengthId?: number; // int64
            carLength?: string;
            transportTemp?: string;
            transportHum?: string;
            transportType?: string;
            billMethod?: string;
            totalMoney?: number;
            reductionMoney?: number;
            payType?: string;
            payStatus?: string;
            otherNeeds?: string;
            remark?: string;
            transportCarDriverId?: string;
            transportCarNumber?: string;
            transportDriverName?: string;
            transportDriverPhone?: string;
            transportCarModelId?: number; // int64
            transportCarModelName?: string;
            transportCarLengthId?: number; // int64
            transportCarLength?: string;
            transportCarType?: string;
            transportCarCategory?: string;
            auditStatus?: string;
            orderStatus?: string;
            transportStatus?: string;
            customerLoadStatus?: string;
            driverLoadStatus?: string;
            customerUnloadStatus?: string;
            driverUnloadStatus?: string;
            mergeStrategy?: string;
            mergeOrderGenerate?: string;
            splitOrderGenerate?: string;
            mergeOrder?: string;
            splitOrder?: string;
            errorOrder?: string;
            orderTime?: string; // date-time
            transportTime?: string; // date-time
            acceptTime?: string; // date-time
            loadTime?: string; // date-time
            unloadTime?: string; // date-time
            finishTime?: string; // date-time
            payTime?: string; // date-time
            driverLoadTime?: string; // date-time
            customerLoadTime?: string; // date-time
            driverUnloadTime?: string; // date-time
            customerUnloadTime?: string; // date-time
            loadRemark?: string;
            unloadRemark?: string;
            settleStatus?: string;
            deptId?: number; // int64
            /**
             * 卸货地址列表
             */
            addressList?: /* 卸货地址Vo */ ReceiverAddressVO[];
            /**
             * 是否关联了设备
             */
            isRelationAssets?: "y" | "n";
            /**
             * 资产ID
             */
            assetsId?: string;
            /**
             * 文件列表
             */
            fileList?: /* 文件列表 */ TransportOrderFileDO[];
        }
        /**
         * 数据
         */
        export interface TransportOrderWarnRecordDO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            orderId?: number; // int64
            warnLevel?: string;
            carNumber?: string;
            assetsId?: string;
            customerId?: number; // int64
            customerName?: string;
            transportDriverName?: string;
            transportDriverPhone?: string;
            recordStartTime?: string; // date-time
            recordEndTime?: string; // date-time
            warnType?: string;
            deptId?: number; // int64
        }
        /**
         * 数据
         */
        export interface TransportSchemeConfigDO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            name?: string;
            seasonsType?: string;
            pointRouteId?: number; // int64
            goodsTypeId?: number; // int64
            tempSet?: string;
            humSet?: string;
            remark?: string;
            status?: string;
            deptId?: number; // int64
        }
        /**
         * 数据
         */
        export interface TransportVO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            parentId?: number; // int64
            transportNumber?: string;
            orderNumber?: string;
            customerId?: string;
            customerName?: string;
            loadingTime?: string; // date-time
            sendProvince?: string;
            sendCity?: string;
            sendCounty?: string;
            sendAddress?: string;
            sendName?: string;
            sendPhone?: string;
            sendLng?: string;
            sendLat?: string;
            transportRequire?: string;
            carModelId?: number; // int64
            carModelName?: string;
            carLengthId?: number; // int64
            carLength?: string;
            transportTemp?: string;
            transportHum?: string;
            transportType?: string;
            billMethod?: string;
            totalMoney?: number;
            reductionMoney?: number;
            payType?: string;
            payStatus?: string;
            otherNeeds?: string;
            remark?: string;
            transportCarDriverId?: string;
            transportCarNumber?: string;
            transportDriverName?: string;
            transportDriverPhone?: string;
            transportCarModelId?: number; // int64
            transportCarModelName?: string;
            transportCarLengthId?: number; // int64
            transportCarLength?: string;
            transportCarType?: string;
            transportCarCategory?: string;
            auditStatus?: string;
            orderStatus?: string;
            transportStatus?: string;
            customerLoadStatus?: string;
            driverLoadStatus?: string;
            customerUnloadStatus?: string;
            driverUnloadStatus?: string;
            mergeStrategy?: string;
            mergeOrderGenerate?: string;
            splitOrderGenerate?: string;
            mergeOrder?: string;
            splitOrder?: string;
            errorOrder?: string;
            orderTime?: string; // date-time
            transportTime?: string; // date-time
            acceptTime?: string; // date-time
            loadTime?: string; // date-time
            unloadTime?: string; // date-time
            finishTime?: string; // date-time
            payTime?: string; // date-time
            driverLoadTime?: string; // date-time
            customerLoadTime?: string; // date-time
            driverUnloadTime?: string; // date-time
            customerUnloadTime?: string; // date-time
            loadRemark?: string;
            unloadRemark?: string;
            settleStatus?: string;
            deptId?: number; // int64
            /**
             * 客户身份:企业:company;个人:personal;
             */
            identityType?: string;
            /**
             * 运力类型：个人：person；自营：self；承运商：carrier；
             */
            capacityType?: string;
            /**
             * 承运商名称
             */
            carrierName?: string;
        }
        /**
         * 数据
         */
        export interface WarnConfigDO {
            createTime?: string; // date-time
            updateTime?: string; // date-time
            creator?: string;
            updater?: string;
            deleted?: boolean;
            id?: number; // int64
            name?: string;
            tempUpValue?: string;
            tempDownValue?: string;
            humUpValue?: string;
            humDownValue?: string;
            duration?: number; // int32
            warnLevel?: string;
            status?: string;
            deptId?: number; // int64
        }
    }
}
declare namespace ServiceTypePathsCcms {
    namespace AcceptOrder {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 车辆ID
             */
            export type CarId = string;
            /**
             * 订单ID
             */
            export type OrderId = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            orderId: /* 订单ID */ Parameters.OrderId /* int64 */;
            carId: /* 车辆ID */ Parameters.CarId;
        }
    }
    namespace AddFeedback {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 反馈内容
             */
            export type FeedbackContent = string;
            /**
             * 反馈文件列表
             */
            export type FileList = /* 文件信息 */ ServiceTypeCcms.Schemas.FeedbackFileDO[];
            /**
             * 订单ID
             */
            export type OrderId = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            /**
             * 反馈类型列表
             */
            export type TypeList = /* 反馈类型列表 */ ServiceTypeCcms.Schemas.FeedbackDetailTypeDO[];
        }
        export interface QueryParameters {
            orderId?: /* 订单ID */ Parameters.OrderId /* int64 */;
            feedbackContent?: /* 反馈内容 */ Parameters.FeedbackContent;
            typeList?: /* 反馈类型列表 */ Parameters.TypeList;
            fileList?: /* 反馈文件列表 */ Parameters.FileList;
        }
    }
    namespace AppointmentBooking {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type DriverId = string;
            export type OrderId = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            orderId: Parameters.OrderId /* int64 */;
            driverId: Parameters.DriverId;
        }
    }
    namespace AssetWarehousing {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 入库资产ID列表
             */
            export type AssetsIdsList = string[];
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            assetsIdsList?: /* 入库资产ID列表 */ Parameters.AssetsIdsList;
        }
    }
    namespace BindCar {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type CarId = string;
            export type DriverId = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            driverId: Parameters.DriverId;
            carId: Parameters.CarId;
        }
    }
    namespace BindDriver {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type CarId = string;
            export type DriverId = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            carId: Parameters.CarId;
            driverId: Parameters.DriverId;
        }
    }
    namespace CancelMergeOrder {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type OrderId = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            orderId: Parameters.OrderId /* int64 */;
        }
    }
    namespace CancelSpiltOrder {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type OrderId = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            orderId: Parameters.OrderId /* int64 */;
        }
    }
    namespace CancelTransportOrder {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type OrderId = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            orderId: Parameters.OrderId /* int64 */;
        }
    }
    namespace CarDispatch {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 车辆号码
             */
            export type CarNumber = string;
            /**
             * 驾驶员ID
             */
            export type DriverId = string;
            /**
             * 订单ID
             */
            export type OrderId = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            orderId: /* 订单ID */ Parameters.OrderId /* int64 */;
            carNumber: /* 车辆号码 */ Parameters.CarNumber;
            driverId: /* 驾驶员ID */ Parameters.DriverId;
        }
    }
    namespace ChangeReceiverAddress {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 地址
             */
            export type Address = string;
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 市
             */
            export type City = string;
            /**
             * 区县
             */
            export type County = string;
            /**
             * ID
             */
            export type Id = number; // int64
            /**
             * 省
             */
            export type Province = string;
            /**
             * 收货人
             */
            export type ReceiverName = string;
            /**
             * 收货人手机号
             */
            export type ReceiverPhone = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id?: /* ID */ Parameters.Id /* int64 */;
            province: /* 省 */ Parameters.Province;
            city: /* 市 */ Parameters.City;
            county: /* 区县 */ Parameters.County;
            address: /* 地址 */ Parameters.Address;
            receiverName?: /* 收货人 */ Parameters.ReceiverName;
            receiverPhone?: /* 收货人手机号 */ Parameters.ReceiverPhone;
        }
    }
    namespace ChangeSendAddress {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 订单id
             */
            export type OrderId = number; // int64
            /**
             * 发货地址
             */
            export type SendAddress = string;
            /**
             * 发货市
             */
            export type SendCity = string;
            /**
             * 发货区县
             */
            export type SendCounty = string;
            /**
             * 发货人
             */
            export type SendName = string;
            /**
             * 发货人手机号
             */
            export type SendPhone = string;
            /**
             * 发货省
             */
            export type SendProvince = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            orderId: /* 订单id */ Parameters.OrderId /* int64 */;
            sendProvince: /* 发货省 */ Parameters.SendProvince;
            sendCity: /* 发货市 */ Parameters.SendCity;
            sendCounty: /* 发货区县 */ Parameters.SendCounty;
            sendAddress: /* 发货地址 */ Parameters.SendAddress;
            sendName?: /* 发货人 */ Parameters.SendName;
            sendPhone?: /* 发货人手机号 */ Parameters.SendPhone;
        }
    }
    namespace CheckTransportOrder {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 文件列表
             */
            export type FileList = /* 文件列表 */ ServiceTypeCcms.Schemas.TransportOrderFileDO[];
            /**
             * 订单ID
             */
            export type OrderId = number; // int64
            /**
             * 抽检描述
             */
            export type Remark = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            orderId: /* 订单ID */ Parameters.OrderId /* int64 */;
            remark?: /* 抽检描述 */ Parameters.Remark;
            fileList: /* 文件列表 */ Parameters.FileList;
        }
    }
    namespace CountAcceptTimeListCountHome {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type EndTime = string; // date-time
            export type StartTime = string; // date-time
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            startTime: Parameters.StartTime /* date-time */;
            endTime: Parameters.EndTime /* date-time */;
        }
    }
    namespace CountAssetsRecord {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type OrderId = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            orderId: Parameters.OrderId /* int64 */;
        }
    }
    namespace CountAvgAcceptCountHome {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
    }
    namespace CountBase {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 结束时间
             */
            export type EndTime = string; // date-time
            /**
             * 开始时间
             */
            export type StartTime = string; // date-time
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            startTime?: /* 开始时间 */ Parameters.StartTime /* date-time */;
            endTime?: /* 结束时间 */ Parameters.EndTime /* date-time */;
        }
    }
    namespace CountBaseCtms {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 结束时间
             */
            export type EndTime = string; // date-time
            /**
             * 开始时间
             */
            export type StartTime = string; // date-time
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            startTime?: /* 开始时间 */ Parameters.StartTime /* date-time */;
            endTime?: /* 结束时间 */ Parameters.EndTime /* date-time */;
        }
    }
    namespace CountCarErrorMonitorTop10 {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 结束时间
             */
            export type EndTime = string; // date-time
            /**
             * 开始时间
             */
            export type StartTime = string; // date-time
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            startTime?: /* 开始时间 */ Parameters.StartTime /* date-time */;
            endTime?: /* 结束时间 */ Parameters.EndTime /* date-time */;
        }
    }
    namespace CountCarTypeRatioCountDriver {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
    }
    namespace CountCarbonEmission {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 结束时间
             */
            export type EndTime = string; // date-time
            /**
             * 开始时间
             */
            export type StartTime = string; // date-time
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            startTime?: /* 开始时间 */ Parameters.StartTime /* date-time */;
            endTime?: /* 结束时间 */ Parameters.EndTime /* date-time */;
        }
    }
    namespace CountCargoDamage {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 结束时间
             */
            export type EndTime = string; // date-time
            /**
             * 开始时间
             */
            export type StartTime = string; // date-time
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            startTime?: /* 开始时间 */ Parameters.StartTime /* date-time */;
            endTime?: /* 结束时间 */ Parameters.EndTime /* date-time */;
        }
    }
    namespace CountColdCar {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 结束时间
             */
            export type EndTime = string; // date-time
            /**
             * 开始时间
             */
            export type StartTime = string; // date-time
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            startTime?: /* 开始时间 */ Parameters.StartTime /* date-time */;
            endTime?: /* 结束时间 */ Parameters.EndTime /* date-time */;
        }
    }
    namespace CountColdCarCtms {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 结束时间
             */
            export type EndTime = string; // date-time
            /**
             * 开始时间
             */
            export type StartTime = string; // date-time
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            startTime?: /* 开始时间 */ Parameters.StartTime /* date-time */;
            endTime?: /* 结束时间 */ Parameters.EndTime /* date-time */;
        }
    }
    namespace CountColdCountVo {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
    }
    namespace CountColdMonitorByTime {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 结束时间
             */
            export type EndTime = string; // date-time
            /**
             * 开始时间
             */
            export type StartTime = string; // date-time
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            export type TimeType = "day" | "weekend" | "month" | "year";
        }
        export interface QueryParameters {
            timeType?: Parameters.TimeType;
            startTime?: /* 开始时间 */ Parameters.StartTime /* date-time */;
            endTime?: /* 结束时间 */ Parameters.EndTime /* date-time */;
        }
    }
    namespace CountColdMonitorError {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
    }
    namespace CountCompletionRate {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
    }
    namespace CountCustomerAcceptWeightRankCountGood {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
    }
    namespace CountCustomerActivityCountCountHome {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            export type Type = "day" | "weekend" | "month" | "year";
        }
        export interface QueryParameters {
            type?: Parameters.Type;
        }
    }
    namespace CountCustomerCountVoCountCustomer {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            export type Type = "company" | "personal";
        }
        export interface QueryParameters {
            type: Parameters.Type;
        }
    }
    namespace CountCustomerDriverTop10 {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type OperateUserType = "customer" | "driver";
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            operateUserType: Parameters.OperateUserType;
        }
    }
    namespace CountCustomerGoodWeightRankCountGood {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
    }
    namespace CountCustomerMoneyCountTrade {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
    }
    namespace CountCustomerMonitorRankTop10 {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
    }
    namespace CountCustomerOrderCountCustomer {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            export type Type = "company" | "personal";
        }
        export interface QueryParameters {
            type: Parameters.Type;
        }
    }
    namespace CountCustomerRatioCountCustomer {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
    }
    namespace CountDamageMoneyByTimeCountCold {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 结束时间
             */
            export type EndTime = string; // date-time
            /**
             * 开始时间
             */
            export type StartTime = string; // date-time
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            export type TimeType = "day" | "weekend" | "month" | "year";
        }
        export interface QueryParameters {
            timeType?: Parameters.TimeType;
            startTime?: /* 开始时间 */ Parameters.StartTime /* date-time */;
            endTime?: /* 结束时间 */ Parameters.EndTime /* date-time */;
        }
    }
    namespace CountDamageMoneyByTimeCountTrade {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 结束时间
             */
            export type EndTime = string; // date-time
            /**
             * 开始时间
             */
            export type StartTime = string; // date-time
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            export type TimeType = "day" | "weekend" | "month" | "year";
        }
        export interface QueryParameters {
            timeType?: Parameters.TimeType;
            startTime?: /* 开始时间 */ Parameters.StartTime /* date-time */;
            endTime?: /* 结束时间 */ Parameters.EndTime /* date-time */;
        }
    }
    namespace CountDriverActivityCountCountHome {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            export type Type = "day" | "weekend" | "month" | "year";
        }
        export interface QueryParameters {
            type?: Parameters.Type;
        }
    }
    namespace CountDriverCountVoCountDriver {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
    }
    namespace CountDriverCustomerActivityCountHome {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            export type TimeType = "day" | "weekend" | "month" | "year";
            export type UserType = "customer" | "driver";
        }
        export interface QueryParameters {
            userType: Parameters.UserType;
            timeType: Parameters.TimeType;
        }
    }
    namespace CountDriverOrderCountDriver {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
    }
    namespace CountDriverTypeRatioCountDriver {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
    }
    namespace CountEnergyConsumption {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 结束时间
             */
            export type EndTime = string; // date-time
            /**
             * 开始时间
             */
            export type StartTime = string; // date-time
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            startTime?: /* 开始时间 */ Parameters.StartTime /* date-time */;
            endTime?: /* 结束时间 */ Parameters.EndTime /* date-time */;
        }
    }
    namespace CountFailureRate {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
    }
    namespace CountGoodCountVoCountGood {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
    }
    namespace CountGoodFaultLevelRatioCountGood {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
    }
    namespace CountGoodIncreaseCountTrade {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type EndTime = string; // date-time
            export type StartTime = string; // date-time
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            /**
             * 时间类型：日day、周weekend、月month、年year
             */
            export type TimeType = "day" | "weekend" | "month" | "year";
        }
        export interface QueryParameters {
            timeType?: /* 时间类型：日day、周weekend、月month、年year */ Parameters.TimeType;
            startTime?: Parameters.StartTime /* date-time */;
            endTime?: Parameters.EndTime /* date-time */;
        }
    }
    namespace CountGoodTypeNameRatioCountCustomer {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            export type Type = "company" | "personal";
        }
        export interface QueryParameters {
            type: Parameters.Type;
        }
    }
    namespace CountGoodTypeNameRatioCountGood {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
    }
    namespace CountGoodWeight {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 结束时间
             */
            export type EndTime = string; // date-time
            /**
             * 开始时间
             */
            export type StartTime = string; // date-time
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            startTime?: /* 开始时间 */ Parameters.StartTime /* date-time */;
            endTime?: /* 结束时间 */ Parameters.EndTime /* date-time */;
        }
    }
    namespace CountGoodWeightByTimeCountGood {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 结束时间
             */
            export type EndTime = string; // date-time
            /**
             * 开始时间
             */
            export type StartTime = string; // date-time
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            export type TimeType = "day" | "weekend" | "month" | "year";
        }
        export interface QueryParameters {
            timeType?: Parameters.TimeType;
            startTime?: /* 开始时间 */ Parameters.StartTime /* date-time */;
            endTime?: /* 结束时间 */ Parameters.EndTime /* date-time */;
        }
    }
    namespace CountHomeCountVoCountHome {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type EndTime = string; // date-time
            export type StartTime = string; // date-time
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            startTime: Parameters.StartTime /* date-time */;
            endTime: Parameters.EndTime /* date-time */;
        }
    }
    namespace CountHomeTotalCountVoCountHome {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
    }
    namespace CountIncrease {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type EndTime = string; // date-time
            export type StartTime = string; // date-time
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            /**
             * 时间类型：日day、周weekend、月month、年year
             */
            export type TimeType = "day" | "weekend" | "month" | "year";
        }
        export interface QueryParameters {
            timeType?: /* 时间类型：日day、周weekend、月month、年year */ Parameters.TimeType;
            startTime?: Parameters.StartTime /* date-time */;
            endTime?: Parameters.EndTime /* date-time */;
        }
    }
    namespace CountMapOrderFlow {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 结束市
             * example:
             * 100
             */
            export type City = string;
            /**
             * 结束区县
             * example:
             * 100
             */
            export type County = string;
            /**
             * 结束省
             * example:
             * 100
             */
            export type Province = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            /**
             * 运单编号
             * example:
             * 100
             */
            export type TransportNumber = string;
        }
        export interface QueryParameters {
            transportNumber: /**
             * 运单编号
             * example:
             * 100
             */
            Parameters.TransportNumber;
            province: /**
             * 结束省
             * example:
             * 100
             */
            Parameters.Province;
            city: /**
             * 结束市
             * example:
             * 100
             */
            Parameters.City;
            county?: /**
             * 结束区县
             * example:
             * 100
             */
            Parameters.County;
        }
    }
    namespace CountMapOrderList {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 卸货市
             * example:
             * 市
             */
            export type City = string;
            /**
             * 卸货区县
             * example:
             * 区县
             */
            export type County = string;
            /**
             * 卸货省
             * example:
             * 省
             */
            export type Province = string;
            /**
             * 发货市
             * example:
             * 市
             */
            export type SendCity = string;
            /**
             * 发货区县
             * example:
             * 区县
             */
            export type SendCounty = string;
            /**
             * 发货省
             * example:
             * 省
             */
            export type SendProvince = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            sendProvince: /**
             * 发货省
             * example:
             * 省
             */
            Parameters.SendProvince;
            sendCity: /**
             * 发货市
             * example:
             * 市
             */
            Parameters.SendCity;
            sendCounty: /**
             * 发货区县
             * example:
             * 区县
             */
            Parameters.SendCounty;
            province: /**
             * 卸货省
             * example:
             * 省
             */
            Parameters.Province;
            city: /**
             * 卸货市
             * example:
             * 市
             */
            Parameters.City;
            county: /**
             * 卸货区县
             * example:
             * 区县
             */
            Parameters.County;
        }
    }
    namespace CountMapOrderListCtms {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 发货市
             * example:
             * 市
             */
            export type SendCity = string;
            /**
             * 发货区县
             * example:
             * 区县
             */
            export type SendCounty = string;
            /**
             * 发货省
             * example:
             * 省
             */
            export type SendProvince = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            sendProvince: /**
             * 发货省
             * example:
             * 省
             */
            Parameters.SendProvince;
            sendCity: /**
             * 发货市
             * example:
             * 市
             */
            Parameters.SendCity;
            sendCounty: /**
             * 发货区县
             * example:
             * 区县
             */
            Parameters.SendCounty;
        }
    }
    namespace CountMapOrderTotal {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
    }
    namespace CountMapOrderTotalCtms {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
    }
    namespace CountMonthOrderCountCustomer {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            export type Type = "company" | "personal";
        }
        export interface QueryParameters {
            type: Parameters.Type;
        }
    }
    namespace CountOrderListCountHome {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            export type Type = "day" | "weekend" | "month" | "year";
        }
        export interface QueryParameters {
            type?: Parameters.Type;
        }
    }
    namespace CountOrderNumber {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 结束时间
             */
            export type EndTime = string; // date-time
            /**
             * 开始时间
             */
            export type StartTime = string; // date-time
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            startTime?: /* 开始时间 */ Parameters.StartTime /* date-time */;
            endTime?: /* 结束时间 */ Parameters.EndTime /* date-time */;
        }
    }
    namespace CountOrderRatioCountHome {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            export type Type = "day" | "weekend" | "month" | "year";
        }
        export interface QueryParameters {
            type?: Parameters.Type;
        }
    }
    namespace CountPayTypeCountTrade {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type EndTime = string; // date-time
            export type StartTime = string; // date-time
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            startTime: Parameters.StartTime /* date-time */;
            endTime: Parameters.EndTime /* date-time */;
        }
    }
    namespace CountTotal {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 结束时间
             */
            export type EndTime = string; // date-time
            /**
             * 开始时间
             */
            export type StartTime = string; // date-time
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            startTime?: /* 开始时间 */ Parameters.StartTime /* date-time */;
            endTime?: /* 结束时间 */ Parameters.EndTime /* date-time */;
        }
    }
    namespace CountTradeCountVoCountTrade {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
    }
    namespace CountTransactionAmountByTimeCountTrade {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 结束时间
             */
            export type EndTime = string; // date-time
            /**
             * 开始时间
             */
            export type StartTime = string; // date-time
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            export type TimeType = "day" | "weekend" | "month" | "year";
        }
        export interface QueryParameters {
            timeType?: Parameters.TimeType;
            startTime?: /* 开始时间 */ Parameters.StartTime /* date-time */;
            endTime?: /* 结束时间 */ Parameters.EndTime /* date-time */;
        }
    }
    namespace CountTransportListCountHome {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            export type Type = "day" | "weekend" | "month" | "year";
        }
        export interface QueryParameters {
            type?: Parameters.Type;
        }
    }
    namespace CountTransportMonthOrderCountDriver {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
    }
    namespace CountTransportRatioCountHome {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            export type Type = "day" | "weekend" | "month" | "year";
        }
        export interface QueryParameters {
            type?: Parameters.Type;
        }
    }
    namespace CountTransportRequireRatioCountCustomer {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            export type Type = "company" | "personal";
        }
        export interface QueryParameters {
            type: Parameters.Type;
        }
    }
    namespace CountTransportTypeRatioCountCustomer {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            export type Type = "company" | "personal";
        }
        export interface QueryParameters {
            type: Parameters.Type;
        }
    }
    namespace CountTurnOver {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 结束时间
             */
            export type EndTime = string; // date-time
            /**
             * 开始时间
             */
            export type StartTime = string; // date-time
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            startTime?: /* 开始时间 */ Parameters.StartTime /* date-time */;
            endTime?: /* 结束时间 */ Parameters.EndTime /* date-time */;
        }
    }
    namespace DealCarRepairForm {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 处理意见
             */
            export type DealRemark = string;
            /**
             * 处理结果：同意：yes；退回：back;
             */
            export type DealResult = "yes" | "back";
            /**
             * 主键
             */
            export type Id = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id?: /* 主键 */ Parameters.Id /* int64 */;
            dealResult: /* 处理结果：同意：yes；退回：back; */ Parameters.DealResult;
            dealRemark?: /* 处理意见 */ Parameters.DealRemark;
        }
    }
    namespace Delete {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type Id = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id: Parameters.Id /* int64 */;
        }
    }
    namespace DeleteAsset {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type Id = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id: Parameters.Id /* int64 */;
        }
    }
    namespace DeleteAssetSale {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type Id = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id: Parameters.Id /* int64 */;
        }
    }
    namespace DeleteCarRepairForm {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type Id = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id: Parameters.Id /* int64 */;
        }
    }
    namespace DeleteCarRoute {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type Id = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id: Parameters.Id /* int64 */;
        }
    }
    namespace DeleteExpenseTime {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type Id = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id: Parameters.Id /* int64 */;
        }
    }
    namespace DeleteFactoryManage {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type Id = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id: Parameters.Id /* int64 */;
        }
    }
    namespace DeleteFile {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type Path = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            path: Parameters.Path;
        }
    }
    namespace DeleteImg {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type Id = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            export type Type = number; // int32
        }
        export interface QueryParameters {
            id: Parameters.Id /* int64 */;
            type: Parameters.Type /* int32 */;
        }
    }
    namespace DeleteLoadingStrategyConfig {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type Id = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id: Parameters.Id /* int64 */;
        }
    }
    namespace DeleteRoute {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type Id = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id: Parameters.Id /* int64 */;
        }
    }
    namespace DeleteStaffCashSettlement {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type Id = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id: Parameters.Id /* int64 */;
        }
    }
    namespace DeleteTempRangConfig {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type Id = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id: Parameters.Id /* int64 */;
        }
    }
    namespace DeleteTransportOrder {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type OrderId = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            orderId: Parameters.OrderId /* int64 */;
        }
    }
    namespace DeleteTransportSchemeConfig {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type Id = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id: Parameters.Id /* int64 */;
        }
    }
    namespace DeleteWarnConfig {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type Id = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id: Parameters.Id /* int64 */;
        }
    }
    namespace EditAsset {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 数据上传周期（分钟）
             */
            export type ActiveWaitTime = number; // int32
            export type AssetsId = string;
            export type AssetsModel = string;
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type CreateTime = string; // date-time
            export type Creator = string;
            export type Deleted = boolean;
            export type DeptId = number; // int64
            export type FactoryId = number; // int64
            /**
             * 湿度校准值
             */
            export type HumCalibration = number; // float
            export type Id = number; // int64
            export type Imei = string;
            /**
             * 温湿度采样周期（分钟）
             */
            export type IntervalTime = number; // int32
            export type LastUpdateTime = string; // date-time
            export type Remark = string;
            export type SimNo = string;
            export type Ssid = string;
            export type Status = string;
            /**
             * 温度校准值
             */
            export type TempCalibration = number; // float
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            export type UpdateTime = string; // date-time
            export type Updater = string;
        }
        export interface QueryParameters {
            activeWaitTime?: /* 数据上传周期（分钟） */ Parameters.ActiveWaitTime /* int32 */;
            intervalTime?: /* 温湿度采样周期（分钟） */ Parameters.IntervalTime /* int32 */;
            tempCalibration?: /* 温度校准值 */ Parameters.TempCalibration /* float */;
            humCalibration?: /* 湿度校准值 */ Parameters.HumCalibration /* float */;
            id?: Parameters.Id /* int64 */;
            assetsId?: Parameters.AssetsId;
            factoryId?: Parameters.FactoryId /* int64 */;
            assetsModel?: Parameters.AssetsModel;
            ssid?: Parameters.Ssid;
            imei?: Parameters.Imei;
            simNo?: Parameters.SimNo;
            status?: Parameters.Status;
            remark?: Parameters.Remark;
            lastUpdateTime?: Parameters.LastUpdateTime /* date-time */;
            deptId?: Parameters.DeptId /* int64 */;
            createTime?: Parameters.CreateTime /* date-time */;
            updateTime?: Parameters.UpdateTime /* date-time */;
            creator?: Parameters.Creator;
            updater?: Parameters.Updater;
            deleted?: Parameters.Deleted;
        }
    }
    namespace EditAssetWholeConfig {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            export type ActiveWaitTime = number; // int32
            export type AppVersion = string;
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type BatteryLevel = number; // int32
            export type ConfigUpdateTime = string; // date-time
            export type CreateTime = string; // date-time
            export type Creator = string;
            export type Deleted = boolean;
            export type DeptId = number; // int64
            export type DeviceId = string;
            export type HumCalibration = number;
            export type Iccid = string;
            export type IntervalTime = number; // int32
            export type Model = string;
            export type NumberOfTimes = number; // int32
            export type StartDelay = number; // int32
            export type TempCalibration = number;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            export type UpdateTime = string; // date-time
            export type Updater = string;
        }
        export interface QueryParameters {
            deviceId?: Parameters.DeviceId;
            model?: Parameters.Model;
            appVersion?: Parameters.AppVersion;
            iccid?: Parameters.Iccid;
            numberOfTimes?: Parameters.NumberOfTimes /* int32 */;
            activeWaitTime?: Parameters.ActiveWaitTime /* int32 */;
            intervalTime?: Parameters.IntervalTime /* int32 */;
            startDelay?: Parameters.StartDelay /* int32 */;
            tempCalibration?: Parameters.TempCalibration;
            humCalibration?: Parameters.HumCalibration;
            batteryLevel?: Parameters.BatteryLevel /* int32 */;
            configUpdateTime?: Parameters.ConfigUpdateTime /* date-time */;
            deptId?: Parameters.DeptId /* int64 */;
            createTime?: Parameters.CreateTime /* date-time */;
            updateTime?: Parameters.UpdateTime /* date-time */;
            creator?: Parameters.Creator;
            updater?: Parameters.Updater;
            deleted?: Parameters.Deleted;
        }
    }
    namespace EditCarInquire {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type Contacts = string;
            export type ContactsAddress = string;
            export type ContactsPhone = string;
            export type CreateTime = string; // date-time
            export type Creator = string;
            export type Deleted = boolean;
            export type DeptId = number; // int64
            export type Id = number; // int64
            export type Remark = string;
            export type ShopName = string;
            export type Status = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            export type UpdateTime = string; // date-time
            export type Updater = string;
        }
        export interface QueryParameters {
            id?: Parameters.Id /* int64 */;
            shopName?: Parameters.ShopName;
            contacts?: Parameters.Contacts;
            contactsPhone?: Parameters.ContactsPhone;
            contactsAddress?: Parameters.ContactsAddress;
            remark?: Parameters.Remark;
            status?: Parameters.Status;
            deptId?: Parameters.DeptId /* int64 */;
            createTime?: Parameters.CreateTime /* date-time */;
            updateTime?: Parameters.UpdateTime /* date-time */;
            creator?: Parameters.Creator;
            updater?: Parameters.Updater;
            deleted?: Parameters.Deleted;
        }
    }
    namespace EditCarRoute {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * ID
             */
            export type Id = number; // int64
            /**
             * 定点路线详情列表
             */
            export type List = /* 定点路线详情列表 */ ServiceTypeCcms.Schemas.FixedPointRouteDetailDO[];
            /**
             * 备注
             */
            export type Remark = string;
            /**
             * 路线名称
             */
            export type RouteName = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id?: /* ID */ Parameters.Id /* int64 */;
            routeName?: /* 路线名称 */ Parameters.RouteName;
            remark?: /* 备注 */ Parameters.Remark;
            list?: /* 定点路线详情列表 */ Parameters.List;
        }
    }
    namespace EditDamage {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            export type AccidentAddress = string;
            export type AccidentCarModel = string;
            export type AccidentCarNumber = string;
            export type AccidentCarType = string;
            export type AccidentForm = string;
            export type AccidentHighwayGovLevel = string;
            export type AccidentHighwayItLevel = string;
            export type AccidentHighwaySection = string;
            export type AccidentLevel = string;
            export type AccidentReason = string;
            export type AccidentReasonAnalysis = string;
            export type AccidentTime = string; // date-time
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type Content = string;
            export type CreateTime = string; // date-time
            export type Creator = string;
            export type DamageLevel = string;
            export type DealTime = string; // date-time
            export type DealType = string;
            export type DealUserId = number; // int64
            export type DeclareTime = string; // date-time
            export type Deleted = boolean;
            export type DeptId = number; // int64
            export type DriverName = string;
            export type DriverNumber = string;
            export type DriverPhone = string;
            export type DrivingNumber = string;
            export type FaultLevel = string;
            export type FaultRemark = string;
            /**
             * 文件信息
             */
            export type Files = /* 文件信息 */ ServiceTypeCcms.Schemas.AccidentFaultDamageReportFileDO[];
            export type Id = number; // int64
            export type MainGoods = string;
            export type OrderId = number; // int64
            export type ReductionMoney = number;
            export type ReportType = string;
            export type Status = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            /**
             * 运输费用
             */
            export type TotalMoney = number;
            export type UpdateTime = string; // date-time
            export type Updater = string;
            export type WeatherCondition = string;
        }
        export interface QueryParameters {
            totalMoney?: /* 运输费用 */ Parameters.TotalMoney;
            files?: /* 文件信息 */ Parameters.Files;
            id?: Parameters.Id /* int64 */;
            orderId?: Parameters.OrderId /* int64 */;
            reportType?: Parameters.ReportType;
            accidentLevel?: Parameters.AccidentLevel;
            accidentForm?: Parameters.AccidentForm;
            accidentTime?: Parameters.AccidentTime /* date-time */;
            accidentAddress?: Parameters.AccidentAddress;
            weatherCondition?: Parameters.WeatherCondition;
            accidentHighwayItLevel?: Parameters.AccidentHighwayItLevel;
            accidentHighwayGovLevel?: Parameters.AccidentHighwayGovLevel;
            accidentHighwaySection?: Parameters.AccidentHighwaySection;
            accidentReason?: Parameters.AccidentReason;
            accidentCarNumber?: Parameters.AccidentCarNumber;
            accidentCarType?: Parameters.AccidentCarType;
            accidentCarModel?: Parameters.AccidentCarModel;
            mainGoods?: Parameters.MainGoods;
            driverName?: Parameters.DriverName;
            driverNumber?: Parameters.DriverNumber;
            accidentReasonAnalysis?: Parameters.AccidentReasonAnalysis;
            driverPhone?: Parameters.DriverPhone;
            drivingNumber?: Parameters.DrivingNumber;
            faultLevel?: Parameters.FaultLevel;
            faultRemark?: Parameters.FaultRemark;
            damageLevel?: Parameters.DamageLevel;
            status?: Parameters.Status;
            declareTime?: Parameters.DeclareTime /* date-time */;
            content?: Parameters.Content;
            dealType?: Parameters.DealType;
            reductionMoney?: Parameters.ReductionMoney;
            dealUserId?: Parameters.DealUserId /* int64 */;
            dealTime?: Parameters.DealTime /* date-time */;
            deptId?: Parameters.DeptId /* int64 */;
            createTime?: Parameters.CreateTime /* date-time */;
            updateTime?: Parameters.UpdateTime /* date-time */;
            creator?: Parameters.Creator;
            updater?: Parameters.Updater;
            deleted?: Parameters.Deleted;
        }
    }
    namespace EditFactoryManage {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            export type Address = string;
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type ContactsName = string;
            export type CreateTime = string; // date-time
            export type Creator = string;
            export type Deleted = boolean;
            export type DeptId = number; // int64
            export type Id = number; // int64
            export type Name = string;
            export type Phone = string;
            export type Remark = string;
            export type Status = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            export type UpdateTime = string; // date-time
            export type Updater = string;
        }
        export interface QueryParameters {
            id?: Parameters.Id /* int64 */;
            name?: Parameters.Name;
            contactsName?: Parameters.ContactsName;
            phone?: Parameters.Phone;
            address?: Parameters.Address;
            status?: Parameters.Status;
            remark?: Parameters.Remark;
            deptId?: Parameters.DeptId /* int64 */;
            createTime?: Parameters.CreateTime /* date-time */;
            updateTime?: Parameters.UpdateTime /* date-time */;
            creator?: Parameters.Creator;
            updater?: Parameters.Updater;
            deleted?: Parameters.Deleted;
        }
    }
    namespace EditFault {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            export type AccidentAddress = string;
            export type AccidentCarModel = string;
            export type AccidentCarNumber = string;
            export type AccidentCarType = string;
            export type AccidentForm = string;
            export type AccidentHighwayGovLevel = string;
            export type AccidentHighwayItLevel = string;
            export type AccidentHighwaySection = string;
            export type AccidentLevel = string;
            export type AccidentReason = string;
            export type AccidentReasonAnalysis = string;
            export type AccidentTime = string; // date-time
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type Content = string;
            export type CreateTime = string; // date-time
            export type Creator = string;
            export type DamageLevel = string;
            export type DealTime = string; // date-time
            export type DealType = string;
            export type DealUserId = number; // int64
            export type DeclareTime = string; // date-time
            export type Deleted = boolean;
            export type DeptId = number; // int64
            export type DriverName = string;
            export type DriverNumber = string;
            export type DriverPhone = string;
            export type DrivingNumber = string;
            export type FaultLevel = string;
            export type FaultRemark = string;
            /**
             * 文件信息
             */
            export type Files = /* 文件信息 */ ServiceTypeCcms.Schemas.AccidentFaultDamageReportFileDO[];
            export type Id = number; // int64
            export type MainGoods = string;
            export type OrderId = number; // int64
            export type ReductionMoney = number;
            export type ReportType = string;
            export type Status = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            /**
             * 运输费用
             */
            export type TotalMoney = number;
            export type UpdateTime = string; // date-time
            export type Updater = string;
            export type WeatherCondition = string;
        }
        export interface QueryParameters {
            totalMoney?: /* 运输费用 */ Parameters.TotalMoney;
            files?: /* 文件信息 */ Parameters.Files;
            id?: Parameters.Id /* int64 */;
            orderId?: Parameters.OrderId /* int64 */;
            reportType?: Parameters.ReportType;
            accidentLevel?: Parameters.AccidentLevel;
            accidentForm?: Parameters.AccidentForm;
            accidentTime?: Parameters.AccidentTime /* date-time */;
            accidentAddress?: Parameters.AccidentAddress;
            weatherCondition?: Parameters.WeatherCondition;
            accidentHighwayItLevel?: Parameters.AccidentHighwayItLevel;
            accidentHighwayGovLevel?: Parameters.AccidentHighwayGovLevel;
            accidentHighwaySection?: Parameters.AccidentHighwaySection;
            accidentReason?: Parameters.AccidentReason;
            accidentCarNumber?: Parameters.AccidentCarNumber;
            accidentCarType?: Parameters.AccidentCarType;
            accidentCarModel?: Parameters.AccidentCarModel;
            mainGoods?: Parameters.MainGoods;
            driverName?: Parameters.DriverName;
            driverNumber?: Parameters.DriverNumber;
            accidentReasonAnalysis?: Parameters.AccidentReasonAnalysis;
            driverPhone?: Parameters.DriverPhone;
            drivingNumber?: Parameters.DrivingNumber;
            faultLevel?: Parameters.FaultLevel;
            faultRemark?: Parameters.FaultRemark;
            damageLevel?: Parameters.DamageLevel;
            status?: Parameters.Status;
            declareTime?: Parameters.DeclareTime /* date-time */;
            content?: Parameters.Content;
            dealType?: Parameters.DealType;
            reductionMoney?: Parameters.ReductionMoney;
            dealUserId?: Parameters.DealUserId /* int64 */;
            dealTime?: Parameters.DealTime /* date-time */;
            deptId?: Parameters.DeptId /* int64 */;
            createTime?: Parameters.CreateTime /* date-time */;
            updateTime?: Parameters.UpdateTime /* date-time */;
            creator?: Parameters.Creator;
            updater?: Parameters.Updater;
            deleted?: Parameters.Deleted;
        }
    }
    namespace EditIncidentAccidentFaultDamageReport {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            export type AccidentAddress = string;
            export type AccidentCarModel = string;
            export type AccidentCarNumber = string;
            export type AccidentCarType = string;
            export type AccidentForm = string;
            export type AccidentHighwayGovLevel = string;
            export type AccidentHighwayItLevel = string;
            export type AccidentHighwaySection = string;
            export type AccidentLevel = string;
            export type AccidentReason = string;
            export type AccidentReasonAnalysis = string;
            export type AccidentTime = string; // date-time
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type Content = string;
            export type CreateTime = string; // date-time
            export type Creator = string;
            export type DamageLevel = string;
            export type DealTime = string; // date-time
            export type DealType = string;
            export type DealUserId = number; // int64
            export type DeclareTime = string; // date-time
            export type Deleted = boolean;
            export type DeptId = number; // int64
            export type DriverName = string;
            export type DriverNumber = string;
            export type DriverPhone = string;
            export type DrivingNumber = string;
            export type FaultLevel = string;
            export type FaultRemark = string;
            /**
             * 文件信息
             */
            export type Files = /* 文件信息 */ ServiceTypeCcms.Schemas.AccidentFaultDamageReportFileDO[];
            export type Id = number; // int64
            export type MainGoods = string;
            export type OrderId = number; // int64
            export type ReductionMoney = number;
            export type ReportType = string;
            export type Status = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            /**
             * 运输费用
             */
            export type TotalMoney = number;
            export type UpdateTime = string; // date-time
            export type Updater = string;
            export type WeatherCondition = string;
        }
        export interface QueryParameters {
            totalMoney?: /* 运输费用 */ Parameters.TotalMoney;
            files?: /* 文件信息 */ Parameters.Files;
            id?: Parameters.Id /* int64 */;
            orderId?: Parameters.OrderId /* int64 */;
            reportType?: Parameters.ReportType;
            accidentLevel?: Parameters.AccidentLevel;
            accidentForm?: Parameters.AccidentForm;
            accidentTime?: Parameters.AccidentTime /* date-time */;
            accidentAddress?: Parameters.AccidentAddress;
            weatherCondition?: Parameters.WeatherCondition;
            accidentHighwayItLevel?: Parameters.AccidentHighwayItLevel;
            accidentHighwayGovLevel?: Parameters.AccidentHighwayGovLevel;
            accidentHighwaySection?: Parameters.AccidentHighwaySection;
            accidentReason?: Parameters.AccidentReason;
            accidentCarNumber?: Parameters.AccidentCarNumber;
            accidentCarType?: Parameters.AccidentCarType;
            accidentCarModel?: Parameters.AccidentCarModel;
            mainGoods?: Parameters.MainGoods;
            driverName?: Parameters.DriverName;
            driverNumber?: Parameters.DriverNumber;
            accidentReasonAnalysis?: Parameters.AccidentReasonAnalysis;
            driverPhone?: Parameters.DriverPhone;
            drivingNumber?: Parameters.DrivingNumber;
            faultLevel?: Parameters.FaultLevel;
            faultRemark?: Parameters.FaultRemark;
            damageLevel?: Parameters.DamageLevel;
            status?: Parameters.Status;
            declareTime?: Parameters.DeclareTime /* date-time */;
            content?: Parameters.Content;
            dealType?: Parameters.DealType;
            reductionMoney?: Parameters.ReductionMoney;
            dealUserId?: Parameters.DealUserId /* int64 */;
            dealTime?: Parameters.DealTime /* date-time */;
            deptId?: Parameters.DeptId /* int64 */;
            createTime?: Parameters.CreateTime /* date-time */;
            updateTime?: Parameters.UpdateTime /* date-time */;
            creator?: Parameters.Creator;
            updater?: Parameters.Updater;
            deleted?: Parameters.Deleted;
        }
    }
    namespace Enable {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type Id = number; // int64
            export type Status = "y" | "n" | "del";
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id: Parameters.Id /* int64 */;
            status: Parameters.Status;
        }
    }
    namespace EnableAssetSale {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type Id = number; // int64
            export type Status = "y" | "n" | "del";
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id: Parameters.Id /* int64 */;
            status: Parameters.Status;
        }
    }
    namespace EnableLoadingStrategyConfig {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type Id = number; // int64
            export type Status = "y" | "n" | "del";
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id: Parameters.Id /* int64 */;
            status: Parameters.Status;
        }
    }
    namespace EnableMaintenance {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type Id = number; // int64
            export type Status = "y" | "n" | "del";
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id: Parameters.Id /* int64 */;
            status: Parameters.Status;
        }
    }
    namespace EnableTempRangConfig {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type Id = number; // int64
            export type Status = "y" | "n" | "del";
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id: Parameters.Id /* int64 */;
            status: Parameters.Status;
        }
    }
    namespace EnableTransportSchemeConfig {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type Id = number; // int64
            export type Status = "y" | "n" | "del";
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id: Parameters.Id /* int64 */;
            status: Parameters.Status;
        }
    }
    namespace EnableWarnConfig {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type Id = number; // int64
            export type Status = "y" | "n" | "del";
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id: Parameters.Id /* int64 */;
            status: Parameters.Status;
        }
    }
    namespace ExceptionHandling {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            export type AccidentAddress = string;
            export type AccidentCarModel = string;
            export type AccidentCarNumber = string;
            export type AccidentCarType = string;
            export type AccidentForm = string;
            export type AccidentHighwayGovLevel = string;
            export type AccidentHighwayItLevel = string;
            export type AccidentHighwaySection = string;
            export type AccidentLevel = string;
            export type AccidentReason = string;
            export type AccidentReasonAnalysis = string;
            export type AccidentTime = string; // date-time
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type Content = string;
            export type CreateTime = string; // date-time
            export type Creator = string;
            export type DamageLevel = string;
            export type DealTime = string; // date-time
            export type DealType = string;
            export type DealUserId = number; // int64
            export type DeclareTime = string; // date-time
            export type Deleted = boolean;
            export type DeptId = number; // int64
            export type DriverName = string;
            export type DriverNumber = string;
            export type DriverPhone = string;
            export type DrivingNumber = string;
            export type FaultLevel = string;
            export type FaultRemark = string;
            /**
             * 文件信息
             */
            export type Files = /* 文件信息 */ ServiceTypeCcms.Schemas.AccidentFaultDamageReportFileDO[];
            export type Id = number; // int64
            export type MainGoods = string;
            export type OrderId = number; // int64
            export type ReductionMoney = number;
            export type ReportType = string;
            export type Status = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            /**
             * 运输费用
             */
            export type TotalMoney = number;
            export type UpdateTime = string; // date-time
            export type Updater = string;
            export type WeatherCondition = string;
        }
        export interface QueryParameters {
            totalMoney?: /* 运输费用 */ Parameters.TotalMoney;
            files?: /* 文件信息 */ Parameters.Files;
            id?: Parameters.Id /* int64 */;
            orderId?: Parameters.OrderId /* int64 */;
            reportType?: Parameters.ReportType;
            accidentLevel?: Parameters.AccidentLevel;
            accidentForm?: Parameters.AccidentForm;
            accidentTime?: Parameters.AccidentTime /* date-time */;
            accidentAddress?: Parameters.AccidentAddress;
            weatherCondition?: Parameters.WeatherCondition;
            accidentHighwayItLevel?: Parameters.AccidentHighwayItLevel;
            accidentHighwayGovLevel?: Parameters.AccidentHighwayGovLevel;
            accidentHighwaySection?: Parameters.AccidentHighwaySection;
            accidentReason?: Parameters.AccidentReason;
            accidentCarNumber?: Parameters.AccidentCarNumber;
            accidentCarType?: Parameters.AccidentCarType;
            accidentCarModel?: Parameters.AccidentCarModel;
            mainGoods?: Parameters.MainGoods;
            driverName?: Parameters.DriverName;
            driverNumber?: Parameters.DriverNumber;
            accidentReasonAnalysis?: Parameters.AccidentReasonAnalysis;
            driverPhone?: Parameters.DriverPhone;
            drivingNumber?: Parameters.DrivingNumber;
            faultLevel?: Parameters.FaultLevel;
            faultRemark?: Parameters.FaultRemark;
            damageLevel?: Parameters.DamageLevel;
            status?: Parameters.Status;
            declareTime?: Parameters.DeclareTime /* date-time */;
            content?: Parameters.Content;
            dealType?: Parameters.DealType;
            reductionMoney?: Parameters.ReductionMoney;
            dealUserId?: Parameters.DealUserId /* int64 */;
            dealTime?: Parameters.DealTime /* date-time */;
            deptId?: Parameters.DeptId /* int64 */;
            createTime?: Parameters.CreateTime /* date-time */;
            updateTime?: Parameters.UpdateTime /* date-time */;
            creator?: Parameters.Creator;
            updater?: Parameters.Updater;
            deleted?: Parameters.Deleted;
        }
    }
    namespace ExportExcelTemplateAsset {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        namespace Responses {
            export interface $200 {
            }
        }
    }
    namespace FeedbackProcessing {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type CreateTime = string; // date-time
            export type Creator = string;
            export type DealContent = string;
            export type DealStatus = string;
            export type DealUserId = number; // int64
            export type Deleted = boolean;
            export type DeptId = number; // int64
            export type FeedbackContent = string;
            export type FeedbackTime = string; // date-time
            export type Id = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            export type UpdateTime = string; // date-time
            export type Updater = string;
            export type UserId = number; // int64
            export type WaybillNumber = string;
        }
        export interface QueryParameters {
            id?: Parameters.Id /* int64 */;
            userId?: Parameters.UserId /* int64 */;
            feedbackTime?: Parameters.FeedbackTime /* date-time */;
            waybillNumber?: Parameters.WaybillNumber;
            feedbackContent?: Parameters.FeedbackContent;
            dealContent?: Parameters.DealContent;
            dealStatus?: Parameters.DealStatus;
            dealUserId?: Parameters.DealUserId /* int64 */;
            deptId?: Parameters.DeptId /* int64 */;
            createTime?: Parameters.CreateTime /* date-time */;
            updateTime?: Parameters.UpdateTime /* date-time */;
            creator?: Parameters.Creator;
            updater?: Parameters.Updater;
            deleted?: Parameters.Deleted;
        }
    }
    namespace FinishLoad {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 文件列表
             */
            export type FileList = /* 文件列表 */ ServiceTypeCcms.Schemas.TransportOrderFileDO[];
            /**
             * 订单ID
             */
            export type OrderId = number; // int64
            /**
             * 描述
             */
            export type Remark = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            /**
             * 操作用户类型
             */
            export type Type = "customer" | "driver";
        }
        export interface QueryParameters {
            orderId: /* 订单ID */ Parameters.OrderId /* int64 */;
            type: /* 操作用户类型 */ Parameters.Type;
            fileList?: /* 文件列表 */ Parameters.FileList;
            remark?: /* 描述 */ Parameters.Remark;
        }
    }
    namespace FinishUnload {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 文件列表
             */
            export type FileList = /* 文件列表 */ ServiceTypeCcms.Schemas.TransportOrderFileDO[];
            /**
             * 订单ID
             */
            export type OrderId = number; // int64
            /**
             * 描述
             */
            export type Remark = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            /**
             * 操作用户类型
             */
            export type Type = "customer" | "driver";
        }
        export interface QueryParameters {
            orderId: /* 订单ID */ Parameters.OrderId /* int64 */;
            type: /* 操作用户类型 */ Parameters.Type;
            fileList?: /* 文件列表 */ Parameters.FileList;
            remark?: /* 描述 */ Parameters.Remark;
        }
    }
    namespace Get {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type Id = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id: Parameters.Id;
        }
    }
    namespace GetAccidentFaultDamageReport {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type Id = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id: Parameters.Id /* int64 */;
        }
    }
    namespace GetAssetSale {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type Id = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id: Parameters.Id /* int64 */;
        }
    }
    namespace GetAssetsSalePlan {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type Id = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id: Parameters.Id /* int64 */;
        }
    }
    namespace GetBusinessRule {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type Id = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id: Parameters.Id /* int64 */;
        }
    }
    namespace GetByAssetsId {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            export type AssetsId = string;
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            assetsId: Parameters.AssetsId;
        }
    }
    namespace GetByLoginUserDriver {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
    }
    namespace GetCar {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type Id = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id: Parameters.Id;
        }
    }
    namespace GetCarRepairForm {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type Id = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id: Parameters.Id /* int64 */;
        }
    }
    namespace GetCarRoute {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type Id = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id: Parameters.Id /* int64 */;
        }
    }
    namespace GetCarrier {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type Id = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id: Parameters.Id;
        }
    }
    namespace GetCustomer {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type Id = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id: Parameters.Id /* int64 */;
        }
    }
    namespace GetDictData {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type DictType = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            export type Value = string;
        }
        export interface QueryParameters {
            dictType: Parameters.DictType;
            value: Parameters.Value;
        }
    }
    namespace GetDictDataList {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type DictType = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            dictType: Parameters.DictType;
        }
    }
    namespace GetDriver {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type Id = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id: Parameters.Id;
        }
    }
    namespace GetErrorConfig {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
    }
    namespace GetExpenseManagement {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type Id = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id: Parameters.Id /* int64 */;
        }
    }
    namespace GetExpenseTime {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type Id = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id: Parameters.Id /* int64 */;
        }
    }
    namespace GetFeedback {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type Id = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id: Parameters.Id /* int64 */;
        }
    }
    namespace GetImgs {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
    }
    namespace GetLoadingStrategyConfig {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type Id = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id: Parameters.Id /* int64 */;
        }
    }
    namespace GetRoute {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type Id = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id: Parameters.Id /* int64 */;
        }
    }
    namespace GetStaffCashSettlement {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type Id = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id: Parameters.Id /* int64 */;
        }
    }
    namespace GetSystemConfig {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
    }
    namespace GetTempRangConfig {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type Id = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id: Parameters.Id /* int64 */;
        }
    }
    namespace GetTransportOrder {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type Id = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id: Parameters.Id /* int64 */;
        }
    }
    namespace GetTransportOrderErrorRecord {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type Id = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id: Parameters.Id /* int64 */;
        }
    }
    namespace GetTransportOrderWarnRecord {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type Id = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id: Parameters.Id /* int64 */;
        }
    }
    namespace GetTransportSchemeConfig {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type Id = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id: Parameters.Id /* int64 */;
        }
    }
    namespace GetWarnConfig {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type Id = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id: Parameters.Id /* int64 */;
        }
    }
    namespace HailingOrder {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 车辆ID
             */
            export type CarId = string;
            /**
             * 订单ID列表
             */
            export type OrderIdList = number /* int64 */[];
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            orderIdList: /* 订单ID列表 */ Parameters.OrderIdList;
            carId: /* 车辆ID */ Parameters.CarId;
        }
    }
    namespace HandleFiles {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type BusinessId = number; // int64
            export type Files = /* 文件信息 */ ServiceTypeCcms.Schemas.AccidentFaultDamageReportFileDO[];
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            files: Parameters.Files;
            businessId: Parameters.BusinessId /* int64 */;
        }
    }
    namespace ImportExcelAsset {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
    }
    namespace MergeOrder {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 运输要求车长
             */
            export type CarLength = string;
            /**
             * 运输要求车长ID
             */
            export type CarLengthId = number; // int64
            /**
             * 运输要求车型ID
             */
            export type CarModelId = number; // int64
            /**
             * 运输要求车型名称
             */
            export type CarModelName = string;
            /**
             * 车辆号码
             */
            export type CarNumber = string;
            /**
             * 驾驶员ID
             */
            export type DriverId = string;
            /**
             * 和单策略：按客户：customer；按路线：route
             */
            export type MergeStrategy = "customer" | "route";
            /**
             * 订单ID列表
             */
            export type OrderIdList = number /* int64 */[];
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            mergeStrategy: /* 和单策略：按客户：customer；按路线：route */ Parameters.MergeStrategy;
            orderIdList: /* 订单ID列表 */ Parameters.OrderIdList;
            carModelId: /* 运输要求车型ID */ Parameters.CarModelId /* int64 */;
            carModelName: /* 运输要求车型名称 */ Parameters.CarModelName;
            carLengthId: /* 运输要求车长ID */ Parameters.CarLengthId /* int64 */;
            carLength: /* 运输要求车长 */ Parameters.CarLength;
            carNumber?: /* 车辆号码 */ Parameters.CarNumber;
            driverId?: /* 驾驶员ID */ Parameters.DriverId;
        }
    }
    namespace PayTransportOrder {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type OrderId = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            orderId: Parameters.OrderId /* int64 */;
        }
    }
    namespace PublishOrder {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 卸货地址列表
             */
            export type AddressList = /* 卸货地址DTO */ ServiceTypeCcms.Schemas.ReceiverAddressDTO[];
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 计费方式：自行报价：self；电询：phone；
             */
            export type BillMethod = "self" | "phone";
            /**
             * 运输要求车长
             */
            export type CarLength = string;
            /**
             * 运输要求车长ID
             */
            export type CarLengthId = number; // int64
            /**
             * 运输要求车型ID
             */
            export type CarModelId = number; // int64
            /**
             * 运输要求车型名称
             */
            export type CarModelName = string;
            /**
             * 装车时间
             */
            export type LoadingTime = string; // date-time
            /**
             * 其它需要：发票：invoice；保障金：security_fund；
             */
            export type OtherNeeds = "invoice" | "security_fund";
            /**
             * 支付方式：线上支付：online；线下结算：offline；
             */
            export type PayType = string;
            /**
             * 备注
             */
            export type Remark = string;
            /**
             * 发货地址
             */
            export type SendAddress = string;
            /**
             * 发货市
             */
            export type SendCity = string;
            /**
             * 发货区县
             */
            export type SendCounty = string;
            /**
             * 发货纬度
             */
            export type SendLat = string;
            /**
             * 发货经度
             */
            export type SendLng = string;
            /**
             * 发货人
             */
            export type SendName = string;
            /**
             * 发货人手机号
             */
            export type SendPhone = string;
            /**
             * 发货省
             */
            export type SendProvince = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            /**
             * 运输费用
             */
            export type TotalMoney = number;
            /**
             * 运输车辆
             */
            export type TransportCarNumber = string;
            /**
             * 运输驾驶员姓名
             */
            export type TransportDriverName = string;
            /**
             * 运输驾驶员联系方式
             */
            export type TransportDriverPhone = string;
            /**
             * 运输要求湿度
             */
            export type TransportHum = string;
            /**
             * 运输要求：常规运输：normal；冷链运输：cold；
             */
            export type TransportRequire = "normal" | "cold";
            /**
             * 运输要求温度
             */
            export type TransportTemp = string;
            /**
             * 运输类型：整车：whole_car；零担：less_car；
             */
            export type TransportType = "whole_car" | "less_car";
        }
        export interface QueryParameters {
            loadingTime: /* 装车时间 */ Parameters.LoadingTime /* date-time */;
            sendProvince: /* 发货省 */ Parameters.SendProvince;
            sendCity: /* 发货市 */ Parameters.SendCity;
            sendCounty: /* 发货区县 */ Parameters.SendCounty;
            sendAddress: /* 发货地址 */ Parameters.SendAddress;
            sendLng?: /* 发货经度 */ Parameters.SendLng;
            sendLat?: /* 发货纬度 */ Parameters.SendLat;
            sendName: /* 发货人 */ Parameters.SendName;
            sendPhone: /* 发货人手机号 */ Parameters.SendPhone;
            transportTemp?: /* 运输要求温度 */ Parameters.TransportTemp;
            transportHum?: /* 运输要求湿度 */ Parameters.TransportHum;
            transportRequire: /* 运输要求：常规运输：normal；冷链运输：cold； */ Parameters.TransportRequire;
            carModelId?: /* 运输要求车型ID */ Parameters.CarModelId /* int64 */;
            carModelName?: /* 运输要求车型名称 */ Parameters.CarModelName;
            carLengthId?: /* 运输要求车长ID */ Parameters.CarLengthId /* int64 */;
            carLength?: /* 运输要求车长 */ Parameters.CarLength;
            transportType?: /* 运输类型：整车：whole_car；零担：less_car； */ Parameters.TransportType;
            billMethod: /* 计费方式：自行报价：self；电询：phone； */ Parameters.BillMethod;
            totalMoney: /* 运输费用 */ Parameters.TotalMoney;
            payType: /* 支付方式：线上支付：online；线下结算：offline； */ Parameters.PayType;
            otherNeeds?: /* 其它需要：发票：invoice；保障金：security_fund； */ Parameters.OtherNeeds;
            remark?: /* 备注 */ Parameters.Remark;
            transportCarNumber?: /* 运输车辆 */ Parameters.TransportCarNumber;
            transportDriverName?: /* 运输驾驶员姓名 */ Parameters.TransportDriverName;
            transportDriverPhone?: /* 运输驾驶员联系方式 */ Parameters.TransportDriverPhone;
            addressList: /* 卸货地址列表 */ Parameters.AddressList;
        }
    }
    namespace PublishWaybill {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 卸货地址列表
             */
            export type AddressList = /* 卸货地址DTO */ ServiceTypeCcms.Schemas.ReceiverAddressDTO[];
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 计费方式：自行报价：self；电询：phone；
             */
            export type BillMethod = "self" | "phone";
            /**
             * 运输要求车长
             */
            export type CarLength = string;
            /**
             * 运输要求车长ID
             */
            export type CarLengthId = number; // int64
            /**
             * 运输要求车型ID
             */
            export type CarModelId = number; // int64
            /**
             * 运输要求车型名称
             */
            export type CarModelName = string;
            /**
             * 装车时间
             */
            export type LoadingTime = string; // date-time
            /**
             * 其它需要：发票：invoice；保障金：security_fund；
             */
            export type OtherNeeds = "invoice" | "security_fund";
            /**
             * 支付方式：线上支付：online；线下结算：offline；
             */
            export type PayType = string;
            /**
             * 备注
             */
            export type Remark = string;
            /**
             * 发货地址
             */
            export type SendAddress = string;
            /**
             * 发货市
             */
            export type SendCity = string;
            /**
             * 发货区县
             */
            export type SendCounty = string;
            /**
             * 发货纬度
             */
            export type SendLat = string;
            /**
             * 发货经度
             */
            export type SendLng = string;
            /**
             * 发货人
             */
            export type SendName = string;
            /**
             * 发货人手机号
             */
            export type SendPhone = string;
            /**
             * 发货省
             */
            export type SendProvince = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            /**
             * 运输费用
             */
            export type TotalMoney = number;
            /**
             * 运输车辆
             */
            export type TransportCarNumber = string;
            /**
             * 运输驾驶员姓名
             */
            export type TransportDriverName = string;
            /**
             * 运输驾驶员联系方式
             */
            export type TransportDriverPhone = string;
            /**
             * 运输要求湿度
             */
            export type TransportHum = string;
            /**
             * 运输要求：常规运输：normal；冷链运输：cold；
             */
            export type TransportRequire = "normal" | "cold";
            /**
             * 运输要求温度
             */
            export type TransportTemp = string;
            /**
             * 运输类型：整车：whole_car；零担：less_car；
             */
            export type TransportType = "whole_car" | "less_car";
        }
        export interface QueryParameters {
            loadingTime: /* 装车时间 */ Parameters.LoadingTime /* date-time */;
            sendProvince: /* 发货省 */ Parameters.SendProvince;
            sendCity: /* 发货市 */ Parameters.SendCity;
            sendCounty: /* 发货区县 */ Parameters.SendCounty;
            sendAddress: /* 发货地址 */ Parameters.SendAddress;
            sendLng?: /* 发货经度 */ Parameters.SendLng;
            sendLat?: /* 发货纬度 */ Parameters.SendLat;
            sendName: /* 发货人 */ Parameters.SendName;
            sendPhone: /* 发货人手机号 */ Parameters.SendPhone;
            transportTemp?: /* 运输要求温度 */ Parameters.TransportTemp;
            transportHum?: /* 运输要求湿度 */ Parameters.TransportHum;
            transportRequire: /* 运输要求：常规运输：normal；冷链运输：cold； */ Parameters.TransportRequire;
            carModelId?: /* 运输要求车型ID */ Parameters.CarModelId /* int64 */;
            carModelName?: /* 运输要求车型名称 */ Parameters.CarModelName;
            carLengthId?: /* 运输要求车长ID */ Parameters.CarLengthId /* int64 */;
            carLength?: /* 运输要求车长 */ Parameters.CarLength;
            transportType?: /* 运输类型：整车：whole_car；零担：less_car； */ Parameters.TransportType;
            billMethod: /* 计费方式：自行报价：self；电询：phone； */ Parameters.BillMethod;
            totalMoney: /* 运输费用 */ Parameters.TotalMoney;
            payType: /* 支付方式：线上支付：online；线下结算：offline； */ Parameters.PayType;
            otherNeeds?: /* 其它需要：发票：invoice；保障金：security_fund； */ Parameters.OtherNeeds;
            remark?: /* 备注 */ Parameters.Remark;
            transportCarNumber?: /* 运输车辆 */ Parameters.TransportCarNumber;
            transportDriverName?: /* 运输驾驶员姓名 */ Parameters.TransportDriverName;
            transportDriverPhone?: /* 运输驾驶员联系方式 */ Parameters.TransportDriverPhone;
            addressList: /* 卸货地址列表 */ Parameters.AddressList;
        }
    }
    namespace Query {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 车主名称
             */
            export type CarMasterName = string;
            /**
             * 结束时间
             */
            export type EndTime = string; // date-time
            /**
             * 保单号码
             */
            export type InsureNumber = string;
            /**
             * 保单类型：交强险：compulsory；商业险：business；
             */
            export type InsureType = string;
            export type PageNo = number; // int32
            export type PageSize = number; // int32
            /**
             * 开始时间
             */
            export type StartTime = string; // date-time
            /**
             * 状态：保障中：ensure；即将到期：due；已终止：end；
             */
            export type Status = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            /**
             * 用户ID
             */
            export type UserId = number; // int64
        }
        export interface QueryParameters {
            userId?: /* 用户ID */ Parameters.UserId /* int64 */;
            insureNumber?: /* 保单号码 */ Parameters.InsureNumber;
            carMasterName?: /* 车主名称 */ Parameters.CarMasterName;
            startTime?: /* 开始时间 */ Parameters.StartTime /* date-time */;
            endTime?: /* 结束时间 */ Parameters.EndTime /* date-time */;
            insureType?: /* 保单类型：交强险：compulsory；商业险：business； */ Parameters.InsureType;
            status?: /* 状态：保障中：ensure；即将到期：due；已终止：end； */ Parameters.Status;
            pageNo?: Parameters.PageNo /* int32 */;
            pageSize?: Parameters.PageSize /* int32 */;
        }
    }
    namespace QueryAccidentDeclaration {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 事故地点
             */
            export type AccidentAddress = string;
            /**
             * 事故车牌号
             */
            export type AccidentCarNumber = string;
            /**
             * 事故车辆类型
             */
            export type AccidentCarType = string;
            /**
             * 事故等级：轻微事故：slight；一般事故：commonly；重大事故：major；特大事故：extra_large；
             */
            export type AccidentLevel = "slight" | "commonly" | "major" | "extra_large";
            /**
             * 事故时间
             */
            export type AccidentTime = string; // date-time
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 货损申报结束时间
             */
            export type DamageEndTime = string; // date-time
            /**
             * 货损等级：轻微损坏：slight；中等损坏：medium；严重损坏：major；
             */
            export type DamageLevel = "slight" | "commonly" | "major" | "extra_large";
            /**
             * 货损申报开始时间
             */
            export type DamageStartTime = string; // date-time
            /**
             * 故障申报结束时间
             */
            export type DeclareEndTime = string; // date-time
            /**
             * 故障申报开始时间
             */
            export type DeclareStartTime = string; // date-time
            /**
             * 申报时间
             */
            export type DeclareTime = string; // date-time
            /**
             * 驾驶员姓名
             */
            export type DriverName = string;
            /**
             * 结束时间
             */
            export type EndTime = string; // date-time
            /**
             * 故障等级：轻微故障：slight；一般故障：commonly；严重故障：major；致命故障：extra_large；
             */
            export type FaultLevel = "slight" | "commonly" | "major" | "extra_large";
            /**
             * 订单ID
             */
            export type OrderId = number; // int64
            export type PageNo = number; // int32
            export type PageSize = number; // int32
            /**
             * 申报类型：事故：accident；故障：fault；货损：damage；
             */
            export type ReportType = "accident" | "fault" | "damage";
            /**
             * 开始时间
             */
            export type StartTime = string; // date-time
            /**
             * 状态：未处理：not；完结：finish；续报：next；
             */
            export type Status = "not" | "next" | "finish";
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            /**
             * 运单号
             */
            export type TransportNumber = string;
        }
        export interface QueryParameters {
            orderId?: /* 订单ID */ Parameters.OrderId /* int64 */;
            transportNumber?: /* 运单号 */ Parameters.TransportNumber;
            reportType?: /* 申报类型：事故：accident；故障：fault；货损：damage； */ Parameters.ReportType;
            accidentLevel?: /* 事故等级：轻微事故：slight；一般事故：commonly；重大事故：major；特大事故：extra_large； */ Parameters.AccidentLevel;
            accidentAddress?: /* 事故地点 */ Parameters.AccidentAddress;
            accidentCarType?: /* 事故车辆类型 */ Parameters.AccidentCarType;
            accidentTime?: /* 事故时间 */ Parameters.AccidentTime /* date-time */;
            driverName?: /* 驾驶员姓名 */ Parameters.DriverName;
            accidentCarNumber?: /* 事故车牌号 */ Parameters.AccidentCarNumber;
            declareTime?: /* 申报时间 */ Parameters.DeclareTime /* date-time */;
            faultLevel?: /* 故障等级：轻微故障：slight；一般故障：commonly；严重故障：major；致命故障：extra_large； */ Parameters.FaultLevel;
            damageLevel?: /* 货损等级：轻微损坏：slight；中等损坏：medium；严重损坏：major； */ Parameters.DamageLevel;
            status?: /* 状态：未处理：not；完结：finish；续报：next； */ Parameters.Status;
            startTime?: /* 开始时间 */ Parameters.StartTime /* date-time */;
            endTime?: /* 结束时间 */ Parameters.EndTime /* date-time */;
            declareStartTime?: /* 故障申报开始时间 */ Parameters.DeclareStartTime /* date-time */;
            declareEndTime?: /* 故障申报结束时间 */ Parameters.DeclareEndTime /* date-time */;
            damageStartTime?: /* 货损申报开始时间 */ Parameters.DamageStartTime /* date-time */;
            damageEndTime?: /* 货损申报结束时间 */ Parameters.DamageEndTime /* date-time */;
            pageNo?: Parameters.PageNo /* int32 */;
            pageSize?: Parameters.PageSize /* int32 */;
        }
    }
    namespace QueryAsset {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 资产ID
             */
            export type AssetsId = string;
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 厂商ID
             */
            export type FactoryId = number; // int64
            export type PageNo = number; // int32
            export type PageSize = number; // int32
            /**
             * SIM卡号
             */
            export type SimNo = string;
            /**
             * 状态：空闲：free；租赁：lease；销售：sale；损坏：damage；
             */
            export type Status = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            simNo?: /* SIM卡号 */ Parameters.SimNo;
            assetsId?: /* 资产ID */ Parameters.AssetsId;
            factoryId?: /* 厂商ID */ Parameters.FactoryId /* int64 */;
            status?: /* 状态：空闲：free；租赁：lease；销售：sale；损坏：damage； */ Parameters.Status;
            pageNo?: Parameters.PageNo /* int32 */;
            pageSize?: Parameters.PageSize /* int32 */;
        }
    }
    namespace QueryAssetStatus {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 资产ID
             */
            export type AssetsId = string;
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 客户名称
             */
            export type CustomerName = string;
            export type PageNo = number; // int32
            export type PageSize = number; // int32
            /**
             * 资产状态：在线：online；离线：offline
             */
            export type Status = "online" | "offline";
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            status?: /* 资产状态：在线：online；离线：offline */ Parameters.Status;
            customerName?: /* 客户名称 */ Parameters.CustomerName;
            assetsId?: /* 资产ID */ Parameters.AssetsId;
            pageNo?: Parameters.PageNo /* int32 */;
            pageSize?: Parameters.PageSize /* int32 */;
        }
    }
    namespace QueryAssetsSalePlan {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 方案名称
             */
            export type Name = string;
            export type PageNo = number; // int32
            export type PageSize = number; // int32
            /**
             * 状态：启用：y；停用：n；
             */
            export type Status = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            name?: /* 方案名称 */ Parameters.Name;
            status?: /* 状态：启用：y；停用：n； */ Parameters.Status;
            pageNo?: Parameters.PageNo /* int32 */;
            pageSize?: Parameters.PageSize /* int32 */;
        }
    }
    namespace QueryBaseInfoTransportOrder {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 审核状态：待审核：not；已通过：yes；已驳回：back；
             */
            export type AuditStatus = "not" | "yes" | "back";
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 车长ID
             */
            export type CarLengthId = number; // int64
            /**
             * 车型ID
             */
            export type CarModelId = number; // int64
            /**
             * 运输类型：整车：whole_car；零担：less_car
             */
            export type CarRouteType = "whole_car" | "less_car";
            /**
             * 运输车辆类型：normal：常规车；cold：冷藏车
             */
            export type CarType = "normal" | "cold";
            /**
             * 客户ID
             */
            export type CustomerId = string;
            /**
             * 客户名称
             */
            export type CustomerName = string;
            /**
             * 结束市
             */
            export type EndCity = string;
            /**
             * 结束省
             */
            export type EndProvince = string;
            /**
             * 历史还是实时订单
             */
            export type History = "y" | "n";
            /**
             * 不等于运单状态：待装货：wait_load；配送中：delivery；待支付：wait_pay；已完成：finish；已取消：cancel；
             */
            export type NotEqualTransportStatus = "wait_load" | "delivery" | "wait_pay" | "finish" | "cancel";
            /**
             * 是货主还是驾驶员
             */
            export type OperateUserType = "customer" | "driver";
            /**
             * 结束时间-查询订单时间
             */
            export type OrderEndTime = string; // date-time
            /**
             * 订单号
             */
            export type OrderNumber = string;
            /**
             * 开始时间-查询订单时间
             */
            export type OrderStartTime = string; // date-time
            /**
             * 订单状态：待接单：not；已接单：yes；已取消：cancel；
             */
            export type OrderStatus = "not" | "yes" | "cancel";
            export type PageNo = number; // int32
            export type PageSize = number; // int32
            /**
             * 是否约单
             */
            export type PlanOrder = "y" | "n";
            /**
             * 发货地址
             */
            export type SendAddress = string;
            /**
             * 发货人手机号
             */
            export type SendPhone = string;
            /**
             * 是否拆单合单：是：y；否：n；
             */
            export type SplitOrder = "y" | "n";
            /**
             * 开始市
             */
            export type StartCity = string;
            /**
             * 开始省
             */
            export type StartProvince = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            /**
             * 运输车辆驾驶员ID
             */
            export type TransportCarDriverId = string;
            /**
             * 驾驶员ID不为空
             */
            export type TransportCarDriverIdIsNotNull = "y" | "n";
            /**
             * 运单号
             */
            export type TransportNumber = string;
            /**
             * 运单状态：待装货：wait_load；配送中：delivery；待支付：wait_pay；已完成：finish；已取消：cancel；
             */
            export type TransportStatus = "wait_load" | "delivery" | "wait_pay" | "finish" | "cancel";
            /**
             * 运单编号或车牌号
             */
            export type WaybillNumberOrCarNumber = string;
        }
        export interface QueryParameters {
            customerId?: /* 客户ID */ Parameters.CustomerId;
            customerName?: /* 客户名称 */ Parameters.CustomerName;
            orderStartTime?: /* 开始时间-查询订单时间 */ Parameters.OrderStartTime /* date-time */;
            orderEndTime?: /* 结束时间-查询订单时间 */ Parameters.OrderEndTime /* date-time */;
            transportNumber?: /* 运单号 */ Parameters.TransportNumber;
            orderNumber?: /* 订单号 */ Parameters.OrderNumber;
            auditStatus?: /* 审核状态：待审核：not；已通过：yes；已驳回：back； */ Parameters.AuditStatus;
            orderStatus?: /* 订单状态：待接单：not；已接单：yes；已取消：cancel； */ Parameters.OrderStatus;
            transportStatus?: /* 运单状态：待装货：wait_load；配送中：delivery；待支付：wait_pay；已完成：finish；已取消：cancel； */ Parameters.TransportStatus;
            notEqualTransportStatus?: /* 不等于运单状态：待装货：wait_load；配送中：delivery；待支付：wait_pay；已完成：finish；已取消：cancel； */ Parameters.NotEqualTransportStatus;
            transportCarDriverId?: /* 运输车辆驾驶员ID */ Parameters.TransportCarDriverId;
            sendPhone?: /* 发货人手机号 */ Parameters.SendPhone;
            startProvince?: /* 开始省 */ Parameters.StartProvince;
            startCity?: /* 开始市 */ Parameters.StartCity;
            sendAddress?: /* 发货地址 */ Parameters.SendAddress;
            endProvince?: /* 结束省 */ Parameters.EndProvince;
            endCity?: /* 结束市 */ Parameters.EndCity;
            carModelId?: /* 车型ID */ Parameters.CarModelId /* int64 */;
            carLengthId?: /* 车长ID */ Parameters.CarLengthId /* int64 */;
            carRouteType?: /* 运输类型：整车：whole_car；零担：less_car */ Parameters.CarRouteType;
            carType?: /* 运输车辆类型：normal：常规车；cold：冷藏车 */ Parameters.CarType;
            operateUserType?: /* 是货主还是驾驶员 */ Parameters.OperateUserType;
            planOrder?: /* 是否约单 */ Parameters.PlanOrder;
            splitOrder?: /* 是否拆单合单：是：y；否：n； */ Parameters.SplitOrder;
            transportCarDriverIdIsNotNull?: /* 驾驶员ID不为空 */ Parameters.TransportCarDriverIdIsNotNull;
            pageNo?: Parameters.PageNo /* int32 */;
            pageSize?: Parameters.PageSize /* int32 */;
            history?: /* 历史还是实时订单 */ Parameters.History;
            waybillNumberOrCarNumber?: /* 运单编号或车牌号 */ Parameters.WaybillNumberOrCarNumber;
        }
    }
    namespace QueryBusinessRule {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type EndTime = string; // date-time
            /**
             * 规则名称
             */
            export type Name = string;
            export type PageNo = number; // int32
            export type PageSize = number; // int32
            export type StartTime = string; // date-time
            /**
             * 状态:启用：y；停用：n；
             */
            export type Status = "y" | "n" | "del";
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            name?: /* 规则名称 */ Parameters.Name;
            status?: /* 状态:启用：y；停用：n； */ Parameters.Status;
            startTime?: Parameters.StartTime /* date-time */;
            endTime?: Parameters.EndTime /* date-time */;
            pageNo?: Parameters.PageNo /* int32 */;
            pageSize?: Parameters.PageSize /* int32 */;
        }
    }
    namespace QueryByDriverId {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type DriverId = string;
            export type Status = "wait" | "yes" | "back";
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            driverId: Parameters.DriverId;
            status: Parameters.Status;
        }
    }
    namespace QueryCanMerge {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 审核状态：待审核：not；已通过：yes；已驳回：back；
             */
            export type AuditStatus = "not" | "yes" | "back";
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 车长ID
             */
            export type CarLengthId = number; // int64
            /**
             * 车型ID
             */
            export type CarModelId = number; // int64
            /**
             * 运输类型：整车：whole_car；零担：less_car
             */
            export type CarRouteType = "whole_car" | "less_car";
            /**
             * 运输车辆类型：normal：常规车；cold：冷藏车
             */
            export type CarType = "normal" | "cold";
            /**
             * 客户ID
             */
            export type CustomerId = string;
            /**
             * 客户名称
             */
            export type CustomerName = string;
            /**
             * 结束市
             */
            export type EndCity = string;
            /**
             * 结束省
             */
            export type EndProvince = string;
            /**
             * 历史还是实时订单
             */
            export type History = "y" | "n";
            /**
             * 不等于运单状态：待装货：wait_load；配送中：delivery；待支付：wait_pay；已完成：finish；已取消：cancel；
             */
            export type NotEqualTransportStatus = "wait_load" | "delivery" | "wait_pay" | "finish" | "cancel";
            /**
             * 是货主还是驾驶员
             */
            export type OperateUserType = "customer" | "driver";
            /**
             * 结束时间-查询订单时间
             */
            export type OrderEndTime = string; // date-time
            /**
             * 订单号
             */
            export type OrderNumber = string;
            /**
             * 开始时间-查询订单时间
             */
            export type OrderStartTime = string; // date-time
            /**
             * 订单状态：待接单：not；已接单：yes；已取消：cancel；
             */
            export type OrderStatus = "not" | "yes" | "cancel";
            export type PageNo = number; // int32
            export type PageSize = number; // int32
            /**
             * 是否约单
             */
            export type PlanOrder = "y" | "n";
            /**
             * 发货地址
             */
            export type SendAddress = string;
            /**
             * 发货人手机号
             */
            export type SendPhone = string;
            /**
             * 是否拆单合单：是：y；否：n；
             */
            export type SplitOrder = "y" | "n";
            /**
             * 开始市
             */
            export type StartCity = string;
            /**
             * 开始省
             */
            export type StartProvince = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            /**
             * 运输车辆驾驶员ID
             */
            export type TransportCarDriverId = string;
            /**
             * 驾驶员ID不为空
             */
            export type TransportCarDriverIdIsNotNull = "y" | "n";
            /**
             * 运单号
             */
            export type TransportNumber = string;
            /**
             * 运单状态：待装货：wait_load；配送中：delivery；待支付：wait_pay；已完成：finish；已取消：cancel；
             */
            export type TransportStatus = "wait_load" | "delivery" | "wait_pay" | "finish" | "cancel";
            /**
             * 运单编号或车牌号
             */
            export type WaybillNumberOrCarNumber = string;
        }
        export interface QueryParameters {
            customerId?: /* 客户ID */ Parameters.CustomerId;
            customerName?: /* 客户名称 */ Parameters.CustomerName;
            orderStartTime?: /* 开始时间-查询订单时间 */ Parameters.OrderStartTime /* date-time */;
            orderEndTime?: /* 结束时间-查询订单时间 */ Parameters.OrderEndTime /* date-time */;
            transportNumber?: /* 运单号 */ Parameters.TransportNumber;
            orderNumber?: /* 订单号 */ Parameters.OrderNumber;
            auditStatus?: /* 审核状态：待审核：not；已通过：yes；已驳回：back； */ Parameters.AuditStatus;
            orderStatus?: /* 订单状态：待接单：not；已接单：yes；已取消：cancel； */ Parameters.OrderStatus;
            transportStatus?: /* 运单状态：待装货：wait_load；配送中：delivery；待支付：wait_pay；已完成：finish；已取消：cancel； */ Parameters.TransportStatus;
            notEqualTransportStatus?: /* 不等于运单状态：待装货：wait_load；配送中：delivery；待支付：wait_pay；已完成：finish；已取消：cancel； */ Parameters.NotEqualTransportStatus;
            transportCarDriverId?: /* 运输车辆驾驶员ID */ Parameters.TransportCarDriverId;
            sendPhone?: /* 发货人手机号 */ Parameters.SendPhone;
            startProvince?: /* 开始省 */ Parameters.StartProvince;
            startCity?: /* 开始市 */ Parameters.StartCity;
            sendAddress?: /* 发货地址 */ Parameters.SendAddress;
            endProvince?: /* 结束省 */ Parameters.EndProvince;
            endCity?: /* 结束市 */ Parameters.EndCity;
            carModelId?: /* 车型ID */ Parameters.CarModelId /* int64 */;
            carLengthId?: /* 车长ID */ Parameters.CarLengthId /* int64 */;
            carRouteType?: /* 运输类型：整车：whole_car；零担：less_car */ Parameters.CarRouteType;
            carType?: /* 运输车辆类型：normal：常规车；cold：冷藏车 */ Parameters.CarType;
            operateUserType?: /* 是货主还是驾驶员 */ Parameters.OperateUserType;
            planOrder?: /* 是否约单 */ Parameters.PlanOrder;
            splitOrder?: /* 是否拆单合单：是：y；否：n； */ Parameters.SplitOrder;
            transportCarDriverIdIsNotNull?: /* 驾驶员ID不为空 */ Parameters.TransportCarDriverIdIsNotNull;
            pageNo?: Parameters.PageNo /* int32 */;
            pageSize?: Parameters.PageSize /* int32 */;
            history?: /* 历史还是实时订单 */ Parameters.History;
            waybillNumberOrCarNumber?: /* 运单编号或车牌号 */ Parameters.WaybillNumberOrCarNumber;
        }
    }
    namespace QueryCar {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 车长ID
             */
            export type CarLengthId = string;
            /**
             * 车型ID
             */
            export type CarModelId = string;
            /**
             * 车牌号
             */
            export type CarNumber = string;
            /**
             * 车辆类型：normal：常规车；cold：冷藏车
             */
            export type CarType = string;
            /**
             * 承运商ID
             */
            export type CarrierId = string;
            /**
             * 驾驶证号码
             */
            export type DriverNumber = string;
            /**
             * 结束时间
             */
            export type EndTime = string; // date-time
            /**
             * 不是运力类型：个人：person；自营：self；承运商：carrier；
             */
            export type NotType = string;
            export type PageNo = number; // int32
            export type PageSize = number; // int32
            /**
             * 开始时间
             */
            export type StartTime = string; // date-time
            /**
             * 认证状态：待认证：wait；通过：yes；驳回：back；
             */
            export type Status = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            /**
             * 运力类型：个人：person；自营：self；承运商：carrier；
             */
            export type Type = string;
        }
        export interface QueryParameters {
            carNumber?: /* 车牌号 */ Parameters.CarNumber;
            carrierId?: /* 承运商ID */ Parameters.CarrierId;
            driverNumber?: /* 驾驶证号码 */ Parameters.DriverNumber;
            carType?: /* 车辆类型：normal：常规车；cold：冷藏车 */ Parameters.CarType;
            carModelId?: /* 车型ID */ Parameters.CarModelId;
            carLengthId?: /* 车长ID */ Parameters.CarLengthId;
            type?: /* 运力类型：个人：person；自营：self；承运商：carrier； */ Parameters.Type;
            notType?: /* 不是运力类型：个人：person；自营：self；承运商：carrier； */ Parameters.NotType;
            startTime?: /* 开始时间 */ Parameters.StartTime /* date-time */;
            endTime?: /* 结束时间 */ Parameters.EndTime /* date-time */;
            status?: /* 认证状态：待认证：wait；通过：yes；驳回：back； */ Parameters.Status;
            pageNo?: Parameters.PageNo /* int32 */;
            pageSize?: Parameters.PageSize /* int32 */;
        }
    }
    namespace QueryCarRepairForm {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 车牌号
             */
            export type CarNumber = string;
            /**
             * 结束时间
             */
            export type EndTime = string; // date-time
            /**
             * 报单编号
             */
            export type FormNumber = string;
            export type PageNo = number; // int32
            export type PageSize = number; // int32
            /**
             * 开始时间
             */
            export type StartTime = string; // date-time
            /**
             * 状态：未处理：not；已处理：yes；
             */
            export type Status = "not" | "yes" | "finish";
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            formNumber?: /* 报单编号 */ Parameters.FormNumber;
            carNumber?: /* 车牌号 */ Parameters.CarNumber;
            startTime?: /* 开始时间 */ Parameters.StartTime /* date-time */;
            endTime?: /* 结束时间 */ Parameters.EndTime /* date-time */;
            status?: /* 状态：未处理：not；已处理：yes； */ Parameters.Status;
            pageNo?: Parameters.PageNo /* int32 */;
            pageSize?: Parameters.PageSize /* int32 */;
        }
    }
    namespace QueryCarRoute {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
    }
    namespace QueryCarRouteCar {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 车长ID
             */
            export type CarLengthId = number; // int64
            /**
             * 车型ID
             */
            export type CarModelId = number; // int64
            /**
             * 运输类型：整车：whole_car；零担：less_car
             */
            export type CarRouteType = string;
            /**
             * 运输车辆类型：normal：常规车；cold：冷藏车
             */
            export type CarType = string;
            /**
             * 结束市
             */
            export type EndCity = string;
            /**
             * 结束省
             */
            export type EndProvince = string;
            export type PageNo = number; // int32
            export type PageSize = number; // int32
            /**
             * 开始市
             */
            export type StartCity = string;
            /**
             * 开始省
             */
            export type StartProvince = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            startProvince?: /* 开始省 */ Parameters.StartProvince;
            startCity?: /* 开始市 */ Parameters.StartCity;
            endProvince?: /* 结束省 */ Parameters.EndProvince;
            endCity?: /* 结束市 */ Parameters.EndCity;
            carModelId?: /* 车型ID */ Parameters.CarModelId /* int64 */;
            carLengthId?: /* 车长ID */ Parameters.CarLengthId /* int64 */;
            carRouteType?: /* 运输类型：整车：whole_car；零担：less_car */ Parameters.CarRouteType;
            carType?: /* 运输车辆类型：normal：常规车；cold：冷藏车 */ Parameters.CarType;
            pageNo?: Parameters.PageNo /* int32 */;
            pageSize?: Parameters.PageSize /* int32 */;
        }
    }
    namespace QueryCarrier {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 联系人
             */
            export type Contacts = string;
            /**
             * 邮箱
             */
            export type Email = string;
            /**
             * 名称
             */
            export type Name = string;
            export type PageNo = number; // int32
            export type PageSize = number; // int32
            /**
             * 手机
             */
            export type Phone = string;
            /**
             * 电话
             */
            export type Telephone = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            name?: /* 名称 */ Parameters.Name;
            contacts?: /* 联系人 */ Parameters.Contacts;
            phone?: /* 手机 */ Parameters.Phone;
            telephone?: /* 电话 */ Parameters.Telephone;
            email?: /* 邮箱 */ Parameters.Email;
            pageNo?: Parameters.PageNo /* int32 */;
            pageSize?: Parameters.PageSize /* int32 */;
        }
    }
    namespace QueryCheckList {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type OrderId = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            orderId: Parameters.OrderId /* int64 */;
        }
    }
    namespace QueryCheckTransportOrder {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type OrderId = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            orderId: Parameters.OrderId /* int64 */;
        }
    }
    namespace QueryChildList {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type OrderId = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            orderId: Parameters.OrderId /* int64 */;
        }
    }
    namespace QueryCustomer {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 申请结束时间
             */
            export type ApplyEndTime = string; // date-time
            /**
             * 申请开始时间
             */
            export type ApplyStartTime = string; // date-time
            /**
             * 审核状态：未审核：not；已审核：yes；已退回：back；
             */
            export type AuditStatus = "not" | "yes" | "back";
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 客户来源：IOS客户端：ios；安卓客户端：android；小程序：wechat；管理员添加：admin；
             */
            export type CustomerSource = "ios" | "android" | "wechat" | "admin";
            /**
             * 客户分组ID
             */
            export type GroupId = number; // int64
            /**
             * 法人手机号码
             */
            export type LegalPhone = string;
            /**
             * 客户名称
             */
            export type Name = string;
            export type PageNo = number; // int32
            export type PageSize = number; // int32
            /**
             * 客户状态
             */
            export type Status = "normal" | "stop";
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            /**
             * 客户身份
             */
            export type Type = "company" | "personal";
        }
        export interface QueryParameters {
            name?: /* 客户名称 */ Parameters.Name;
            groupId?: /* 客户分组ID */ Parameters.GroupId /* int64 */;
            type?: /* 客户身份 */ Parameters.Type;
            status?: /* 客户状态 */ Parameters.Status;
            customerSource?: /* 客户来源：IOS客户端：ios；安卓客户端：android；小程序：wechat；管理员添加：admin； */ Parameters.CustomerSource;
            auditStatus?: /* 审核状态：未审核：not；已审核：yes；已退回：back； */ Parameters.AuditStatus;
            applyStartTime?: /* 申请开始时间 */ Parameters.ApplyStartTime /* date-time */;
            applyEndTime?: /* 申请结束时间 */ Parameters.ApplyEndTime /* date-time */;
            legalPhone?: /* 法人手机号码 */ Parameters.LegalPhone;
            pageNo?: Parameters.PageNo /* int32 */;
            pageSize?: Parameters.PageSize /* int32 */;
        }
    }
    namespace QueryCustomerAssetsSaleDetail {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type PageNo = number; // int32
            export type PageSize = number; // int32
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            pageNo: Parameters.PageNo /* int32 */;
            pageSize: Parameters.PageSize /* int32 */;
        }
    }
    namespace QueryCustomerGroup {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
    }
    namespace QueryCustomerWaybill {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type CustomerIdList = string[];
            /**
             * 客户名称
             */
            export type CustomerName = string;
            /**
             * 结算结束时间
             */
            export type EndTime = string; // date-time
            /**
             * 客户身份:企业:company;个人:personal;
             */
            export type IdentityType = "company" | "personal";
            export type PageNo = number; // int32
            export type PageSize = number; // int32
            /**
             * 结算状态：是：y；否：n；
             */
            export type SettleStatus = "y" | "n";
            /**
             * 结算开始时间
             */
            export type StartTime = string; // date-time
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            /**
             * 运输驾驶员姓名
             */
            export type TransportDriverName = string;
            /**
             * 运单号
             */
            export type TransportNumber = string;
        }
        export interface QueryParameters {
            transportNumber?: /* 运单号 */ Parameters.TransportNumber;
            customerName?: /* 客户名称 */ Parameters.CustomerName;
            transportDriverName?: /* 运输驾驶员姓名 */ Parameters.TransportDriverName;
            identityType?: /* 客户身份:企业:company;个人:personal; */ Parameters.IdentityType;
            settleStatus?: /* 结算状态：是：y；否：n； */ Parameters.SettleStatus;
            startTime?: /* 结算开始时间 */ Parameters.StartTime /* date-time */;
            endTime?: /* 结算结束时间 */ Parameters.EndTime /* date-time */;
            customerIdList?: Parameters.CustomerIdList;
            pageNo?: Parameters.PageNo /* int32 */;
            pageSize?: Parameters.PageSize /* int32 */;
        }
    }
    namespace QueryDamageDeclaration {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 事故地点
             */
            export type AccidentAddress = string;
            /**
             * 事故车牌号
             */
            export type AccidentCarNumber = string;
            /**
             * 事故车辆类型
             */
            export type AccidentCarType = string;
            /**
             * 事故等级：轻微事故：slight；一般事故：commonly；重大事故：major；特大事故：extra_large；
             */
            export type AccidentLevel = "slight" | "commonly" | "major" | "extra_large";
            /**
             * 事故时间
             */
            export type AccidentTime = string; // date-time
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 货损申报结束时间
             */
            export type DamageEndTime = string; // date-time
            /**
             * 货损等级：轻微损坏：slight；中等损坏：medium；严重损坏：major；
             */
            export type DamageLevel = "slight" | "commonly" | "major" | "extra_large";
            /**
             * 货损申报开始时间
             */
            export type DamageStartTime = string; // date-time
            /**
             * 故障申报结束时间
             */
            export type DeclareEndTime = string; // date-time
            /**
             * 故障申报开始时间
             */
            export type DeclareStartTime = string; // date-time
            /**
             * 申报时间
             */
            export type DeclareTime = string; // date-time
            /**
             * 驾驶员姓名
             */
            export type DriverName = string;
            /**
             * 结束时间
             */
            export type EndTime = string; // date-time
            /**
             * 故障等级：轻微故障：slight；一般故障：commonly；严重故障：major；致命故障：extra_large；
             */
            export type FaultLevel = "slight" | "commonly" | "major" | "extra_large";
            /**
             * 订单ID
             */
            export type OrderId = number; // int64
            export type PageNo = number; // int32
            export type PageSize = number; // int32
            /**
             * 申报类型：事故：accident；故障：fault；货损：damage；
             */
            export type ReportType = "accident" | "fault" | "damage";
            /**
             * 开始时间
             */
            export type StartTime = string; // date-time
            /**
             * 状态：未处理：not；完结：finish；续报：next；
             */
            export type Status = "not" | "next" | "finish";
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            /**
             * 运单号
             */
            export type TransportNumber = string;
        }
        export interface QueryParameters {
            orderId?: /* 订单ID */ Parameters.OrderId /* int64 */;
            transportNumber?: /* 运单号 */ Parameters.TransportNumber;
            reportType?: /* 申报类型：事故：accident；故障：fault；货损：damage； */ Parameters.ReportType;
            accidentLevel?: /* 事故等级：轻微事故：slight；一般事故：commonly；重大事故：major；特大事故：extra_large； */ Parameters.AccidentLevel;
            accidentAddress?: /* 事故地点 */ Parameters.AccidentAddress;
            accidentCarType?: /* 事故车辆类型 */ Parameters.AccidentCarType;
            accidentTime?: /* 事故时间 */ Parameters.AccidentTime /* date-time */;
            driverName?: /* 驾驶员姓名 */ Parameters.DriverName;
            accidentCarNumber?: /* 事故车牌号 */ Parameters.AccidentCarNumber;
            declareTime?: /* 申报时间 */ Parameters.DeclareTime /* date-time */;
            faultLevel?: /* 故障等级：轻微故障：slight；一般故障：commonly；严重故障：major；致命故障：extra_large； */ Parameters.FaultLevel;
            damageLevel?: /* 货损等级：轻微损坏：slight；中等损坏：medium；严重损坏：major； */ Parameters.DamageLevel;
            status?: /* 状态：未处理：not；完结：finish；续报：next； */ Parameters.Status;
            startTime?: /* 开始时间 */ Parameters.StartTime /* date-time */;
            endTime?: /* 结束时间 */ Parameters.EndTime /* date-time */;
            declareStartTime?: /* 故障申报开始时间 */ Parameters.DeclareStartTime /* date-time */;
            declareEndTime?: /* 故障申报结束时间 */ Parameters.DeclareEndTime /* date-time */;
            damageStartTime?: /* 货损申报开始时间 */ Parameters.DamageStartTime /* date-time */;
            damageEndTime?: /* 货损申报结束时间 */ Parameters.DamageEndTime /* date-time */;
            pageNo?: Parameters.PageNo /* int32 */;
            pageSize?: Parameters.PageSize /* int32 */;
        }
    }
    namespace QueryDriver {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 承运商ID
             */
            export type CarrierId = number; // int64
            /**
             * 驾驶证编号
             */
            export type DriverLicense = string;
            /**
             * 结束时间
             */
            export type EndTime = string; // date-time
            /**
             * 身份证号
             */
            export type IdCard = string;
            /**
             * 驾驶员名称
             */
            export type Name = string;
            export type PageNo = number; // int32
            export type PageSize = number; // int32
            /**
             * 手机号
             */
            export type Phone = string;
            /**
             * 开始时间
             */
            export type StartTime = string; // date-time
            /**
             * 认证状态：待认证：wait；通过：yes；驳回：back；
             */
            export type Status = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            /**
             * 运力类型：个人：person；自营：self；承运商：carrier；
             */
            export type Type = string;
        }
        export interface QueryParameters {
            name?: /* 驾驶员名称 */ Parameters.Name;
            carrierId?: /* 承运商ID */ Parameters.CarrierId /* int64 */;
            driverLicense?: /* 驾驶证编号 */ Parameters.DriverLicense;
            phone?: /* 手机号 */ Parameters.Phone;
            idCard?: /* 身份证号 */ Parameters.IdCard;
            type?: /* 运力类型：个人：person；自营：self；承运商：carrier； */ Parameters.Type;
            status?: /* 认证状态：待认证：wait；通过：yes；驳回：back； */ Parameters.Status;
            startTime?: /* 开始时间 */ Parameters.StartTime /* date-time */;
            endTime?: /* 结束时间 */ Parameters.EndTime /* date-time */;
            pageNo?: Parameters.PageNo /* int32 */;
            pageSize?: Parameters.PageSize /* int32 */;
        }
    }
    namespace QueryDriverOrders {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type CustomerIdList = string[];
            /**
             * 客户名称
             */
            export type CustomerName = string;
            /**
             * 结算结束时间
             */
            export type EndTime = string; // date-time
            /**
             * 客户身份:企业:company;个人:personal;
             */
            export type IdentityType = "company" | "personal";
            export type PageNo = number; // int32
            export type PageSize = number; // int32
            /**
             * 结算状态：是：y；否：n；
             */
            export type SettleStatus = "y" | "n";
            /**
             * 结算开始时间
             */
            export type StartTime = string; // date-time
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            /**
             * 运输驾驶员姓名
             */
            export type TransportDriverName = string;
            /**
             * 运单号
             */
            export type TransportNumber = string;
        }
        export interface QueryParameters {
            transportNumber?: /* 运单号 */ Parameters.TransportNumber;
            customerName?: /* 客户名称 */ Parameters.CustomerName;
            transportDriverName?: /* 运输驾驶员姓名 */ Parameters.TransportDriverName;
            identityType?: /* 客户身份:企业:company;个人:personal; */ Parameters.IdentityType;
            settleStatus?: /* 结算状态：是：y；否：n； */ Parameters.SettleStatus;
            startTime?: /* 结算开始时间 */ Parameters.StartTime /* date-time */;
            endTime?: /* 结算结束时间 */ Parameters.EndTime /* date-time */;
            customerIdList?: Parameters.CustomerIdList;
            pageNo?: Parameters.PageNo /* int32 */;
            pageSize?: Parameters.PageSize /* int32 */;
        }
    }
    namespace QueryDriverSign {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type CarNumber = string;
            export type CreateTime = string; // date-time
            export type Creator = string;
            export type Deleted = boolean;
            export type DeptId = number; // int64
            export type DriverId = number; // int64
            /**
             * 驾驶员姓名
             */
            export type DriverName = string;
            /**
             * 结束时间
             */
            export type EndTime = string; // date-time
            export type Id = number; // int64
            export type PageNo = number; // int32
            export type PageSize = number; // int32
            export type SignTime = string; // date-time
            /**
             * 开始时间
             */
            export type StartTime = string; // date-time
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            export type UpdateTime = string; // date-time
            export type Updater = string;
        }
        export interface QueryParameters {
            driverName?: /* 驾驶员姓名 */ Parameters.DriverName;
            startTime?: /* 开始时间 */ Parameters.StartTime /* date-time */;
            endTime?: /* 结束时间 */ Parameters.EndTime /* date-time */;
            pageNo?: Parameters.PageNo /* int32 */;
            pageSize?: Parameters.PageSize /* int32 */;
            id?: Parameters.Id /* int64 */;
            signTime?: Parameters.SignTime /* date-time */;
            driverId?: Parameters.DriverId /* int64 */;
            carNumber?: Parameters.CarNumber;
            deptId?: Parameters.DeptId /* int64 */;
            createTime?: Parameters.CreateTime /* date-time */;
            updateTime?: Parameters.UpdateTime /* date-time */;
            creator?: Parameters.Creator;
            updater?: Parameters.Updater;
            deleted?: Parameters.Deleted;
        }
    }
    namespace QueryEnums {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
    }
    namespace QueryEnumsByKey {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type Key = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            key: Parameters.Key;
        }
    }
    namespace QueryExpenseTime {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type CreateTime = string; // date-time
            export type Creator = string;
            export type Deleted = boolean;
            export type DeptId = number; // int64
            export type EndTime = string; // date-time
            export type Id = number; // int64
            export type Name = string;
            export type PageNo = number; // int32
            export type PageSize = number; // int32
            export type Remark = string;
            export type StartTime = string; // date-time
            export type Status = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            export type UpdateTime = string; // date-time
            export type Updater = string;
        }
        export interface QueryParameters {
            pageNo?: Parameters.PageNo /* int32 */;
            pageSize?: Parameters.PageSize /* int32 */;
            id?: Parameters.Id /* int64 */;
            name?: Parameters.Name;
            startTime?: Parameters.StartTime /* date-time */;
            endTime?: Parameters.EndTime /* date-time */;
            status?: Parameters.Status;
            remark?: Parameters.Remark;
            deptId?: Parameters.DeptId /* int64 */;
            createTime?: Parameters.CreateTime /* date-time */;
            updateTime?: Parameters.UpdateTime /* date-time */;
            creator?: Parameters.Creator;
            updater?: Parameters.Updater;
            deleted?: Parameters.Deleted;
        }
    }
    namespace QueryExpenseType {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
    }
    namespace QueryExpensesExpenseManagement {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type ExpenseTypeId = number; // int64
            export type Name = string;
            /**
             * 跳转页
             */
            export type PageNo = number; // int32
            /**
             * 分页大小
             */
            export type PageSize = number; // int32
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            name?: Parameters.Name;
            expenseTypeId?: Parameters.ExpenseTypeId /* int64 */;
            pageNo?: /* 跳转页 */ Parameters.PageNo /* int32 */;
            pageSize?: /* 分页大小 */ Parameters.PageSize /* int32 */;
        }
    }
    namespace QueryFactoryManage {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            export type Address = string;
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type ContactsName = string;
            export type CreateTime = string; // date-time
            export type Creator = string;
            export type Deleted = boolean;
            export type DeptId = number; // int64
            export type Id = number; // int64
            export type Name = string;
            export type PageNo = number; // int32
            export type PageSize = number; // int32
            export type Phone = string;
            export type Remark = string;
            export type Status = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            export type UpdateTime = string; // date-time
            export type Updater = string;
        }
        export interface QueryParameters {
            pageNo?: Parameters.PageNo /* int32 */;
            pageSize?: Parameters.PageSize /* int32 */;
            id?: Parameters.Id /* int64 */;
            name?: Parameters.Name;
            contactsName?: Parameters.ContactsName;
            phone?: Parameters.Phone;
            address?: Parameters.Address;
            status?: Parameters.Status;
            remark?: Parameters.Remark;
            deptId?: Parameters.DeptId /* int64 */;
            createTime?: Parameters.CreateTime /* date-time */;
            updateTime?: Parameters.UpdateTime /* date-time */;
            creator?: Parameters.Creator;
            updater?: Parameters.Updater;
            deleted?: Parameters.Deleted;
        }
    }
    namespace QueryFaultDeclaration {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 事故地点
             */
            export type AccidentAddress = string;
            /**
             * 事故车牌号
             */
            export type AccidentCarNumber = string;
            /**
             * 事故车辆类型
             */
            export type AccidentCarType = string;
            /**
             * 事故等级：轻微事故：slight；一般事故：commonly；重大事故：major；特大事故：extra_large；
             */
            export type AccidentLevel = "slight" | "commonly" | "major" | "extra_large";
            /**
             * 事故时间
             */
            export type AccidentTime = string; // date-time
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 货损申报结束时间
             */
            export type DamageEndTime = string; // date-time
            /**
             * 货损等级：轻微损坏：slight；中等损坏：medium；严重损坏：major；
             */
            export type DamageLevel = "slight" | "commonly" | "major" | "extra_large";
            /**
             * 货损申报开始时间
             */
            export type DamageStartTime = string; // date-time
            /**
             * 故障申报结束时间
             */
            export type DeclareEndTime = string; // date-time
            /**
             * 故障申报开始时间
             */
            export type DeclareStartTime = string; // date-time
            /**
             * 申报时间
             */
            export type DeclareTime = string; // date-time
            /**
             * 驾驶员姓名
             */
            export type DriverName = string;
            /**
             * 结束时间
             */
            export type EndTime = string; // date-time
            /**
             * 故障等级：轻微故障：slight；一般故障：commonly；严重故障：major；致命故障：extra_large；
             */
            export type FaultLevel = "slight" | "commonly" | "major" | "extra_large";
            /**
             * 订单ID
             */
            export type OrderId = number; // int64
            export type PageNo = number; // int32
            export type PageSize = number; // int32
            /**
             * 申报类型：事故：accident；故障：fault；货损：damage；
             */
            export type ReportType = "accident" | "fault" | "damage";
            /**
             * 开始时间
             */
            export type StartTime = string; // date-time
            /**
             * 状态：未处理：not；完结：finish；续报：next；
             */
            export type Status = "not" | "next" | "finish";
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            /**
             * 运单号
             */
            export type TransportNumber = string;
        }
        export interface QueryParameters {
            orderId?: /* 订单ID */ Parameters.OrderId /* int64 */;
            transportNumber?: /* 运单号 */ Parameters.TransportNumber;
            reportType?: /* 申报类型：事故：accident；故障：fault；货损：damage； */ Parameters.ReportType;
            accidentLevel?: /* 事故等级：轻微事故：slight；一般事故：commonly；重大事故：major；特大事故：extra_large； */ Parameters.AccidentLevel;
            accidentAddress?: /* 事故地点 */ Parameters.AccidentAddress;
            accidentCarType?: /* 事故车辆类型 */ Parameters.AccidentCarType;
            accidentTime?: /* 事故时间 */ Parameters.AccidentTime /* date-time */;
            driverName?: /* 驾驶员姓名 */ Parameters.DriverName;
            accidentCarNumber?: /* 事故车牌号 */ Parameters.AccidentCarNumber;
            declareTime?: /* 申报时间 */ Parameters.DeclareTime /* date-time */;
            faultLevel?: /* 故障等级：轻微故障：slight；一般故障：commonly；严重故障：major；致命故障：extra_large； */ Parameters.FaultLevel;
            damageLevel?: /* 货损等级：轻微损坏：slight；中等损坏：medium；严重损坏：major； */ Parameters.DamageLevel;
            status?: /* 状态：未处理：not；完结：finish；续报：next； */ Parameters.Status;
            startTime?: /* 开始时间 */ Parameters.StartTime /* date-time */;
            endTime?: /* 结束时间 */ Parameters.EndTime /* date-time */;
            declareStartTime?: /* 故障申报开始时间 */ Parameters.DeclareStartTime /* date-time */;
            declareEndTime?: /* 故障申报结束时间 */ Parameters.DeclareEndTime /* date-time */;
            damageStartTime?: /* 货损申报开始时间 */ Parameters.DamageStartTime /* date-time */;
            damageEndTime?: /* 货损申报结束时间 */ Parameters.DamageEndTime /* date-time */;
            pageNo?: Parameters.PageNo /* int32 */;
            pageSize?: Parameters.PageSize /* int32 */;
        }
    }
    namespace QueryFeedback {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 处理状态：未处理：not；已处理：yes
             */
            export type DealStatus = "not" | "yes" | "finish";
            /**
             * 处理人ID
             */
            export type DealUserId = number; // int64
            /**
             * 结束时间
             */
            export type EndTime = string; // date-time
            /**
             * 反馈时间
             */
            export type FeedbackTime = string; // date-time
            /**
             * 反馈类型
             */
            export type FeedbackType = string;
            /**
             * 反馈类型ID
             */
            export type FeedbackTypeId = string;
            /**
             * 客户名称
             */
            export type Name = string;
            export type PageNo = number; // int32
            export type PageSize = number; // int32
            /**
             * 开始时间
             */
            export type StartTime = string; // date-time
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            /**
             * 反馈人ID
             */
            export type UserId = number; // int64
            /**
             * 运单号
             */
            export type WaybillNumber = string;
        }
        export interface QueryParameters {
            waybillNumber?: /* 运单号 */ Parameters.WaybillNumber;
            name?: /* 客户名称 */ Parameters.Name;
            feedbackTypeId?: /* 反馈类型ID */ Parameters.FeedbackTypeId;
            feedbackType?: /* 反馈类型 */ Parameters.FeedbackType;
            userId?: /* 反馈人ID */ Parameters.UserId /* int64 */;
            dealUserId?: /* 处理人ID */ Parameters.DealUserId /* int64 */;
            feedbackTime?: /* 反馈时间 */ Parameters.FeedbackTime /* date-time */;
            dealStatus?: /* 处理状态：未处理：not；已处理：yes */ Parameters.DealStatus;
            startTime?: /* 开始时间 */ Parameters.StartTime /* date-time */;
            endTime?: /* 结束时间 */ Parameters.EndTime /* date-time */;
            pageNo?: Parameters.PageNo /* int32 */;
            pageSize?: Parameters.PageSize /* int32 */;
        }
    }
    namespace QueryFeedbackType {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
    }
    namespace QueryGoods {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
    }
    namespace QueryImage {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type BusinessId = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            businessId: Parameters.BusinessId /* int64 */;
        }
    }
    namespace QueryInquire {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type Contacts = string;
            export type ContactsAddress = string;
            export type ContactsPhone = string;
            export type CreateTime = string; // date-time
            export type Creator = string;
            export type Deleted = boolean;
            export type DeptId = number; // int64
            export type Id = number; // int64
            export type PageNo = number; // int32
            export type PageSize = number; // int32
            export type Remark = string;
            export type ShopName = string;
            export type Status = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            export type UpdateTime = string; // date-time
            export type Updater = string;
        }
        export interface QueryParameters {
            pageNo?: Parameters.PageNo /* int32 */;
            pageSize?: Parameters.PageSize /* int32 */;
            id?: Parameters.Id /* int64 */;
            shopName?: Parameters.ShopName;
            contacts?: Parameters.Contacts;
            contactsPhone?: Parameters.ContactsPhone;
            contactsAddress?: Parameters.ContactsAddress;
            remark?: Parameters.Remark;
            status?: Parameters.Status;
            deptId?: Parameters.DeptId /* int64 */;
            createTime?: Parameters.CreateTime /* date-time */;
            updateTime?: Parameters.UpdateTime /* date-time */;
            creator?: Parameters.Creator;
            updater?: Parameters.Updater;
            deleted?: Parameters.Deleted;
        }
    }
    namespace QueryLatLngRecord {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type OrderId = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            orderId: Parameters.OrderId /* int64 */;
        }
    }
    namespace QueryLength {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type CarLength = string;
            export type CarVolume = string;
            export type CarWeight = string;
            export type CreateTime = string; // date-time
            export type Creator = string;
            export type Deleted = boolean;
            export type DeptId = number; // int64
            export type Id = string;
            export type PageNo = number; // int32
            export type PageSize = number; // int32
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            export type UpdateTime = string; // date-time
            export type Updater = string;
        }
        export interface QueryParameters {
            pageNo?: Parameters.PageNo /* int32 */;
            pageSize?: Parameters.PageSize /* int32 */;
            id?: Parameters.Id;
            carLength?: Parameters.CarLength;
            carWeight?: Parameters.CarWeight;
            carVolume?: Parameters.CarVolume;
            deptId?: Parameters.DeptId /* int64 */;
            createTime?: Parameters.CreateTime /* date-time */;
            updateTime?: Parameters.UpdateTime /* date-time */;
            creator?: Parameters.Creator;
            updater?: Parameters.Updater;
            deleted?: Parameters.Deleted;
        }
    }
    namespace QueryLoadingStrategyConfig {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 结束时间
             */
            export type EndTime = string; // date-time
            /**
             * 名称
             */
            export type Name = string;
            export type PageNo = number; // int32
            export type PageSize = number; // int32
            /**
             * 开始时间
             */
            export type StartTime = string; // date-time
            /**
             * 状态:启用：y；停用：n；
             */
            export type Status = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            name?: /* 名称 */ Parameters.Name;
            status?: /* 状态:启用：y；停用：n； */ Parameters.Status;
            startTime?: /* 开始时间 */ Parameters.StartTime /* date-time */;
            endTime?: /* 结束时间 */ Parameters.EndTime /* date-time */;
            pageNo?: Parameters.PageNo /* int32 */;
            pageSize?: Parameters.PageSize /* int32 */;
        }
    }
    namespace QueryMergeVOList {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type CustomerId = string;
            export type RouteId = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            customerId: Parameters.CustomerId;
            routeId: Parameters.RouteId /* int64 */;
        }
    }
    namespace QueryModels {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type CreateTime = string; // date-time
            export type Creator = string;
            export type Deleted = boolean;
            export type DeptId = number; // int64
            export type Id = string;
            export type Name = string;
            export type PageNo = number; // int32
            export type PageSize = number; // int32
            export type Status = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            export type UpdateTime = string; // date-time
            export type Updater = string;
        }
        export interface QueryParameters {
            pageNo?: Parameters.PageNo /* int32 */;
            pageSize?: Parameters.PageSize /* int32 */;
            id?: Parameters.Id;
            name?: Parameters.Name;
            status?: Parameters.Status;
            deptId?: Parameters.DeptId /* int64 */;
            createTime?: Parameters.CreateTime /* date-time */;
            updateTime?: Parameters.UpdateTime /* date-time */;
            creator?: Parameters.Creator;
            updater?: Parameters.Updater;
            deleted?: Parameters.Deleted;
        }
    }
    namespace QueryOrderAssetList {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type OrderId = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            orderId: Parameters.OrderId /* int64 */;
        }
    }
    namespace QueryOrderSendAddress {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type CustomerId = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            customerId: Parameters.CustomerId /* int64 */;
        }
    }
    namespace QueryPackaging {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
    }
    namespace QueryRoute {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            export type Address = string;
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type City = string;
            export type County = string;
            export type CreateTime = string; // date-time
            export type Creator = string;
            export type Deleted = boolean;
            export type DeptId = number; // int64
            export type Id = number; // int64
            export type Lat = string;
            export type Lng = string;
            export type PageNo = number; // int32
            export type PageSize = number; // int32
            export type Province = string;
            export type RouteId = number; // int64
            export type Seq = number; // int32
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            export type UpdateTime = string; // date-time
            export type Updater = string;
        }
        export interface QueryParameters {
            pageNo?: Parameters.PageNo /* int32 */;
            pageSize?: Parameters.PageSize /* int32 */;
            id?: Parameters.Id /* int64 */;
            routeId?: Parameters.RouteId /* int64 */;
            province?: Parameters.Province;
            city?: Parameters.City;
            county?: Parameters.County;
            address?: Parameters.Address;
            lng?: Parameters.Lng;
            lat?: Parameters.Lat;
            seq?: Parameters.Seq /* int32 */;
            deptId?: Parameters.DeptId /* int64 */;
            createTime?: Parameters.CreateTime /* date-time */;
            updateTime?: Parameters.UpdateTime /* date-time */;
            creator?: Parameters.Creator;
            updater?: Parameters.Updater;
            deleted?: Parameters.Deleted;
        }
    }
    namespace QuerySalesOrders {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            export type AcceptAddress = string;
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type ContactsName = string;
            export type ContactsPhone = string;
            export type CreateTime = string; // date-time
            export type Creator = string;
            export type CustomerId = number; // int64
            export type CustomerName = string;
            export type Deleted = boolean;
            export type DeptId = number; // int64
            /**
             * 结束时间
             */
            export type EndTime = string; // date-time
            export type FastMailName = string;
            export type FastMailNumber = string;
            export type Id = number; // int64
            export type LeaseEndTime = string; // date-time
            export type LeaseStartTime = string; // date-time
            export type LeaseTime = number; // int32
            export type Number = number; // int32
            export type OrderTime = string; // date-time
            export type PageNo = number; // int32
            export type PageSize = number; // int32
            export type Price = number;
            export type Remark = string;
            export type SaleConfigId = number; // int64
            export type SaleType = string;
            /**
             * 开始时间
             */
            export type StartTime = string; // date-time
            export type Status = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            export type TotalPrice = number;
            export type UpdateTime = string; // date-time
            export type Updater = string;
        }
        export interface QueryParameters {
            startTime?: /* 开始时间 */ Parameters.StartTime /* date-time */;
            endTime?: /* 结束时间 */ Parameters.EndTime /* date-time */;
            pageNo?: Parameters.PageNo /* int32 */;
            pageSize?: Parameters.PageSize /* int32 */;
            id?: Parameters.Id /* int64 */;
            customerId?: Parameters.CustomerId /* int64 */;
            customerName?: Parameters.CustomerName;
            contactsName?: Parameters.ContactsName;
            contactsPhone?: Parameters.ContactsPhone;
            saleConfigId?: Parameters.SaleConfigId /* int64 */;
            saleType?: Parameters.SaleType;
            leaseTime?: Parameters.LeaseTime /* int32 */;
            leaseStartTime?: Parameters.LeaseStartTime /* date-time */;
            leaseEndTime?: Parameters.LeaseEndTime /* date-time */;
            number?: Parameters.Number /* int32 */;
            price?: Parameters.Price;
            totalPrice?: Parameters.TotalPrice;
            status?: Parameters.Status;
            remark?: Parameters.Remark;
            acceptAddress?: Parameters.AcceptAddress;
            fastMailName?: Parameters.FastMailName;
            fastMailNumber?: Parameters.FastMailNumber;
            orderTime?: Parameters.OrderTime /* date-time */;
            deptId?: Parameters.DeptId /* int64 */;
            createTime?: Parameters.CreateTime /* date-time */;
            updateTime?: Parameters.UpdateTime /* date-time */;
            creator?: Parameters.Creator;
            updater?: Parameters.Updater;
            deleted?: Parameters.Deleted;
        }
    }
    namespace QueryStaffCashSettlement {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type PageNo = number; // int32
            export type PageSize = number; // int32
            /**
             * 结算状态：是：y；否：n；
             */
            export type SettleStatus = "y" | "n";
            /**
             * 员工名称
             */
            export type StaffName = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            /**
             * 运单号
             */
            export type TransportNumber = string;
        }
        export interface QueryParameters {
            transportNumber?: /* 运单号 */ Parameters.TransportNumber;
            staffName?: /* 员工名称 */ Parameters.StaffName;
            settleStatus?: /* 结算状态：是：y；否：n； */ Parameters.SettleStatus;
            pageNo?: Parameters.PageNo /* int32 */;
            pageSize?: Parameters.PageSize /* int32 */;
        }
    }
    namespace QueryStaffManage {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type ContactsPhone = string;
            export type CreateTime = string; // date-time
            export type Creator = string;
            export type Deleted = boolean;
            export type DeptId = number; // int64
            export type Gender = string;
            export type Id = string;
            export type Name = string;
            export type OrgId = number; // int64
            export type PageNo = number; // int32
            export type PageSize = number; // int32
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            export type UpdateTime = string; // date-time
            export type Updater = string;
        }
        export interface QueryParameters {
            pageNo?: Parameters.PageNo /* int32 */;
            pageSize?: Parameters.PageSize /* int32 */;
            id?: Parameters.Id;
            name?: Parameters.Name;
            gender?: Parameters.Gender;
            contactsPhone?: Parameters.ContactsPhone;
            orgId?: Parameters.OrgId /* int64 */;
            deptId?: Parameters.DeptId /* int64 */;
            createTime?: Parameters.CreateTime /* date-time */;
            updateTime?: Parameters.UpdateTime /* date-time */;
            creator?: Parameters.Creator;
            updater?: Parameters.Updater;
            deleted?: Parameters.Deleted;
        }
    }
    namespace QueryTempHumRecords {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type OrderId = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            orderId: Parameters.OrderId /* int64 */;
        }
    }
    namespace QueryTempRangConfig {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type PageNo = number; // int32
            export type PageSize = number; // int32
            export type Status = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            status: Parameters.Status;
            pageNo: Parameters.PageNo /* int32 */;
            pageSize: Parameters.PageSize /* int32 */;
        }
    }
    namespace QueryTransportOrder {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 审核状态：待审核：not；已通过：yes；已驳回：back；
             */
            export type AuditStatus = "not" | "yes" | "back";
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 车长ID
             */
            export type CarLengthId = number; // int64
            /**
             * 车型ID
             */
            export type CarModelId = number; // int64
            /**
             * 运输类型：整车：whole_car；零担：less_car
             */
            export type CarRouteType = "whole_car" | "less_car";
            /**
             * 运输车辆类型：normal：常规车；cold：冷藏车
             */
            export type CarType = "normal" | "cold";
            /**
             * 客户ID
             */
            export type CustomerId = string;
            /**
             * 客户名称
             */
            export type CustomerName = string;
            /**
             * 结束市
             */
            export type EndCity = string;
            /**
             * 结束省
             */
            export type EndProvince = string;
            /**
             * 历史还是实时订单
             */
            export type History = "y" | "n";
            /**
             * 不等于运单状态：待装货：wait_load；配送中：delivery；待支付：wait_pay；已完成：finish；已取消：cancel；
             */
            export type NotEqualTransportStatus = "wait_load" | "delivery" | "wait_pay" | "finish" | "cancel";
            /**
             * 是货主还是驾驶员
             */
            export type OperateUserType = "customer" | "driver";
            /**
             * 结束时间-查询订单时间
             */
            export type OrderEndTime = string; // date-time
            /**
             * 订单号
             */
            export type OrderNumber = string;
            /**
             * 开始时间-查询订单时间
             */
            export type OrderStartTime = string; // date-time
            /**
             * 订单状态：待接单：not；已接单：yes；已取消：cancel；
             */
            export type OrderStatus = "not" | "yes" | "cancel";
            export type PageNo = number; // int32
            export type PageSize = number; // int32
            /**
             * 是否约单
             */
            export type PlanOrder = "y" | "n";
            /**
             * 发货地址
             */
            export type SendAddress = string;
            /**
             * 发货人手机号
             */
            export type SendPhone = string;
            /**
             * 是否拆单合单：是：y；否：n；
             */
            export type SplitOrder = "y" | "n";
            /**
             * 开始市
             */
            export type StartCity = string;
            /**
             * 开始省
             */
            export type StartProvince = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            /**
             * 运输车辆驾驶员ID
             */
            export type TransportCarDriverId = string;
            /**
             * 驾驶员ID不为空
             */
            export type TransportCarDriverIdIsNotNull = "y" | "n";
            /**
             * 运单号
             */
            export type TransportNumber = string;
            /**
             * 运单状态：待装货：wait_load；配送中：delivery；待支付：wait_pay；已完成：finish；已取消：cancel；
             */
            export type TransportStatus = "wait_load" | "delivery" | "wait_pay" | "finish" | "cancel";
            /**
             * 运单编号或车牌号
             */
            export type WaybillNumberOrCarNumber = string;
        }
        export interface QueryParameters {
            customerId?: /* 客户ID */ Parameters.CustomerId;
            customerName?: /* 客户名称 */ Parameters.CustomerName;
            orderStartTime?: /* 开始时间-查询订单时间 */ Parameters.OrderStartTime /* date-time */;
            orderEndTime?: /* 结束时间-查询订单时间 */ Parameters.OrderEndTime /* date-time */;
            transportNumber?: /* 运单号 */ Parameters.TransportNumber;
            orderNumber?: /* 订单号 */ Parameters.OrderNumber;
            auditStatus?: /* 审核状态：待审核：not；已通过：yes；已驳回：back； */ Parameters.AuditStatus;
            orderStatus?: /* 订单状态：待接单：not；已接单：yes；已取消：cancel； */ Parameters.OrderStatus;
            transportStatus?: /* 运单状态：待装货：wait_load；配送中：delivery；待支付：wait_pay；已完成：finish；已取消：cancel； */ Parameters.TransportStatus;
            notEqualTransportStatus?: /* 不等于运单状态：待装货：wait_load；配送中：delivery；待支付：wait_pay；已完成：finish；已取消：cancel； */ Parameters.NotEqualTransportStatus;
            transportCarDriverId?: /* 运输车辆驾驶员ID */ Parameters.TransportCarDriverId;
            sendPhone?: /* 发货人手机号 */ Parameters.SendPhone;
            startProvince?: /* 开始省 */ Parameters.StartProvince;
            startCity?: /* 开始市 */ Parameters.StartCity;
            sendAddress?: /* 发货地址 */ Parameters.SendAddress;
            endProvince?: /* 结束省 */ Parameters.EndProvince;
            endCity?: /* 结束市 */ Parameters.EndCity;
            carModelId?: /* 车型ID */ Parameters.CarModelId /* int64 */;
            carLengthId?: /* 车长ID */ Parameters.CarLengthId /* int64 */;
            carRouteType?: /* 运输类型：整车：whole_car；零担：less_car */ Parameters.CarRouteType;
            carType?: /* 运输车辆类型：normal：常规车；cold：冷藏车 */ Parameters.CarType;
            operateUserType?: /* 是货主还是驾驶员 */ Parameters.OperateUserType;
            planOrder?: /* 是否约单 */ Parameters.PlanOrder;
            splitOrder?: /* 是否拆单合单：是：y；否：n； */ Parameters.SplitOrder;
            transportCarDriverIdIsNotNull?: /* 驾驶员ID不为空 */ Parameters.TransportCarDriverIdIsNotNull;
            pageNo?: Parameters.PageNo /* int32 */;
            pageSize?: Parameters.PageSize /* int32 */;
            history?: /* 历史还是实时订单 */ Parameters.History;
            waybillNumberOrCarNumber?: /* 运单编号或车牌号 */ Parameters.WaybillNumberOrCarNumber;
        }
    }
    namespace QueryTransportOrderErrorRecord {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 资产ID
             */
            export type AssetsId = string;
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 车牌号码
             */
            export type CarNumber = string;
            /**
             * 客户ID
             */
            export type CustomerId = number; // int64
            /**
             * 异常类型：无响应：response_error；定位距离跳变：location_error；
             */
            export type ErrorType = "response_error" | "location_error";
            export type PageNo = number; // int32
            export type PageSize = number; // int32
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            carNumber?: /* 车牌号码 */ Parameters.CarNumber;
            assetsId?: /* 资产ID */ Parameters.AssetsId;
            customerId?: /* 客户ID */ Parameters.CustomerId /* int64 */;
            errorType?: /* 异常类型：无响应：response_error；定位距离跳变：location_error； */ Parameters.ErrorType;
            pageNo?: Parameters.PageNo /* int32 */;
            pageSize?: Parameters.PageSize /* int32 */;
        }
    }
    namespace QueryTransportOrderWarnRecord {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 资产ID
             */
            export type AssetsId = string;
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 车牌号码
             */
            export type CarNumber = string;
            /**
             * 客户ID
             */
            export type CustomerId = number; // int64
            export type PageNo = number; // int32
            export type PageSize = number; // int32
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            /**
             * 告警等级：初级预警：level1；中级预警：level2；高级预警：level3
             */
            export type WarnLevel = "level1" | "level2" | "level3";
        }
        export interface QueryParameters {
            carNumber?: /* 车牌号码 */ Parameters.CarNumber;
            assetsId?: /* 资产ID */ Parameters.AssetsId;
            customerId?: /* 客户ID */ Parameters.CustomerId /* int64 */;
            warnLevel?: /* 告警等级：初级预警：level1；中级预警：level2；高级预警：level3 */ Parameters.WarnLevel;
            pageNo?: Parameters.PageNo /* int32 */;
            pageSize?: Parameters.PageSize /* int32 */;
        }
    }
    namespace QueryTransportSchemeConfig {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type PageNo = number; // int32
            export type PageSize = number; // int32
            export type Status = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            status: Parameters.Status;
            pageNo: Parameters.PageNo /* int32 */;
            pageSize: Parameters.PageSize /* int32 */;
        }
    }
    namespace QueryWarnConfig {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type PageNo = number; // int32
            export type PageSize = number; // int32
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            pageNo: Parameters.PageNo /* int32 */;
            pageSize: Parameters.PageSize /* int32 */;
        }
    }
    namespace RegisterCustomer {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 经营地址
             */
            export type Address = string;
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 联系人名称
             */
            export type ContactsName = string;
            /**
             * 联系人手机号码
             */
            export type ContactsPhone = string;
            /**
             * 客户来源：IOS客户端：ios；安卓客户端：android；小程序：wechat；管理员添加：admin；
             */
            export type CustomerSource = "ios" | "android" | "wechat" | "admin";
            /**
             * 法人身份证
             */
            export type IdCard = string;
            /**
             * 法人名称
             */
            export type LegalPerson = string;
            /**
             * 客户名称
             */
            export type Name = string;
            /**
             * 密码
             */
            export type Password = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            /**
             * 客户身份：企业：company；个人：personal；
             */
            export type Type = "company" | "personal";
        }
        export interface QueryParameters {
            type: /* 客户身份：企业：company；个人：personal； */ Parameters.Type;
            contactsName: /* 联系人名称 */ Parameters.ContactsName;
            contactsPhone: /* 联系人手机号码 */ Parameters.ContactsPhone;
            legalPerson?: /* 法人名称 */ Parameters.LegalPerson;
            idCard?: /* 法人身份证 */ Parameters.IdCard;
            name?: /* 客户名称 */ Parameters.Name;
            address?: /* 经营地址 */ Parameters.Address;
            password?: /* 密码 */ Parameters.Password;
            customerSource?: /* 客户来源：IOS客户端：ios；安卓客户端：android；小程序：wechat；管理员添加：admin； */ Parameters.CustomerSource;
        }
    }
    namespace RegisterDriver {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 车辆类别：A：A类；B：B类；C：C类；D：D类；E：E类；F：F类；G：G类；H：H类；
             */
            export type CarCategory = "A" | "B" | "C" | "D" | "E" | "F" | "G" | "H";
            /**
             * 车长
             */
            export type CarLength = string;
            /**
             * 车长ID
             */
            export type CarLengthId = number; // int64
            /**
             * 车型ID
             */
            export type CarModelId = number; // int64
            /**
             * 车型名称
             */
            export type CarModelName = string;
            /**
             * 车牌号
             */
            export type CarNumber = string;
            /**
             * 车辆类型：normal：常规车；cold：冷藏车
             */
            export type CarType = "normal" | "cold";
            /**
             * 数据来源：注册：register；运单录入：order；
             */
            export type DataSource = "register" | "order";
            /**
             * 驾驶证编号
             */
            export type DriverLicense = string;
            /**
             * 行驶证号
             */
            export type DriverNumber = string;
            /**
             * 身份证号
             */
            export type IdCard = string;
            /**
             * 姓名
             */
            export type Name = string;
            /**
             * 密码
             */
            export type Password = string;
            /**
             * 手机号
             */
            export type Phone = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            name: /* 姓名 */ Parameters.Name;
            phone: /* 手机号 */ Parameters.Phone;
            idCard?: /* 身份证号 */ Parameters.IdCard;
            driverLicense?: /* 驾驶证编号 */ Parameters.DriverLicense;
            carNumber: /* 车牌号 */ Parameters.CarNumber;
            carType?: /* 车辆类型：normal：常规车；cold：冷藏车 */ Parameters.CarType;
            carCategory?: /* 车辆类别：A：A类；B：B类；C：C类；D：D类；E：E类；F：F类；G：G类；H：H类； */ Parameters.CarCategory;
            carModelId?: /* 车型ID */ Parameters.CarModelId /* int64 */;
            carModelName?: /* 车型名称 */ Parameters.CarModelName;
            carLength?: /* 车长 */ Parameters.CarLength;
            carLengthId?: /* 车长ID */ Parameters.CarLengthId /* int64 */;
            driverNumber?: /* 行驶证号 */ Parameters.DriverNumber;
            dataSource?: /* 数据来源：注册：register；运单录入：order； */ Parameters.DataSource;
            password?: /* 密码 */ Parameters.Password;
        }
    }
    namespace RelationColdSensor {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 感应器ID
             */
            export type AssetsId = string;
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 安装位置
             */
            export type InstallPosition = string;
            /**
             * 订单ID
             */
            export type OrderId = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            /**
             * 使用场景：冷库：storage；冷藏车：car；
             */
            export type UsageType = "storage" | "car";
        }
        export interface QueryParameters {
            orderId: /* 订单ID */ Parameters.OrderId /* int64 */;
            assetsId: /* 感应器ID */ Parameters.AssetsId;
            usageType?: /* 使用场景：冷库：storage；冷藏车：car； */ Parameters.UsageType;
            installPosition?: /* 安装位置 */ Parameters.InstallPosition;
        }
    }
    namespace SaveAsset {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 数据上传周期（分钟）
             */
            export type ActiveWaitTime = number; // int32
            export type AssetsId = string;
            export type AssetsModel = string;
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type CreateTime = string; // date-time
            export type Creator = string;
            export type Deleted = boolean;
            export type DeptId = number; // int64
            export type FactoryId = number; // int64
            /**
             * 湿度校准值
             */
            export type HumCalibration = number; // float
            export type Id = number; // int64
            export type Imei = string;
            /**
             * 温湿度采样周期（分钟）
             */
            export type IntervalTime = number; // int32
            export type LastUpdateTime = string; // date-time
            export type Remark = string;
            export type SimNo = string;
            export type Ssid = string;
            export type Status = string;
            /**
             * 温度校准值
             */
            export type TempCalibration = number; // float
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            export type UpdateTime = string; // date-time
            export type Updater = string;
        }
        export interface QueryParameters {
            activeWaitTime?: /* 数据上传周期（分钟） */ Parameters.ActiveWaitTime /* int32 */;
            intervalTime?: /* 温湿度采样周期（分钟） */ Parameters.IntervalTime /* int32 */;
            tempCalibration?: /* 温度校准值 */ Parameters.TempCalibration /* float */;
            humCalibration?: /* 湿度校准值 */ Parameters.HumCalibration /* float */;
            id?: Parameters.Id /* int64 */;
            assetsId?: Parameters.AssetsId;
            factoryId?: Parameters.FactoryId /* int64 */;
            assetsModel?: Parameters.AssetsModel;
            ssid?: Parameters.Ssid;
            imei?: Parameters.Imei;
            simNo?: Parameters.SimNo;
            status?: Parameters.Status;
            remark?: Parameters.Remark;
            lastUpdateTime?: Parameters.LastUpdateTime /* date-time */;
            deptId?: Parameters.DeptId /* int64 */;
            createTime?: Parameters.CreateTime /* date-time */;
            updateTime?: Parameters.UpdateTime /* date-time */;
            creator?: Parameters.Creator;
            updater?: Parameters.Updater;
            deleted?: Parameters.Deleted;
        }
    }
    namespace SaveAssetsSalePlan {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 资产型号
             */
            export type AssetModel = string;
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 文件列表
             */
            export type FileList = /* 文件列表 */ ServiceTypeCcms.Schemas.AssetsSalePlanFileDO[];
            /**
             * 主键
             */
            export type Id = number; // int64
            /**
             * 租赁单价
             */
            export type LeasePrice = number;
            /**
             * 最少租赁时长
             */
            export type MinLeaseTime = number; // int32
            /**
             * 方案名称
             */
            export type Name = string;
            /**
             * 适用湿度
             */
            export type SafeHumidity = string;
            /**
             * 适用温度
             */
            export type SafeTemp = string;
            /**
             * 销售单价
             */
            export type SalePrice = number;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id?: /* 主键 */ Parameters.Id /* int64 */;
            name?: /* 方案名称 */ Parameters.Name;
            assetModel?: /* 资产型号 */ Parameters.AssetModel;
            safeTemp?: /* 适用温度 */ Parameters.SafeTemp;
            safeHumidity?: /* 适用湿度 */ Parameters.SafeHumidity;
            minLeaseTime?: /* 最少租赁时长 */ Parameters.MinLeaseTime /* int32 */;
            leasePrice?: /* 租赁单价 */ Parameters.LeasePrice;
            salePrice?: /* 销售单价 */ Parameters.SalePrice;
            fileList?: /* 文件列表 */ Parameters.FileList;
        }
    }
    namespace SaveBusinessRule {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 配置列表
             */
            export type ConfigTypeList = /* 配置列表 */ ServiceTypeCcms.Schemas.BusinessRuleConfigDO[];
            /**
             * ID
             */
            export type Id = number; // int64
            /**
             * 规则名称
             */
            export type Name = string;
            /**
             * 备注
             */
            export type Remark = string;
            /**
             * 状态:启用：y；停用：n；
             */
            export type Status = "y" | "n" | "del";
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            /**
             * 用户类型：客户：customer；
             */
            export type UserType = "customer" | "common" | "ssoAdmin";
        }
        export interface QueryParameters {
            id?: /* ID */ Parameters.Id /* int64 */;
            name: /* 规则名称 */ Parameters.Name;
            userType: /* 用户类型：客户：customer； */ Parameters.UserType;
            remark?: /* 备注 */ Parameters.Remark;
            status: /* 状态:启用：y；停用：n； */ Parameters.Status;
            configTypeList: /* 配置列表 */ Parameters.ConfigTypeList;
        }
    }
    namespace SaveCar {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 车辆类别：A：A类；B：B类；C：C类；D：D类；E：E类；F：F类；G：G类；H：H类；
             */
            export type CarCategory = string;
            /**
             * 车长
             */
            export type CarLength = string;
            /**
             * 车长ID
             */
            export type CarLengthId = number; // int64
            /**
             * 车型ID
             */
            export type CarModelId = number; // int64
            /**
             * 车型名称
             */
            export type CarModelName = string;
            /**
             * 车牌号
             */
            export type CarNumber = string;
            /**
             * 车辆类型：normal：常规车；cold：冷藏车
             */
            export type CarType = string;
            /**
             * 所属承运商ID
             */
            export type CarrierId = string;
            /**
             * 驾驶员ID
             */
            export type DriverId = string;
            /**
             * 行驶证号
             */
            export type DriverNumber = string;
            /**
             * 文件信息
             */
            export type Files = ServiceTypeCcms.Schemas.FileDO[];
            /**
             * 主键
             */
            export type Id = string;
            /**
             * 最大载重
             */
            export type MaximumPayload = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            /**
             * 运力类型：个人：person；自营：self；承运商：carrier；
             */
            export type Type = string;
        }
        export interface QueryParameters {
            id?: /* 主键 */ Parameters.Id;
            carNumber?: /* 车牌号 */ Parameters.CarNumber;
            driverNumber?: /* 行驶证号 */ Parameters.DriverNumber;
            files?: /* 文件信息 */ Parameters.Files;
            carType?: /* 车辆类型：normal：常规车；cold：冷藏车 */ Parameters.CarType;
            carModelId?: /* 车型ID */ Parameters.CarModelId /* int64 */;
            carModelName?: /* 车型名称 */ Parameters.CarModelName;
            carLength?: /* 车长 */ Parameters.CarLength;
            carLengthId?: /* 车长ID */ Parameters.CarLengthId /* int64 */;
            maximumPayload?: /* 最大载重 */ Parameters.MaximumPayload;
            carCategory?: /* 车辆类别：A：A类；B：B类；C：C类；D：D类；E：E类；F：F类；G：G类；H：H类； */ Parameters.CarCategory;
            type?: /* 运力类型：个人：person；自营：self；承运商：carrier； */ Parameters.Type;
            carrierId?: /* 所属承运商ID */ Parameters.CarrierId;
            driverId?: /* 驾驶员ID */ Parameters.DriverId;
        }
    }
    namespace SaveCarInquire {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type Contacts = string;
            export type ContactsAddress = string;
            export type ContactsPhone = string;
            export type CreateTime = string; // date-time
            export type Creator = string;
            export type Deleted = boolean;
            export type DeptId = number; // int64
            export type Id = number; // int64
            export type Remark = string;
            export type ShopName = string;
            export type Status = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            export type UpdateTime = string; // date-time
            export type Updater = string;
        }
        export interface QueryParameters {
            id?: Parameters.Id /* int64 */;
            shopName?: Parameters.ShopName;
            contacts?: Parameters.Contacts;
            contactsPhone?: Parameters.ContactsPhone;
            contactsAddress?: Parameters.ContactsAddress;
            remark?: Parameters.Remark;
            status?: Parameters.Status;
            deptId?: Parameters.DeptId /* int64 */;
            createTime?: Parameters.CreateTime /* date-time */;
            updateTime?: Parameters.UpdateTime /* date-time */;
            creator?: Parameters.Creator;
            updater?: Parameters.Updater;
            deleted?: Parameters.Deleted;
        }
    }
    namespace SaveCarInsure {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 品牌型号
             */
            export type BrandModel = string;
            /**
             * 车主名称
             */
            export type CarMasterName = string;
            /**
             * 车牌号
             */
            export type CarNumber = string;
            /**
             * 电子报单
             */
            export type FileList = ServiceTypeCcms.Schemas.FileDO[];
            /**
             * id
             */
            export type Id = number; // int64
            /**
             * 保障结束时间
             */
            export type InsureEndTime = string; // date-time
            /**
             * 保单号码
             */
            export type InsureNumber = string;
            /**
             * 保障开始时间
             */
            export type InsureStartTime = string; // date-time
            /**
             * 保单类型：交强险：compulsory；商业险：business；
             */
            export type InsureType = "compulsory" | "business";
            /**
             * 手机号码
             */
            export type Phone = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            /**
             * 车架号
             */
            export type VinNo = string;
        }
        export interface QueryParameters {
            id?: /* id */ Parameters.Id /* int64 */;
            insureNumber: /* 保单号码 */ Parameters.InsureNumber;
            insureType: /* 保单类型：交强险：compulsory；商业险：business； */ Parameters.InsureType;
            carNumber: /* 车牌号 */ Parameters.CarNumber;
            insureStartTime: /* 保障开始时间 */ Parameters.InsureStartTime /* date-time */;
            insureEndTime: /* 保障结束时间 */ Parameters.InsureEndTime /* date-time */;
            vinNo: /* 车架号 */ Parameters.VinNo;
            brandModel: /* 品牌型号 */ Parameters.BrandModel;
            carMasterName: /* 车主名称 */ Parameters.CarMasterName;
            phone: /* 手机号码 */ Parameters.Phone;
            fileList: /* 电子报单 */ Parameters.FileList;
        }
    }
    namespace SaveCarRepairForm {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 车牌号
             */
            export type CarNumber = string;
            /**
             * 电子报单
             */
            export type FileList = ServiceTypeCcms.Schemas.FileDO[];
            /**
             * 维修厂联系人
             */
            export type FixName = string;
            /**
             * 维修厂手机号码
             */
            export type FixPhone = string;
            /**
             * 维修总价
             */
            export type FixTotalPrice = number;
            /**
             * 报单编号
             */
            export type FormNumber = string;
            /**
             * id
             */
            export type Id = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            /**
             * 车架号
             */
            export type VinNo = string;
        }
        export interface QueryParameters {
            id?: /* id */ Parameters.Id /* int64 */;
            formNumber?: /* 报单编号 */ Parameters.FormNumber;
            carNumber?: /* 车牌号 */ Parameters.CarNumber;
            vinNo?: /* 车架号 */ Parameters.VinNo;
            fixTotalPrice?: /* 维修总价 */ Parameters.FixTotalPrice;
            fixName?: /* 维修厂联系人 */ Parameters.FixName;
            fixPhone?: /* 维修厂手机号码 */ Parameters.FixPhone;
            fileList?: /* 电子报单 */ Parameters.FileList;
        }
    }
    namespace SaveCarRoute {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 运输类型：整车：whole_car；零担：less_car
             */
            export type CarType = "whole_car" | "less_car";
            /**
             * 驾驶员ID
             */
            export type DriverId = number; // int64
            /**
             * 结束市
             */
            export type EndCity = string;
            /**
             * 结束省
             */
            export type EndProvince = string;
            /**
             * ID
             */
            export type Id = number; // int64
            /**
             * 定点路线详情列表
             */
            export type List = /* 定点路线详情列表 */ ServiceTypeCcms.Schemas.FixedPointRouteDetailDO[];
            /**
             * 备注
             */
            export type Remark = string;
            /**
             * 路线名称
             */
            export type RouteName = string;
            /**
             * 开始市
             */
            export type StartCity = string;
            /**
             * 开始省
             */
            export type StartProvince = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id?: /* ID */ Parameters.Id /* int64 */;
            routeName?: /* 路线名称 */ Parameters.RouteName;
            remark?: /* 备注 */ Parameters.Remark;
            list?: /* 定点路线详情列表 */ Parameters.List;
        }
    }
    namespace SaveDamage {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            export type AccidentAddress = string;
            export type AccidentCarModel = string;
            export type AccidentCarNumber = string;
            export type AccidentCarType = string;
            export type AccidentForm = string;
            export type AccidentHighwayGovLevel = string;
            export type AccidentHighwayItLevel = string;
            export type AccidentHighwaySection = string;
            export type AccidentLevel = string;
            export type AccidentReason = string;
            export type AccidentReasonAnalysis = string;
            export type AccidentTime = string; // date-time
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type Content = string;
            export type CreateTime = string; // date-time
            export type Creator = string;
            export type DamageLevel = string;
            export type DealTime = string; // date-time
            export type DealType = string;
            export type DealUserId = number; // int64
            export type DeclareTime = string; // date-time
            export type Deleted = boolean;
            export type DeptId = number; // int64
            export type DriverName = string;
            export type DriverNumber = string;
            export type DriverPhone = string;
            export type DrivingNumber = string;
            export type FaultLevel = string;
            export type FaultRemark = string;
            /**
             * 文件信息
             */
            export type Files = /* 文件信息 */ ServiceTypeCcms.Schemas.AccidentFaultDamageReportFileDO[];
            export type Id = number; // int64
            export type MainGoods = string;
            export type OrderId = number; // int64
            export type ReductionMoney = number;
            export type ReportType = string;
            export type Status = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            /**
             * 运输费用
             */
            export type TotalMoney = number;
            export type UpdateTime = string; // date-time
            export type Updater = string;
            export type WeatherCondition = string;
        }
        export interface QueryParameters {
            totalMoney?: /* 运输费用 */ Parameters.TotalMoney;
            files?: /* 文件信息 */ Parameters.Files;
            id?: Parameters.Id /* int64 */;
            orderId?: Parameters.OrderId /* int64 */;
            reportType?: Parameters.ReportType;
            accidentLevel?: Parameters.AccidentLevel;
            accidentForm?: Parameters.AccidentForm;
            accidentTime?: Parameters.AccidentTime /* date-time */;
            accidentAddress?: Parameters.AccidentAddress;
            weatherCondition?: Parameters.WeatherCondition;
            accidentHighwayItLevel?: Parameters.AccidentHighwayItLevel;
            accidentHighwayGovLevel?: Parameters.AccidentHighwayGovLevel;
            accidentHighwaySection?: Parameters.AccidentHighwaySection;
            accidentReason?: Parameters.AccidentReason;
            accidentCarNumber?: Parameters.AccidentCarNumber;
            accidentCarType?: Parameters.AccidentCarType;
            accidentCarModel?: Parameters.AccidentCarModel;
            mainGoods?: Parameters.MainGoods;
            driverName?: Parameters.DriverName;
            driverNumber?: Parameters.DriverNumber;
            accidentReasonAnalysis?: Parameters.AccidentReasonAnalysis;
            driverPhone?: Parameters.DriverPhone;
            drivingNumber?: Parameters.DrivingNumber;
            faultLevel?: Parameters.FaultLevel;
            faultRemark?: Parameters.FaultRemark;
            damageLevel?: Parameters.DamageLevel;
            status?: Parameters.Status;
            declareTime?: Parameters.DeclareTime /* date-time */;
            content?: Parameters.Content;
            dealType?: Parameters.DealType;
            reductionMoney?: Parameters.ReductionMoney;
            dealUserId?: Parameters.DealUserId /* int64 */;
            dealTime?: Parameters.DealTime /* date-time */;
            deptId?: Parameters.DeptId /* int64 */;
            createTime?: Parameters.CreateTime /* date-time */;
            updateTime?: Parameters.UpdateTime /* date-time */;
            creator?: Parameters.Creator;
            updater?: Parameters.Updater;
            deleted?: Parameters.Deleted;
        }
    }
    namespace SaveErrorConfig {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * ID
             */
            export type Id = number; // int64
            /**
             * 定位距离跳变（千米）
             */
            export type LocationError = string;
            /**
             * 无响应时间（分钟）
             */
            export type ResponseError = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id?: /* ID */ Parameters.Id /* int64 */;
            responseError?: /* 无响应时间（分钟） */ Parameters.ResponseError;
            locationError?: /* 定位距离跳变（千米） */ Parameters.LocationError;
        }
    }
    namespace SaveExpenseTime {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type CreateTime = string; // date-time
            export type Creator = string;
            export type Deleted = boolean;
            export type DeptId = number; // int64
            export type EndTime = string; // date-time
            export type Id = number; // int64
            export type Name = string;
            export type Remark = string;
            export type StartTime = string; // date-time
            export type Status = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            export type UpdateTime = string; // date-time
            export type Updater = string;
        }
        export interface QueryParameters {
            id?: Parameters.Id /* int64 */;
            name?: Parameters.Name;
            startTime?: Parameters.StartTime /* date-time */;
            endTime?: Parameters.EndTime /* date-time */;
            status?: Parameters.Status;
            remark?: Parameters.Remark;
            deptId?: Parameters.DeptId /* int64 */;
            createTime?: Parameters.CreateTime /* date-time */;
            updateTime?: Parameters.UpdateTime /* date-time */;
            creator?: Parameters.Creator;
            updater?: Parameters.Updater;
            deleted?: Parameters.Deleted;
        }
    }
    namespace SaveFactoryManage {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            export type Address = string;
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type ContactsName = string;
            export type CreateTime = string; // date-time
            export type Creator = string;
            export type Deleted = boolean;
            export type DeptId = number; // int64
            export type Id = number; // int64
            export type Name = string;
            export type Phone = string;
            export type Remark = string;
            export type Status = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            export type UpdateTime = string; // date-time
            export type Updater = string;
        }
        export interface QueryParameters {
            id?: Parameters.Id /* int64 */;
            name?: Parameters.Name;
            contactsName?: Parameters.ContactsName;
            phone?: Parameters.Phone;
            address?: Parameters.Address;
            status?: Parameters.Status;
            remark?: Parameters.Remark;
            deptId?: Parameters.DeptId /* int64 */;
            createTime?: Parameters.CreateTime /* date-time */;
            updateTime?: Parameters.UpdateTime /* date-time */;
            creator?: Parameters.Creator;
            updater?: Parameters.Updater;
            deleted?: Parameters.Deleted;
        }
    }
    namespace SaveFault {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            export type AccidentAddress = string;
            export type AccidentCarModel = string;
            export type AccidentCarNumber = string;
            export type AccidentCarType = string;
            export type AccidentForm = string;
            export type AccidentHighwayGovLevel = string;
            export type AccidentHighwayItLevel = string;
            export type AccidentHighwaySection = string;
            export type AccidentLevel = string;
            export type AccidentReason = string;
            export type AccidentReasonAnalysis = string;
            export type AccidentTime = string; // date-time
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type Content = string;
            export type CreateTime = string; // date-time
            export type Creator = string;
            export type DamageLevel = string;
            export type DealTime = string; // date-time
            export type DealType = string;
            export type DealUserId = number; // int64
            export type DeclareTime = string; // date-time
            export type Deleted = boolean;
            export type DeptId = number; // int64
            export type DriverName = string;
            export type DriverNumber = string;
            export type DriverPhone = string;
            export type DrivingNumber = string;
            export type FaultLevel = string;
            export type FaultRemark = string;
            /**
             * 文件信息
             */
            export type Files = /* 文件信息 */ ServiceTypeCcms.Schemas.AccidentFaultDamageReportFileDO[];
            export type Id = number; // int64
            export type MainGoods = string;
            export type OrderId = number; // int64
            export type ReductionMoney = number;
            export type ReportType = string;
            export type Status = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            /**
             * 运输费用
             */
            export type TotalMoney = number;
            export type UpdateTime = string; // date-time
            export type Updater = string;
            export type WeatherCondition = string;
        }
        export interface QueryParameters {
            totalMoney?: /* 运输费用 */ Parameters.TotalMoney;
            files?: /* 文件信息 */ Parameters.Files;
            id?: Parameters.Id /* int64 */;
            orderId?: Parameters.OrderId /* int64 */;
            reportType?: Parameters.ReportType;
            accidentLevel?: Parameters.AccidentLevel;
            accidentForm?: Parameters.AccidentForm;
            accidentTime?: Parameters.AccidentTime /* date-time */;
            accidentAddress?: Parameters.AccidentAddress;
            weatherCondition?: Parameters.WeatherCondition;
            accidentHighwayItLevel?: Parameters.AccidentHighwayItLevel;
            accidentHighwayGovLevel?: Parameters.AccidentHighwayGovLevel;
            accidentHighwaySection?: Parameters.AccidentHighwaySection;
            accidentReason?: Parameters.AccidentReason;
            accidentCarNumber?: Parameters.AccidentCarNumber;
            accidentCarType?: Parameters.AccidentCarType;
            accidentCarModel?: Parameters.AccidentCarModel;
            mainGoods?: Parameters.MainGoods;
            driverName?: Parameters.DriverName;
            driverNumber?: Parameters.DriverNumber;
            accidentReasonAnalysis?: Parameters.AccidentReasonAnalysis;
            driverPhone?: Parameters.DriverPhone;
            drivingNumber?: Parameters.DrivingNumber;
            faultLevel?: Parameters.FaultLevel;
            faultRemark?: Parameters.FaultRemark;
            damageLevel?: Parameters.DamageLevel;
            status?: Parameters.Status;
            declareTime?: Parameters.DeclareTime /* date-time */;
            content?: Parameters.Content;
            dealType?: Parameters.DealType;
            reductionMoney?: Parameters.ReductionMoney;
            dealUserId?: Parameters.DealUserId /* int64 */;
            dealTime?: Parameters.DealTime /* date-time */;
            deptId?: Parameters.DeptId /* int64 */;
            createTime?: Parameters.CreateTime /* date-time */;
            updateTime?: Parameters.UpdateTime /* date-time */;
            creator?: Parameters.Creator;
            updater?: Parameters.Updater;
            deleted?: Parameters.Deleted;
        }
    }
    namespace SaveIncidentAccidentFaultDamageReport {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            export type AccidentAddress = string;
            export type AccidentCarModel = string;
            export type AccidentCarNumber = string;
            export type AccidentCarType = string;
            export type AccidentForm = string;
            export type AccidentHighwayGovLevel = string;
            export type AccidentHighwayItLevel = string;
            export type AccidentHighwaySection = string;
            export type AccidentLevel = string;
            export type AccidentReason = string;
            export type AccidentReasonAnalysis = string;
            export type AccidentTime = string; // date-time
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type Content = string;
            export type CreateTime = string; // date-time
            export type Creator = string;
            export type DamageLevel = string;
            export type DealTime = string; // date-time
            export type DealType = string;
            export type DealUserId = number; // int64
            export type DeclareTime = string; // date-time
            export type Deleted = boolean;
            export type DeptId = number; // int64
            export type DriverName = string;
            export type DriverNumber = string;
            export type DriverPhone = string;
            export type DrivingNumber = string;
            export type FaultLevel = string;
            export type FaultRemark = string;
            /**
             * 文件信息
             */
            export type Files = /* 文件信息 */ ServiceTypeCcms.Schemas.AccidentFaultDamageReportFileDO[];
            export type Id = number; // int64
            export type MainGoods = string;
            export type OrderId = number; // int64
            export type ReductionMoney = number;
            export type ReportType = string;
            export type Status = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            /**
             * 运输费用
             */
            export type TotalMoney = number;
            export type UpdateTime = string; // date-time
            export type Updater = string;
            export type WeatherCondition = string;
        }
        export interface QueryParameters {
            totalMoney?: /* 运输费用 */ Parameters.TotalMoney;
            files?: /* 文件信息 */ Parameters.Files;
            id?: Parameters.Id /* int64 */;
            orderId?: Parameters.OrderId /* int64 */;
            reportType?: Parameters.ReportType;
            accidentLevel?: Parameters.AccidentLevel;
            accidentForm?: Parameters.AccidentForm;
            accidentTime?: Parameters.AccidentTime /* date-time */;
            accidentAddress?: Parameters.AccidentAddress;
            weatherCondition?: Parameters.WeatherCondition;
            accidentHighwayItLevel?: Parameters.AccidentHighwayItLevel;
            accidentHighwayGovLevel?: Parameters.AccidentHighwayGovLevel;
            accidentHighwaySection?: Parameters.AccidentHighwaySection;
            accidentReason?: Parameters.AccidentReason;
            accidentCarNumber?: Parameters.AccidentCarNumber;
            accidentCarType?: Parameters.AccidentCarType;
            accidentCarModel?: Parameters.AccidentCarModel;
            mainGoods?: Parameters.MainGoods;
            driverName?: Parameters.DriverName;
            driverNumber?: Parameters.DriverNumber;
            accidentReasonAnalysis?: Parameters.AccidentReasonAnalysis;
            driverPhone?: Parameters.DriverPhone;
            drivingNumber?: Parameters.DrivingNumber;
            faultLevel?: Parameters.FaultLevel;
            faultRemark?: Parameters.FaultRemark;
            damageLevel?: Parameters.DamageLevel;
            status?: Parameters.Status;
            declareTime?: Parameters.DeclareTime /* date-time */;
            content?: Parameters.Content;
            dealType?: Parameters.DealType;
            reductionMoney?: Parameters.ReductionMoney;
            dealUserId?: Parameters.DealUserId /* int64 */;
            dealTime?: Parameters.DealTime /* date-time */;
            deptId?: Parameters.DeptId /* int64 */;
            createTime?: Parameters.CreateTime /* date-time */;
            updateTime?: Parameters.UpdateTime /* date-time */;
            creator?: Parameters.Creator;
            updater?: Parameters.Updater;
            deleted?: Parameters.Deleted;
        }
    }
    namespace SaveLoadingStrategyConfig {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 排斥商品
             */
            export type ExcludeGoods = string;
            /**
             * ID
             */
            export type Id = number; // int64
            /**
             * 兼容商品
             */
            export type IncludeGoods = string;
            /**
             * 名称
             */
            export type Name = string;
            /**
             * 备注
             */
            export type Remark = string;
            /**
             * 状态:启用：y；停用：n；
             */
            export type Status = "y" | "n" | "del";
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id?: /* ID */ Parameters.Id /* int64 */;
            name: /* 名称 */ Parameters.Name;
            remark?: /* 备注 */ Parameters.Remark;
            includeGoods: /* 兼容商品 */ Parameters.IncludeGoods;
            excludeGoods: /* 排斥商品 */ Parameters.ExcludeGoods;
            status: /* 状态:启用：y；停用：n； */ Parameters.Status;
        }
    }
    namespace SaveSalesOrders {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            export type AcceptAddress = string;
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type ContactsName = string;
            export type ContactsPhone = string;
            export type CreateTime = string; // date-time
            export type Creator = string;
            export type CustomerId = number; // int64
            export type CustomerName = string;
            export type Deleted = boolean;
            export type DeptId = number; // int64
            export type FastMailName = string;
            export type FastMailNumber = string;
            export type Id = number; // int64
            export type LeaseEndTime = string; // date-time
            export type LeaseStartTime = string; // date-time
            export type LeaseTime = number; // int32
            export type Number = number; // int32
            export type OrderTime = string; // date-time
            export type Price = number;
            export type Remark = string;
            export type SaleConfigId = number; // int64
            export type SaleType = string;
            export type Status = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            export type TotalPrice = number;
            export type UpdateTime = string; // date-time
            export type Updater = string;
        }
        export interface QueryParameters {
            id?: Parameters.Id /* int64 */;
            customerId?: Parameters.CustomerId /* int64 */;
            customerName?: Parameters.CustomerName;
            contactsName?: Parameters.ContactsName;
            contactsPhone?: Parameters.ContactsPhone;
            saleConfigId?: Parameters.SaleConfigId /* int64 */;
            saleType?: Parameters.SaleType;
            leaseTime?: Parameters.LeaseTime /* int32 */;
            leaseStartTime?: Parameters.LeaseStartTime /* date-time */;
            leaseEndTime?: Parameters.LeaseEndTime /* date-time */;
            number?: Parameters.Number /* int32 */;
            price?: Parameters.Price;
            totalPrice?: Parameters.TotalPrice;
            status?: Parameters.Status;
            remark?: Parameters.Remark;
            acceptAddress?: Parameters.AcceptAddress;
            fastMailName?: Parameters.FastMailName;
            fastMailNumber?: Parameters.FastMailNumber;
            orderTime?: Parameters.OrderTime /* date-time */;
            deptId?: Parameters.DeptId /* int64 */;
            createTime?: Parameters.CreateTime /* date-time */;
            updateTime?: Parameters.UpdateTime /* date-time */;
            creator?: Parameters.Creator;
            updater?: Parameters.Updater;
            deleted?: Parameters.Deleted;
        }
    }
    namespace SaveStaffCashSettlement {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type CreateTime = string; // date-time
            export type Creator = string;
            export type Deleted = boolean;
            export type DeptId = number; // int64
            export type Id = number; // int64
            /**
             * 订单ID
             */
            export type OrderId = number; // int64
            /**
             * 结算ID
             */
            export type SettleId = number; // int64
            /**
             * 结算状态：是：y；否：n；
             */
            export type SettleStatus = string;
            /**
             * 结算方式：现金：cash；转账：transfer
             */
            export type SettleType = string;
            /**
             * 结算信息详情
             */
            export type StaffCashSettlementList = /* 结算信息详情 */ ServiceTypeCcms.Schemas.StaffCashSettlementDetailDO[];
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            export type TotalMoney = number;
            export type UpdateTime = string; // date-time
            export type Updater = string;
        }
        export interface QueryParameters {
            orderId?: /* 订单ID */ Parameters.OrderId /* int64 */;
            settleId?: /* 结算ID */ Parameters.SettleId /* int64 */;
            settleType?: /* 结算方式：现金：cash；转账：transfer */ Parameters.SettleType;
            settleStatus?: /* 结算状态：是：y；否：n； */ Parameters.SettleStatus;
            staffCashSettlementList?: /* 结算信息详情 */ Parameters.StaffCashSettlementList;
            id?: Parameters.Id /* int64 */;
            totalMoney?: Parameters.TotalMoney;
            deptId?: Parameters.DeptId /* int64 */;
            createTime?: Parameters.CreateTime /* date-time */;
            updateTime?: Parameters.UpdateTime /* date-time */;
            creator?: Parameters.Creator;
            updater?: Parameters.Updater;
            deleted?: Parameters.Deleted;
        }
    }
    namespace SaveSystemConfig {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 收款账号
             */
            export type Account = string;
            /**
             * 收款账号类型:银行卡：bank；支付宝：alipay；微信：wechat；
             */
            export type AccountType = "bank" | "alipay" | "wechat";
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 银行名称
             */
            export type BankName = string;
            /**
             * 撤单时间限制
             */
            export type CancelLimitTime = number; // int32
            /**
             * 冷链设施采集频率
             */
            export type CollectFrequency = number; // int32
            /**
             * ID
             */
            export type Id = number; // int64
            /**
             * 企业名称
             */
            export type Name = string;
            /**
             * 开启预冷预热：开启预冷预热：开启：open；关闭：close；
             */
            export type OpenColdHot = string;
            /**
             * 冷链设施发送频率
             */
            export type SendFrequency = number; // int32
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id?: /* ID */ Parameters.Id /* int64 */;
            name?: /* 企业名称 */ Parameters.Name;
            accountType?: /* 收款账号类型:银行卡：bank；支付宝：alipay；微信：wechat； */ Parameters.AccountType;
            bankName?: /* 银行名称 */ Parameters.BankName;
            account?: /* 收款账号 */ Parameters.Account;
            collectFrequency?: /* 冷链设施采集频率 */ Parameters.CollectFrequency /* int32 */;
            sendFrequency?: /* 冷链设施发送频率 */ Parameters.SendFrequency /* int32 */;
            openColdHot?: /* 开启预冷预热：开启预冷预热：开启：open；关闭：close； */ Parameters.OpenColdHot;
            cancelLimitTime?: /* 撤单时间限制 */ Parameters.CancelLimitTime /* int32 */;
        }
    }
    namespace SaveTempRangConfig {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 车辆类别：A：A类；B：B类；C：C类；D：D类；E：E类；F：F类；G：G类；H：H类；
             */
            export type CarCategory = "A" | "B" | "C" | "D" | "E" | "F" | "G" | "H";
            /**
             * 车长ID
             */
            export type CarLengthId = number; // int64
            /**
             * 车型ID
             */
            export type CarModelId = number; // int64
            /**
             * 温区配置详情
             */
            export type DetailList = /* 温区配置详情 */ ServiceTypeCcms.Schemas.TempRangConfigDetailDO[];
            /**
             * ID
             */
            export type Id = number; // int64
            /**
             * 策略名称
             */
            export type Name = string;
            /**
             * 状态:启用：y；停用：n；
             */
            export type Status = "y" | "n" | "del";
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id?: /* ID */ Parameters.Id /* int64 */;
            name?: /* 策略名称 */ Parameters.Name;
            carModelId?: /* 车型ID */ Parameters.CarModelId /* int64 */;
            carLengthId?: /* 车长ID */ Parameters.CarLengthId /* int64 */;
            carCategory?: /* 车辆类别：A：A类；B：B类；C：C类；D：D类；E：E类；F：F类；G：G类；H：H类； */ Parameters.CarCategory;
            status?: /* 状态:启用：y；停用：n； */ Parameters.Status;
            detailList?: /* 温区配置详情 */ Parameters.DetailList;
        }
    }
    namespace SaveTransportSchemeConfig {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 货物类型ID
             */
            export type GoodsTypeId = number; // int64
            /**
             * 设定湿度
             */
            export type HumSet = string;
            /**
             * ID
             */
            export type Id = number; // int64
            /**
             * 名称
             */
            export type Name = string;
            /**
             * 定点路线ID
             */
            export type PointRouteId = number; // int64
            /**
             * 适用季节：春季：spring；夏季：summer；秋季：autumn；冬季：winter；
             */
            export type SeasonsType = string;
            /**
             * 状态:启用：y；停用：n；
             */
            export type Status = "y" | "n" | "del";
            /**
             * 设定温度
             */
            export type TempSet = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id?: /* ID */ Parameters.Id /* int64 */;
            name?: /* 名称 */ Parameters.Name;
            seasonsType?: /* 适用季节：春季：spring；夏季：summer；秋季：autumn；冬季：winter； */ Parameters.SeasonsType;
            pointRouteId?: /* 定点路线ID */ Parameters.PointRouteId /* int64 */;
            goodsTypeId?: /* 货物类型ID */ Parameters.GoodsTypeId /* int64 */;
            tempSet?: /* 设定温度 */ Parameters.TempSet;
            humSet?: /* 设定湿度 */ Parameters.HumSet;
            status?: /* 状态:启用：y；停用：n； */ Parameters.Status;
        }
    }
    namespace SaveWarnConfig {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 持续时长（分钟）
             */
            export type Duration = number; // int32
            /**
             * 湿度下偏差值
             */
            export type HumDownValue = string;
            /**
             * 湿度上偏差值
             */
            export type HumUpValue = string;
            /**
             * ID
             */
            export type Id = number; // int64
            /**
             * 名称
             */
            export type Name = string;
            /**
             * 状态:启用：y；停用：n；
             */
            export type Status = "y" | "n" | "del";
            /**
             * 温度下偏差值
             */
            export type TempDownValue = string;
            /**
             * 温度上偏差值
             */
            export type TempUpValue = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            /**
             * 预警等级：初级预警：level1；中级预警：level2；高级预警：level3
             */
            export type WarnLevel = "level1" | "level2" | "level3";
        }
        export interface QueryParameters {
            id?: /* ID */ Parameters.Id /* int64 */;
            name?: /* 名称 */ Parameters.Name;
            tempUpValue?: /* 温度上偏差值 */ Parameters.TempUpValue;
            tempDownValue?: /* 温度下偏差值 */ Parameters.TempDownValue;
            humUpValue?: /* 湿度上偏差值 */ Parameters.HumUpValue;
            humDownValue?: /* 湿度下偏差值 */ Parameters.HumDownValue;
            duration?: /* 持续时长（分钟） */ Parameters.Duration /* int32 */;
            warnLevel?: /* 预警等级：初级预警：level1；中级预警：level2；高级预警：level3 */ Parameters.WarnLevel;
            status?: /* 状态:启用：y；停用：n； */ Parameters.Status;
        }
    }
    namespace ShipmentsAssetSale {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 设备ID列表
             */
            export type AssetsIdList = string[];
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 快递名称
             */
            export type FastMailName = string;
            /**
             * 快递单号
             */
            export type FastMailNumber = string;
            /**
             * 主键
             */
            export type Id = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id?: /* 主键 */ Parameters.Id /* int64 */;
            fastMailName?: /* 快递名称 */ Parameters.FastMailName;
            fastMailNumber?: /* 快递单号 */ Parameters.FastMailNumber;
            assetsIdList?: /* 设备ID列表 */ Parameters.AssetsIdList;
        }
    }
    namespace SignAccidentFaultDamageReport {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type CarNumber = string;
            export type CreateTime = string; // date-time
            export type Creator = string;
            export type Deleted = boolean;
            export type DeptId = number; // int64
            export type DriverId = number; // int64
            export type Id = number; // int64
            export type SignTime = string; // date-time
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            export type UpdateTime = string; // date-time
            export type Updater = string;
        }
        export interface QueryParameters {
            id?: Parameters.Id /* int64 */;
            signTime?: Parameters.SignTime /* date-time */;
            driverId?: Parameters.DriverId /* int64 */;
            carNumber?: Parameters.CarNumber;
            deptId?: Parameters.DeptId /* int64 */;
            createTime?: Parameters.CreateTime /* date-time */;
            updateTime?: Parameters.UpdateTime /* date-time */;
            creator?: Parameters.Creator;
            updater?: Parameters.Updater;
            deleted?: Parameters.Deleted;
        }
    }
    namespace SpiltOrder {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 拆单列表
             */
            export type DtoList = /* 拆分订单DTO */ ServiceTypeCcms.Schemas.TransportOrderSplitDTO[];
            /**
             * 订单ID
             */
            export type OrderId = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            orderId: /* 订单ID */ Parameters.OrderId /* int64 */;
            dtoList: /* 拆单列表 */ Parameters.DtoList;
        }
    }
    namespace StatisticsDriverSignHistory {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type CarNumber = string;
            export type CreateTime = string; // date-time
            export type Creator = string;
            export type Deleted = boolean;
            export type DeptId = number; // int64
            export type DriverId = number; // int64
            /**
             * 驾驶员姓名
             */
            export type DriverName = string;
            /**
             * 结束时间
             */
            export type EndTime = string; // date-time
            export type Id = number; // int64
            export type PageNo = number; // int32
            export type PageSize = number; // int32
            export type SignTime = string; // date-time
            /**
             * 开始时间
             */
            export type StartTime = string; // date-time
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            export type UpdateTime = string; // date-time
            export type Updater = string;
        }
        export interface QueryParameters {
            driverName?: /* 驾驶员姓名 */ Parameters.DriverName;
            startTime?: /* 开始时间 */ Parameters.StartTime /* date-time */;
            endTime?: /* 结束时间 */ Parameters.EndTime /* date-time */;
            pageNo?: Parameters.PageNo /* int32 */;
            pageSize?: Parameters.PageSize /* int32 */;
            id?: Parameters.Id /* int64 */;
            signTime?: Parameters.SignTime /* date-time */;
            driverId?: Parameters.DriverId /* int64 */;
            carNumber?: Parameters.CarNumber;
            deptId?: Parameters.DeptId /* int64 */;
            createTime?: Parameters.CreateTime /* date-time */;
            updateTime?: Parameters.UpdateTime /* date-time */;
            creator?: Parameters.Creator;
            updater?: Parameters.Updater;
            deleted?: Parameters.Deleted;
        }
    }
    namespace UnbindCar {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type CarId = string;
            export type DriverId = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            driverId: Parameters.DriverId;
            carId: Parameters.CarId;
        }
    }
    namespace UnbindDriver {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type CarId = string;
            export type DriverId = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            carId: Parameters.CarId;
            driverId: Parameters.DriverId;
        }
    }
    namespace Update {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 计费方式：按里程：mileage；按体积：volume；按重量：weight；按时长：time
             * example:
             * 1
             */
            export type BillingType = string;
            /**
             * 品牌型号
             */
            export type BrandModel = string;
            /**
             * 车主名称
             */
            export type CarMasterName = string;
            /**
             * 车牌号
             */
            export type CarNumber = string;
            /**
             * 配置列表
             */
            export type ConfigTypeList = /* 配置列表 */ ServiceTypeCcms.Schemas.BusinessRuleConfigDO[];
            export type CreateTime = string; // date-time
            export type Creator = string;
            export type Deleted = boolean;
            export type DeptId = number; // int64
            export type EndTime = string; // date-time
            /**
             * 费用类型ID
             * example:
             * 14749
             */
            export type ExpenseTypeId = string;
            /**
             * 电子报单
             */
            export type FileList = ServiceTypeCcms.Schemas.FileDO[];
            export type Id = number; // int64
            /**
             * 保障结束时间
             */
            export type InsureEndTime = string; // date-time
            /**
             * 保单号码
             */
            export type InsureNumber = string;
            /**
             * 保障开始时间
             */
            export type InsureStartTime = string; // date-time
            /**
             * 保单类型：交强险：compulsory；商业险：business；
             */
            export type InsureType = "compulsory" | "business";
            /**
             * 费用配置计费时段
             */
            export type List = /* 费用配置计费时段 */ ServiceTypeCcms.Schemas.ExpenseConfigTimeDO[];
            export type Name = string;
            /**
             * 手机号码
             */
            export type Phone = string;
            export type Remark = string;
            export type StartTime = string; // date-time
            export type Status = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            export type UpdateTime = string; // date-time
            export type Updater = string;
            /**
             * 用户类型：客户：customer；
             */
            export type UserType = "customer" | "common" | "ssoAdmin";
            /**
             * 车架号
             */
            export type VinNo = string;
        }
        export interface QueryParameters {
            id?: Parameters.Id /* int64 */;
            name?: Parameters.Name;
            startTime?: Parameters.StartTime /* date-time */;
            endTime?: Parameters.EndTime /* date-time */;
            status?: Parameters.Status;
            remark?: Parameters.Remark;
            deptId?: Parameters.DeptId /* int64 */;
            createTime?: Parameters.CreateTime /* date-time */;
            updateTime?: Parameters.UpdateTime /* date-time */;
            creator?: Parameters.Creator;
            updater?: Parameters.Updater;
            deleted?: Parameters.Deleted;
        }
    }
    namespace UpdateAssetsSalePlan {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 资产型号
             */
            export type AssetModel = string;
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 文件列表
             */
            export type FileList = /* 文件列表 */ ServiceTypeCcms.Schemas.AssetsSalePlanFileDO[];
            /**
             * 主键
             */
            export type Id = number; // int64
            /**
             * 租赁单价
             */
            export type LeasePrice = number;
            /**
             * 最少租赁时长
             */
            export type MinLeaseTime = number; // int32
            /**
             * 方案名称
             */
            export type Name = string;
            /**
             * 适用湿度
             */
            export type SafeHumidity = string;
            /**
             * 适用温度
             */
            export type SafeTemp = string;
            /**
             * 销售单价
             */
            export type SalePrice = number;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id?: /* 主键 */ Parameters.Id /* int64 */;
            name?: /* 方案名称 */ Parameters.Name;
            assetModel?: /* 资产型号 */ Parameters.AssetModel;
            safeTemp?: /* 适用温度 */ Parameters.SafeTemp;
            safeHumidity?: /* 适用湿度 */ Parameters.SafeHumidity;
            minLeaseTime?: /* 最少租赁时长 */ Parameters.MinLeaseTime /* int32 */;
            leasePrice?: /* 租赁单价 */ Parameters.LeasePrice;
            salePrice?: /* 销售单价 */ Parameters.SalePrice;
            fileList?: /* 文件列表 */ Parameters.FileList;
        }
    }
    namespace UpdateCarRepairForm {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 车牌号
             */
            export type CarNumber = string;
            /**
             * 电子报单
             */
            export type FileList = ServiceTypeCcms.Schemas.FileDO[];
            /**
             * 维修厂联系人
             */
            export type FixName = string;
            /**
             * 维修厂手机号码
             */
            export type FixPhone = string;
            /**
             * 维修总价
             */
            export type FixTotalPrice = number;
            /**
             * 报单编号
             */
            export type FormNumber = string;
            /**
             * id
             */
            export type Id = number; // int64
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            /**
             * 车架号
             */
            export type VinNo = string;
        }
        export interface QueryParameters {
            id?: /* id */ Parameters.Id /* int64 */;
            formNumber?: /* 报单编号 */ Parameters.FormNumber;
            carNumber?: /* 车牌号 */ Parameters.CarNumber;
            vinNo?: /* 车架号 */ Parameters.VinNo;
            fixTotalPrice?: /* 维修总价 */ Parameters.FixTotalPrice;
            fixName?: /* 维修厂联系人 */ Parameters.FixName;
            fixPhone?: /* 维修厂手机号码 */ Parameters.FixPhone;
            fileList?: /* 电子报单 */ Parameters.FileList;
        }
    }
    namespace UpdateCarRoute {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 运输类型：整车：whole_car；零担：less_car
             */
            export type CarType = "whole_car" | "less_car";
            /**
             * 驾驶员ID
             */
            export type DriverId = number; // int64
            /**
             * 结束市
             */
            export type EndCity = string;
            /**
             * 结束省
             */
            export type EndProvince = string;
            /**
             * ID
             */
            export type Id = number; // int64
            /**
             * 开始市
             */
            export type StartCity = string;
            /**
             * 开始省
             */
            export type StartProvince = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id?: /* ID */ Parameters.Id /* int64 */;
            driverId: /* 驾驶员ID */ Parameters.DriverId /* int64 */;
            carType: /* 运输类型：整车：whole_car；零担：less_car */ Parameters.CarType;
            startProvince: /* 开始省 */ Parameters.StartProvince;
            startCity: /* 开始市 */ Parameters.StartCity;
            endProvince: /* 结束省 */ Parameters.EndProvince;
            endCity: /* 结束市 */ Parameters.EndCity;
        }
        export type RequestBody = /* 车辆路线 */ ServiceTypeCcms.Schemas.CarRouteDTO;
    }
    namespace UpdateLoadingStrategyConfig {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 排斥商品
             */
            export type ExcludeGoods = string;
            /**
             * ID
             */
            export type Id = number; // int64
            /**
             * 兼容商品
             */
            export type IncludeGoods = string;
            /**
             * 名称
             */
            export type Name = string;
            /**
             * 备注
             */
            export type Remark = string;
            /**
             * 状态:启用：y；停用：n；
             */
            export type Status = "y" | "n" | "del";
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id?: /* ID */ Parameters.Id /* int64 */;
            name: /* 名称 */ Parameters.Name;
            remark?: /* 备注 */ Parameters.Remark;
            includeGoods: /* 兼容商品 */ Parameters.IncludeGoods;
            excludeGoods: /* 排斥商品 */ Parameters.ExcludeGoods;
            status: /* 状态:启用：y；停用：n； */ Parameters.Status;
        }
    }
    namespace UpdateSalesOrders {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            export type AcceptAddress = string;
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type ContactsName = string;
            export type ContactsPhone = string;
            export type CreateTime = string; // date-time
            export type Creator = string;
            export type CustomerId = number; // int64
            export type CustomerName = string;
            export type Deleted = boolean;
            export type DeptId = number; // int64
            export type FastMailName = string;
            export type FastMailNumber = string;
            export type Id = number; // int64
            export type LeaseEndTime = string; // date-time
            export type LeaseStartTime = string; // date-time
            export type LeaseTime = number; // int32
            export type Number = number; // int32
            export type OrderTime = string; // date-time
            export type Price = number;
            export type Remark = string;
            export type SaleConfigId = number; // int64
            export type SaleType = string;
            export type Status = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            export type TotalPrice = number;
            export type UpdateTime = string; // date-time
            export type Updater = string;
        }
        export interface QueryParameters {
            id?: Parameters.Id /* int64 */;
            customerId?: Parameters.CustomerId /* int64 */;
            customerName?: Parameters.CustomerName;
            contactsName?: Parameters.ContactsName;
            contactsPhone?: Parameters.ContactsPhone;
            saleConfigId?: Parameters.SaleConfigId /* int64 */;
            saleType?: Parameters.SaleType;
            leaseTime?: Parameters.LeaseTime /* int32 */;
            leaseStartTime?: Parameters.LeaseStartTime /* date-time */;
            leaseEndTime?: Parameters.LeaseEndTime /* date-time */;
            number?: Parameters.Number /* int32 */;
            price?: Parameters.Price;
            totalPrice?: Parameters.TotalPrice;
            status?: Parameters.Status;
            remark?: Parameters.Remark;
            acceptAddress?: Parameters.AcceptAddress;
            fastMailName?: Parameters.FastMailName;
            fastMailNumber?: Parameters.FastMailNumber;
            orderTime?: Parameters.OrderTime /* date-time */;
            deptId?: Parameters.DeptId /* int64 */;
            createTime?: Parameters.CreateTime /* date-time */;
            updateTime?: Parameters.UpdateTime /* date-time */;
            creator?: Parameters.Creator;
            updater?: Parameters.Updater;
            deleted?: Parameters.Deleted;
        }
    }
    namespace UpdateStaffCashSettlement {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type CreateTime = string; // date-time
            export type Creator = string;
            export type Deleted = boolean;
            export type DeptId = number; // int64
            export type Id = number; // int64
            /**
             * 订单ID
             */
            export type OrderId = number; // int64
            /**
             * 结算ID
             */
            export type SettleId = number; // int64
            /**
             * 结算状态：是：y；否：n；
             */
            export type SettleStatus = string;
            /**
             * 结算方式：现金：cash；转账：transfer
             */
            export type SettleType = string;
            /**
             * 结算信息详情
             */
            export type StaffCashSettlementList = /* 结算信息详情 */ ServiceTypeCcms.Schemas.StaffCashSettlementDetailDO[];
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            export type TotalMoney = number;
            export type UpdateTime = string; // date-time
            export type Updater = string;
        }
        export interface QueryParameters {
            orderId?: /* 订单ID */ Parameters.OrderId /* int64 */;
            settleId?: /* 结算ID */ Parameters.SettleId /* int64 */;
            settleType?: /* 结算方式：现金：cash；转账：transfer */ Parameters.SettleType;
            settleStatus?: /* 结算状态：是：y；否：n； */ Parameters.SettleStatus;
            staffCashSettlementList?: /* 结算信息详情 */ Parameters.StaffCashSettlementList;
            id?: Parameters.Id /* int64 */;
            totalMoney?: Parameters.TotalMoney;
            deptId?: Parameters.DeptId /* int64 */;
            createTime?: Parameters.CreateTime /* date-time */;
            updateTime?: Parameters.UpdateTime /* date-time */;
            creator?: Parameters.Creator;
            updater?: Parameters.Updater;
            deleted?: Parameters.Deleted;
        }
    }
    namespace UpdateSystemConfig {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 收款账号
             */
            export type Account = string;
            /**
             * 收款账号类型:银行卡：bank；支付宝：alipay；微信：wechat；
             */
            export type AccountType = "bank" | "alipay" | "wechat";
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 银行名称
             */
            export type BankName = string;
            /**
             * 撤单时间限制
             */
            export type CancelLimitTime = number; // int32
            /**
             * 冷链设施采集频率
             */
            export type CollectFrequency = number; // int32
            /**
             * ID
             */
            export type Id = number; // int64
            /**
             * 企业名称
             */
            export type Name = string;
            /**
             * 开启预冷预热：开启预冷预热：开启：open；关闭：close；
             */
            export type OpenColdHot = string;
            /**
             * 冷链设施发送频率
             */
            export type SendFrequency = number; // int32
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id?: /* ID */ Parameters.Id /* int64 */;
            name?: /* 企业名称 */ Parameters.Name;
            accountType?: /* 收款账号类型:银行卡：bank；支付宝：alipay；微信：wechat； */ Parameters.AccountType;
            bankName?: /* 银行名称 */ Parameters.BankName;
            account?: /* 收款账号 */ Parameters.Account;
            collectFrequency?: /* 冷链设施采集频率 */ Parameters.CollectFrequency /* int32 */;
            sendFrequency?: /* 冷链设施发送频率 */ Parameters.SendFrequency /* int32 */;
            openColdHot?: /* 开启预冷预热：开启预冷预热：开启：open；关闭：close； */ Parameters.OpenColdHot;
            cancelLimitTime?: /* 撤单时间限制 */ Parameters.CancelLimitTime /* int32 */;
        }
    }
    namespace UpdateTempRangConfig {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 车辆类别：A：A类；B：B类；C：C类；D：D类；E：E类；F：F类；G：G类；H：H类；
             */
            export type CarCategory = "A" | "B" | "C" | "D" | "E" | "F" | "G" | "H";
            /**
             * 车长ID
             */
            export type CarLengthId = number; // int64
            /**
             * 车型ID
             */
            export type CarModelId = number; // int64
            /**
             * 温区配置详情
             */
            export type DetailList = /* 温区配置详情 */ ServiceTypeCcms.Schemas.TempRangConfigDetailDO[];
            /**
             * ID
             */
            export type Id = number; // int64
            /**
             * 策略名称
             */
            export type Name = string;
            /**
             * 状态:启用：y；停用：n；
             */
            export type Status = "y" | "n" | "del";
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id?: /* ID */ Parameters.Id /* int64 */;
            name?: /* 策略名称 */ Parameters.Name;
            carModelId?: /* 车型ID */ Parameters.CarModelId /* int64 */;
            carLengthId?: /* 车长ID */ Parameters.CarLengthId /* int64 */;
            carCategory?: /* 车辆类别：A：A类；B：B类；C：C类；D：D类；E：E类；F：F类；G：G类；H：H类； */ Parameters.CarCategory;
            status?: /* 状态:启用：y；停用：n； */ Parameters.Status;
            detailList?: /* 温区配置详情 */ Parameters.DetailList;
        }
    }
    namespace UpdateTransportSchemeConfig {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 货物类型ID
             */
            export type GoodsTypeId = number; // int64
            /**
             * 设定湿度
             */
            export type HumSet = string;
            /**
             * ID
             */
            export type Id = number; // int64
            /**
             * 名称
             */
            export type Name = string;
            /**
             * 定点路线ID
             */
            export type PointRouteId = number; // int64
            /**
             * 适用季节：春季：spring；夏季：summer；秋季：autumn；冬季：winter；
             */
            export type SeasonsType = string;
            /**
             * 状态:启用：y；停用：n；
             */
            export type Status = "y" | "n" | "del";
            /**
             * 设定温度
             */
            export type TempSet = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            id?: /* ID */ Parameters.Id /* int64 */;
            name?: /* 名称 */ Parameters.Name;
            seasonsType?: /* 适用季节：春季：spring；夏季：summer；秋季：autumn；冬季：winter； */ Parameters.SeasonsType;
            pointRouteId?: /* 定点路线ID */ Parameters.PointRouteId /* int64 */;
            goodsTypeId?: /* 货物类型ID */ Parameters.GoodsTypeId /* int64 */;
            tempSet?: /* 设定温度 */ Parameters.TempSet;
            humSet?: /* 设定湿度 */ Parameters.HumSet;
            status?: /* 状态:启用：y；停用：n； */ Parameters.Status;
        }
    }
    namespace UpdateWarnConfig {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            /**
             * 持续时长（分钟）
             */
            export type Duration = number; // int32
            /**
             * 湿度下偏差值
             */
            export type HumDownValue = string;
            /**
             * 湿度上偏差值
             */
            export type HumUpValue = string;
            /**
             * ID
             */
            export type Id = number; // int64
            /**
             * 名称
             */
            export type Name = string;
            /**
             * 状态:启用：y；停用：n；
             */
            export type Status = "y" | "n" | "del";
            /**
             * 温度下偏差值
             */
            export type TempDownValue = string;
            /**
             * 温度上偏差值
             */
            export type TempUpValue = string;
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
            /**
             * 预警等级：初级预警：level1；中级预警：level2；高级预警：level3
             */
            export type WarnLevel = "level1" | "level2" | "level3";
        }
        export interface QueryParameters {
            id?: /* ID */ Parameters.Id /* int64 */;
            name?: /* 名称 */ Parameters.Name;
            tempUpValue?: /* 温度上偏差值 */ Parameters.TempUpValue;
            tempDownValue?: /* 温度下偏差值 */ Parameters.TempDownValue;
            humUpValue?: /* 湿度上偏差值 */ Parameters.HumUpValue;
            humDownValue?: /* 湿度下偏差值 */ Parameters.HumDownValue;
            duration?: /* 持续时长（分钟） */ Parameters.Duration /* int32 */;
            warnLevel?: /* 预警等级：初级预警：level1；中级预警：level2；高级预警：level3 */ Parameters.WarnLevel;
            status?: /* 状态:启用：y；停用：n； */ Parameters.Status;
        }
    }
    namespace UploadFile {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type FileType = "id_card" | "driver_license" | "driving_license" | "other" | "picture" | "load_picture" | "unload_picture" | "check_picture" | "LICENSE" | "CAR_PIC" | "PROFILE" | "QUALIFICATION" | "driver_license_full" | "driver_license_back" | "ID_CARD_POSITIVE" | "ID_CARD_OTHER_SIDE" | "CAR_INSURE" | "repair_form" | "accident" | "fault" | "damage";
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            fileType: Parameters.FileType;
        }
    }
    namespace UploadFileOpen {
        export interface HeaderParameters {
            "tenant-id"?: /* 租户编号 */ Parameters.TenantId /* int32 */;
            Authorization?: /* 认证 Token */ Parameters.Authorization;
        }
        namespace Parameters {
            /**
             * 认证 Token
             */
            export type Authorization = string;
            export type FileType = "id_card" | "driver_license" | "driving_license" | "other" | "picture" | "load_picture" | "unload_picture" | "check_picture" | "LICENSE" | "CAR_PIC" | "PROFILE" | "QUALIFICATION" | "driver_license_full" | "driver_license_back" | "ID_CARD_POSITIVE" | "ID_CARD_OTHER_SIDE" | "CAR_INSURE" | "repair_form" | "accident" | "fault" | "damage";
            /**
             * 租户编号
             */
            export type TenantId = number; // int32
        }
        export interface QueryParameters {
            fileType: Parameters.FileType;
        }
    }
}
