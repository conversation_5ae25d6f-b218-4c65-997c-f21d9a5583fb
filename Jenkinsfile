pipeline {
    agent none
    environment {
        /*start:需要配置的参数*/
        REMOTE_SERVER_IP = '**********'//远程服务器IP
        REMOTE_SERVICE_DIR = '/data/apps/tslbdp/statics/'//远程服务器的部署目录
        REMOTE_SERVICE_MODEL_NAME = 'tslbdp-cloud-module-ccms-page'//远程服务器的部署工程文件夹名称
        CREDENTIALS_ID = 'app3_common'//远程服务器访问凭证
        /*end:需要配置的参数*/
    }
    stages {
        stage('Build') {
            agent {
                docker {
                    image 'arvinchen9539/node-20.11.1-corepack-volta'
                }
            }
            steps {
                sh 'npm config set registry https://registry.npmmirror.com'
                sh 'pnpm install'
                sh 'pnpm build:pst'
            }
        }
        stage('Deploy') {
            agent any
            steps {
                script {
                    sh "rm -fr ${REMOTE_SERVICE_MODEL_NAME} && mv dist-pst ${REMOTE_SERVICE_MODEL_NAME}"//重命名为待部署的模块名文件夹
                    def sshServer = getServer(REMOTE_SERVER_IP, CREDENTIALS_ID)
                    sshCommand remote: sshServer, command: "rm -fr ${REMOTE_SERVICE_DIR}${REMOTE_SERVICE_MODEL_NAME}"
                    //远程删除原文件
                    sshPut remote: sshServer, from: "${REMOTE_SERVICE_MODEL_NAME}", into: "${REMOTE_SERVICE_DIR}"
                    //远程复制文件夹
                    sh "mv ${REMOTE_SERVICE_MODEL_NAME} dist-pst"//改回原文件夹名称
                }
            }
        }
    }
}
//通过凭证获取远程访问服务器的对象
def getServer(ip, credentialsId) {
    def remote = [:]
    remote.name = "server-${ip}"
    remote.host = ip
    remote.port = 22
    remote.allowAnyHosts = true
    //credentialsId填写jenkins配置的凭证id
    withCredentials([usernamePassword(credentialsId: credentialsId, passwordVariable: 'password', usernameVariable: 'username')]) {
        remote.user = "${username}"
        remote.password = "${password}"
    }
    return remote
}
