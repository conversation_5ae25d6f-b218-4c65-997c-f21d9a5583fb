import request from "@/config/axios";
import { sysType } from '@/utils/sysType'
import <% if(!$$.isTsVersion3){ %>type <% } %> {AxiosRequestConfig} from 'axios'
declare type Overwrite<T, U> = Pick<T, Exclude<keyof T, keyof U>> & U;
declare type Omit<T, K> = Pick<T, Exclude<keyof T, K>>;
<% _.forEach(data.mocks, function(mock){ %>/** {{mock.description}} */
async function {{$$.convertMethod(mock)}}(opts?:Overwrite<AxiosRequestConfig,{data?:{{$$.convertParamsType(mock)}}}>) {
  const i = request.{{mock.method}}({
    url:<% if($$.isREST(mock.url)) {%>convertRESTAPI({{$$.convertAdminApi(mock.url)}}', opts)<%} else {%> `/${sysType}{{$$.convertAdminApi(mock.url)}}`<% } %>,
    ...(opts||{}),
      headersType: opts && opts.headers && opts.headers['Content-Type'] || "application/x-www-form-urlencoded"
  });
  const data = await i
  const res = {data}
  return (res as unknown<% if($$.convertType(mock)){%> as {{$$.convertType(mock)}}<% } %>)
}

<% }) %>export {<% _.forEach(data.mocks, function(mock, i){ %>
  {{$$.convertMethod(mock)}}<% if(data.mocks.length - 1 !== i) { %>,<% } %><% }) %>
};
