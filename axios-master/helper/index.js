var _ = require('lodash')
var tsc = require('typescript')

// 判断是不是3版本
exports.isTsVersion3 = tsc.version.startsWith('3')

exports.convertAdminApi = function(url){
  return url.replace(/admin-api\/ccms\//g, '')
}

var convertUrl = exports.convertUrl = function (url) {
  // /restful/:id/:list/{id} -> restful_id_list_id
  // /restful/:id/:list/{id}.json -> restful_id_list_id
  var _url = url
    .replace(/:|{|}/g, '')
    .replace(/admin-api\/ccms/g, '')
    .replace(/-/g, '_')
    .split('/')
    .filter(value => !!value).join('_')
  return _url.split('.')[0]
}

exports.convertMethod = function (mock) {
  // 防止重名
  // restful_id_list_id => restful_id_list_id_g
  // or
  // restful_id_list_id => restful_id_list_id_p
  // console.log(mock.url)
  return convertUrl(mock.url) + '_' + mock.method.toLowerCase()
}

exports.convertParamsType = function (mock) {
  // 替换参数中的{为'daolefudaolefu_'
  mock.parameters = mock.parameters.replace(/{/g, 'daolefudaolefu_')
  mock.parameters = mock.parameters.replace(/}/g, '')
  mock.parameters = mock.parameters.replace(/\./g, '')
  mock.parameters = mock.parameters.replace(/-/g, '_')
  if (mock.paramsTypes) {
    let types = Object.keys(mock.paramsTypes)
    if (mock.parameters && types.length) {
      let resTypes = []
      // 修复入参的格式会发生变化的问题,需要更新为冷链全国监控平台的swagger生成代码
      // 将'Daolefudaolefu'转化为$
      const params = mock.parameters.split('_')
      let paramName = ''
      for (let item of params) {
        for (let item1 of item.split('/')) {
          paramName += _.upperFirst(item1)
        }
      }
      const baseTypeName = (mock.serviceTypePathsName + '.' + paramName + '.').replace(/Daolefudaolefu/g, '$')
      for (let item of types) {
        // body参数api自动转换为对象,格式约束改为any
        if (item === 'body') {
          resTypes.push('any')
        } else {
          resTypes.push(baseTypeName + _.upperFirst(item) + 'Parameters')
        }
      }
      // 忽略ticket属性必填
      return `Omit<${resTypes.join('&')},"ticket">` + (mock.checkParams ? '' : '|any')
    }
  }
  return 'any'
}
exports.convertType = function (mock) {
  let type = mock.response_model
  // 没有声明定义时跳过类型指定步骤
  if (!type) {
    return false
  }

  type = type.replace(/，/g, '')
  type = type.replace(/_/g, '')
  type = type.replace(/、/g, '')
  type = type.replace(/：/g, '')
  type = type.replace(/:/g, '')
  type = type.replace(/\./g, '')

  if (type.indexOf(',') !== -1) {
    type = _.camelCase(type.replace(/,/g, '_'))
  }
  if (type.indexOf('-') !== -1) {
    type = _.camelCase(type.replace(/-/g, '_'))
  }

  // «符号后的第一个字母大写,修复转换的类型和生成的类型对不上的问题
  const typeChar = type.split('')
  for (let [index, item] of typeChar.entries()) {
    if (['«','(',')'].includes(item||'')) {
      typeChar[index + 1] = _.upperFirst(typeChar[index + 1])
    }
  }
  type = typeChar.join('')

  // 空格后的字母大写
  const typeChar1 = type.split('')
  for (let [index, item] of typeChar1.entries()) {
    if (item === ' ') {
      typeChar1[index + 1] = _.upperFirst(typeChar1[index + 1])
    }
  }
  type = typeChar1.join('')

  // 移除特殊符号
  type = type.replace(/«/g, '')
  type = type.replace(/»/g, '')
  type = type.replace(/\(/g, '')
  type = type.replace(/\)/g, '')
  type = type.replace(/ /g, '')
  //修复类型中带/的问题转换为点
  type = type.replace(/\//g, '.')
  if (mock.version === '2.0') {
    return `${mock.serviceTypeName}.${type}`
  } else {
    return `${mock.serviceTypeName}.Schemas.${type}`
  }
}

exports.joinUrl = function () {
  // https://www.easy-mock.com//mock/.... => https://www.easy-mock.com/mock/....
  var url = [].slice.call(arguments, 0).join('/')
  url = url.replace(/:\//g, '://')
  url = url.replace(/([^:\s\%\3\A])\/+/g, '$1/')
  return url
}

exports.isREST = function (url) {
  return /(:|{|})/.test(url)
}
