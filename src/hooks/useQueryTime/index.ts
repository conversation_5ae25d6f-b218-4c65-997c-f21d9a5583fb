import type { Ref } from "vue";
import { computed, isRef, watch } from "vue";
import dayjs from "dayjs";
import { isEmpty } from "lodash-es";

/**
 * 用于绑定UI库的时间范围组件
 * @param startDate 开始时间
 * @param endDate 结束时间
 * @param type 需要获取的时间范围类型
 */
export function useQueryTime(
  startDate: Ref<string | undefined>,
  endDate: Ref<string | undefined>,
  type: "D" | "M" = "D" //D:天的开始和结束;M:月份的开始和结束
) {
  if (!isRef(startDate) || !isRef(endDate)) {
    console.warn("参数必须是ref对象", startDate, endDate);
  }

  //其中一个时间为空时,另一个时间也为空
  watch([startDate, endDate], (newVal, oldVal) => {
    if (newVal !== oldVal && (!startDate.value || !endDate.value)) {
      startDate.value = undefined;
      endDate.value = undefined;
    }
  });

  //自动映射UI时间控件的数组到对象属性上
  const datePickerModel = computed({
    get: () => {
      return [startDate.value, endDate.value] as any;
    },
    set: (data) => {
      if (isEmpty(data)) {
        data = [];
      }
      startDate.value = data[0]
        ? dayjs(data[0]).format("YYYY-MM-DD HH:mm:ss")
        : "";
      endDate.value = data[1]
        ? dayjs(data[1]).format("YYYY-MM-DD HH:mm:ss")
        : "";
    },
  });

  //搜索时间默认的时间(UI库必须将这个对象设置到defaultTime属性上,才能选中后拿到正确的开始和结束时间)
  const startEndTime = computed(() => {
    return [
      dayjs(startDate.value || undefined)
        .startOf(type)
        .toDate(),
      dayjs(endDate.value || undefined)
        .endOf(type)
        .toDate(),
    ] as any;
  });

  return {
    datePickerModel,
    startEndTime,
  };
}
