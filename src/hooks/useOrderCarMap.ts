import { computed, onMounted, ref, Ref } from "vue";
import carIcon from "@/assets/images/<EMAIL>";

// 配置项：是否需要转换百度坐标为高德坐标
const NEED_CONVERT_COORDS = true;

/**
 * 百度坐标（BD09）转高德坐标（GCJ02）
 * @param lng 经度
 * @param lat 纬度
 * @returns 转换后的坐标数组 [lng, lat]
 */
function convertBD09ToGCJ02(lng: number, lat: number): [number, number] {
  const xPi = Math.PI * 3000.0 / 180.0;
  const x = lng - 0.0065;
  const y = lat - 0.006;
  const z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * xPi);
  const theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * xPi);
  const gcjLng = z * Math.cos(theta);
  const gcjLat = z * Math.sin(theta);
  return [gcjLng, gcjLat];
}

// 确保插件已加载
function ensurePluginsLoaded() {
  return new Promise<void>((resolve) => {
    // 需要加载的插件列表
    const plugins = ['AMap.Geocoder'];
    
    // 加载插件
    AMap.plugin(plugins, () => {
      resolve();
    });
  });
}

declare const AMap: any;

export function useOrderCarMap(mapRef: Ref<HTMLDivElement | undefined>) {
  let map: any;
  let polyline: any;
  let iconMarker: any;
  let geoc: any;

  function getMap() {
    return map;
  }

  function getPolyline() {
    return polyline;
  }

  //路线坐标点集合
  const mapPoints = ref<any>([]);
  const currentMapPoint = computed(() => {
    return mapPoints.value[mapPoints.value.length - 1] || {};
  });

  function setMapPoints(data: any[], isNoSetView?: boolean) {
    mapPoints.value = data || [];
    
    // 创建路径坐标点数组
    const path = mapPoints.value.map((item: any) => {
      // 如果需要转换坐标，将百度坐标转换为高德坐标
      if (NEED_CONVERT_COORDS) {
        const [gcjLng, gcjLat] = convertBD09ToGCJ02(Number(item.lngValue), Number(item.latValue));
        return [gcjLng, gcjLat];
      } else {
        return [item.lngValue, item.latValue];
      }
    });
    
    if (path.length === 0) return;
    
    // 清除之前的路线和标记
    if (polyline) {
      map.remove(polyline);
    }
    if (iconMarker) {
      map.remove(iconMarker);
    }
    
    // 创建新的路线
    polyline = new AMap.Polyline({
      path: path,
      strokeColor: '#1678FF',
      strokeOpacity: 1,
      strokeWeight: 8,
      strokeStyle: 'solid',
      lineJoin: 'round',
      lineCap: 'round',
      zIndex: 50,
      showDir: true,         // 显示方向箭头
      dirColor: '#1678FF'    // 方向箭头颜色
    });
    
    // 将折线添加到地图
    map.add(polyline);
    
    // 显示最后的车辆位置
    if (currentMapPoint.value && currentMapPoint.value.lngValue) {
      let position;
      if (NEED_CONVERT_COORDS) {
        const [gcjLng, gcjLat] = convertBD09ToGCJ02(
          Number(currentMapPoint.value.lngValue), 
          Number(currentMapPoint.value.latValue)
        );
        position = [gcjLng, gcjLat];
      } else {
        position = [currentMapPoint.value.lngValue, currentMapPoint.value.latValue];
      }
      
      iconMarker = new AMap.Marker({
        position: position,
        icon: carIcon,
        offset: new AMap.Pixel(-15, -15),
        anchor: 'center'
      });
      
      map.add(iconMarker);
    }
    
    // 调整地图视野
    if (!isNoSetView && path.length > 0) {
      setTimeout(() => {
        map.setFitView(null, false, [60, 60, 60, 60]);
      }, 1000);
    }
  }

  // 创建标记点样式
  function createMarkerContent(text: string, color: string) {
    const markerContent = document.createElement('div');
    markerContent.className = 'custom-marker rounded-full text-white flex justify-center items-center';
    markerContent.style.backgroundColor = color || '#1678FF';
    markerContent.style.color = '#fff';
    markerContent.style.fontSize = '12px';
    markerContent.style.fontWeight = 'bold';
    markerContent.style.textAlign = 'center';
    markerContent.style.width = '30px';
    markerContent.style.height = '30px';
    markerContent.style.borderRadius = '50%';
    markerContent.style.whiteSpace = 'nowrap';
    markerContent.innerHTML = text;
    return markerContent;
  }

  let startPointMarker: any;
  let endPointMarkerList: any = [];

  function setStartEndPointByAddress(start: string, endList: string[]) {
    if (!geoc) return;
    
    // 设置起点
    geoc.getLocation(start, function(status: string, result: any) {
      if (status === 'complete' && result.geocodes.length) {
        const location = result.geocodes[0].location;
        
        // 移除之前的起点标记
        if (startPointMarker) {
          map.remove(startPointMarker);
        }
        
        // 创建新的起点标记
        startPointMarker = new AMap.Marker({
          position: [location.lng, location.lat],
          content: createMarkerContent('起点', '#36B336'),
          offset: new AMap.Pixel(-15, -15),
          zIndex: 100
        });
        
        map.add(startPointMarker);
      }
    });
    
    // 清除之前的终点标记
    if (endPointMarkerList && endPointMarkerList.length) {
      map.remove(endPointMarkerList);
      endPointMarkerList = [];
    }
    
    // 设置终点
    for (const end of endList) {
      geoc.getLocation(end, function(status: string, result: any) {
        if (status === 'complete' && result.geocodes.length) {
          const location = result.geocodes[0].location;
          
          // 创建终点标记
          const endMarker = new AMap.Marker({
            position: [location.lng, location.lat],
            content: createMarkerContent('终点', '#E04F48'),
            offset: new AMap.Pixel(-15, -15),
            zIndex: 100
          });
          
          map.add(endMarker);
          endPointMarkerList.push(endMarker);
        }
      });
    }
  }

  onMounted(async () => {
    // 先加载插件
    await ensurePluginsLoaded();
    
    // 创建地图实例
    map = new AMap.Map(mapRef.value, {
      viewMode: '3D', // 3D视图
      zoom: 12,      // 缩放级别
      center: [120.209947, 30.245853], // 杭州市中心坐标
      mapStyle: 'amap://styles/normal',
      resizeEnable: true
    });
    
    // 初始化地理编码服务
    geoc = new AMap.Geocoder({
      city: "全国", // 城市，默认全国
      radius: 1000 // 范围，默认1000米
    });
  });

  return {
    getMap,
    getPolyline,
    mapPoints,
    currentMapPoint,
    geoc,
    setMapPoints,
    setStartEndPointByAddress,
  };
}
