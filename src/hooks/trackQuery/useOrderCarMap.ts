import { computed, onMounted, ref, Ref } from 'vue'
import * as mapvgl from 'mapvgl'
// import mapvgl from "http://mapv.baidu.com/gl/mapvgl.min.js";

import carIcon from '@/assets/images/<EMAIL>'
import { size } from 'min-dash'
/**
 * 百度坐标（BD09）转高德坐标（GCJ02）
 * @param lng 经度
 * @param lat 纬度
 * @returns 转换后的坐标数组 [lng, lat]
 */
function convertBD09ToGCJ02(lng: number, lat: number): [number, number] {
  const xPi = (Math.PI * 3000.0) / 180.0
  const x = lng - 0.0065
  const y = lat - 0.006
  const z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * xPi)
  const theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * xPi)
  const gcjLng = z * Math.cos(theta)
  const gcjLat = z * Math.sin(theta)
  return [gcjLng, gcjLat]
}
onMounted(async () => {
  await loadScript('https://mapv.baidu.com/gl/mapvgl.min.js')
  // console.log(mapvgl, 'mapvgl') // 现在可以使用 mapvgl
})
function loadScript(src: string) {
  return new Promise((resolve, reject) => {
    const script = document.createElement('script')
    script.src = src
    script.onload = resolve
    script.onerror = reject
    document.head.appendChild(script)
  })
}
// declare const BMapGL: any
export function useOrderCarMap(mapRef: Ref<HTMLDivElement | undefined>) {
  let geoc: any
  let map: any
  let viewer: any
  // console.log(AMap)
  //引入插件，此示例采用异步引入，更多引入方式 https://lbs.amap.com/api/javascript-api-v2/guide/abc/plugins
  AMap.plugin('AMap.Geocoder', function () {
    geoc = new AMap.Geocoder({
      city: '杭州' // city 指定进行编码查询的城市，支持传入城市名、adcode 和 citycode
    })
    geoc.getLocation('杭州市拱墅区半山街道', function (status, result) {
      if (status === 'complete' && result.info === 'OK') {
        // result中对应详细地理坐标信息
        // console.log(result, 'result')
        const position = new AMap.LngLat(
          result.geocodes[0].location.lng,
          result.geocodes[0].location.lat
        ) //传入经纬度
        //var position = [116.681212,39.852698]; //另一种写法
        map.setCenter(position)
      }
    })
    // geocoder.getAddress(lnglat, function (status, result) {
    //   if (status === "complete" && result.info === "OK") {
    //     // result为对应的地理位置详细信息
    //     console.log(result);
    //   }
    // });
  })

  function getMap() {
    return map
  }

  function getViewer() {
    return viewer
  }

  //路线坐标点集合
  const mapPoints = ref<any>([])
  const currentMapPoint = computed(() => {
    return mapPoints.value[mapPoints.value.length - 1] || {}
  })

  function setMapPoints(data: any[], isNoSetView?: boolean) {
    mapPoints.value = data || []
    if (!isNoSetView) {
      // console.log(mapPoints.value, 'mapPoints.value')
      setTimeout(() => {
        //调整地图视野
        const points = mapPoints.value.map((item: any) => {
          // console.log(item.lngValue, item.latValue, 'item.lngValue, item.latValue')
          return map.setCenter([item.lngValue, item.latValue])
        })
        if (startPointOverlay) {
          points.push(startPointOverlay.point)
        }
        if (endPointOverlayList && endPointOverlayList.length) {
          for (const item of endPointOverlayList) {
            points.push(item.point)
          }
        }
        map.setFitView();
        // map.setViewport(points)
      }, 1000)
    }
    const pat = mapPoints.value.map((item: any) => {
      // console.log(item.lngValue, item.latValue, 'item.lngValue, item.latValue')
      const lnglatarr = convertBD09ToGCJ02(item.lngValue, item.latValue)
      return new AMap.LngLat(lnglatarr[0], lnglatarr[1])
    })

    //创建 Polyline 实例
    const polyline = new AMap.Polyline({
      path: pat,
      strokeWeight: 8, //线条宽度
      strokeColor: '#1678FF', //线条颜色
      lineJoin: 'round', //折线拐点连接处样式
      strokeOpacity:1,
      showDir:true
    })
    map.add(polyline)
    //清除所有层
    // viewer.removeAllLayers()
    // const lineLayer = new mapvgl.LineLayer({
    //   style: 'road',
    //   width: 8,
    //   color: '#1678FF',
    //   lineCap: 'round '
    // })
    // viewer.addLayer(lineLayer)

    // lineLayer.setData([
    //   {
    //     geometry: {
    //       type: 'LineString',
    //       coordinates: mapPoints.value.map((item: any) => {
    //         return [item.lngValue, item.latValue]
    //       })
    //     }
    //   }
    // ])

    //显示最后的车辆位置
    const icon = new AMap.Icon({
      size: new AMap.Size(30, 30), //图标尺寸
      image: carIcon, //Icon 的图像
      imageSize: new AMap.Size(30, 30) //根据所设置的大小拉伸或压缩图片
    })
    // console.log(currentMapPoint.value.lngValue, currentMapPoint.value.latValue);

    const marker = new AMap.Marker({
      position: new AMap.LngLat(
        currentMapPoint.value.lngValue || 120,
        currentMapPoint.value.latValue || 30
      ),
      offset: new AMap.Pixel(-10, -10),
      icon: icon, //添加 icon 图标 URL
      size: 30
    })
    map.add(marker)
    // iconLayer.setData([
    //   {
    //     geometry: {
    //       type: 'Point',
    //       coordinates: [currentMapPoint.value.lngValue, currentMapPoint.value.latValue]
    //     }
    //   }
    // ])
  }

  // 创建自定义覆盖物DOM
  function createTextDOM({ text, color }: { text: string; color?: string }) {
    const dom = document.createElement('div')
    dom.style.backgroundColor = color || '#1678FF'
    dom.style.fontSize = '12px'
    dom.style.fontWeight = 'bold'
    dom.style.display = 'flex'
    dom.style.justifyContent = 'center'
    dom.style.alignItems = 'center'
    dom.style.width = '30px'
    dom.style.height = '30px'
    //禁止文本换行
    dom.style.whiteSpace = 'nowrap'
    dom.innerHTML = text
    dom.className = 'size-[30px] rounded-full text-white'
    dom.draggable = false
    return dom
  }

  let startPointOverlay: any

  let endPointOverlayList: any

  function setStartEndPointByAddress(start: string, endList: string[]) {
    map.clearMap()
    // 创建自定义覆盖物
    // console.log(start, 'starrt')
    geoc.getLocation(start, function (status, result) {
      if (status === 'complete' && result.info === 'OK') {
        // result中对应详细地理坐标信息
        // console.log(result)
        const circle = new AMap.Marker({
          content: createTextDOM({ text: '起点', color: '#36B336' }), //自定义点标记覆盖物内容
          position: [result.geocodes[0].location.lng, result.geocodes[0].location.lat], //基点位置
          offset: new AMap.Pixel(-13, -30) //相对于基点的偏移位置
        })
        map.add(circle)
      }
    })

    if (endPointOverlayList && endPointOverlayList.length) {
      for (const endOverlay of endPointOverlayList) {
        map.removeOverlay(endOverlay)
      }
    } else {
      endPointOverlayList = []
    }
    endList.forEach((item) => {
      geoc.getLocation(item, function (status, result) {
        if (status === 'complete' && result.info === 'OK') {
          // result中对应详细地理坐标信息
          // console.log(result)
          const circle = new AMap.Marker({
            content: createTextDOM({ text: '终点', color: '#E04F48' }), //自定义点标记覆盖物内容
            position: [result.geocodes[0].location.lng, result.geocodes[0].location.lat], //基点位置
            offset: new AMap.Pixel(-13, -30) //相对于基点的偏移位置
          })
          map.add(circle)
        }
      })
    })
  }

  onMounted(() => {
    map = new AMap.Map(mapRef.value) // 创建Map实例

    // map.centerAndZoom('杭州', 12) // 初始化地图,设置中心点坐标和地图级别
    // geoc.getLocation('杭州');
    // map.enableScrollWheelZoom(true) // 开启鼠标滚
    //添加路线
    // viewer = new mapvgl.View({
    //   map: map
    // })
    // viewer.startAnimation()
  })
  return {
    getMap,
    getViewer,
    mapPoints,
    currentMapPoint,
    geoc,
    setMapPoints,
    setStartEndPointByAddress
  }
}
