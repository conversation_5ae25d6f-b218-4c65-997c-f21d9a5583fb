<script lang="ts" setup>
import { computed, onMounted, ref, unref, watch } from 'vue'
import { useAppStore } from '@/store/modules/app'
import { useDesign } from '@/hooks/web/useDesign'
import router from '@/router'
import {storeToRefs} from "pinia"
import { useSysConfigStore } from '@/store/modules/sysConfig'
import logoPng from "@/assets/imgs/logo.png"

defineOptions({ name: 'Logo' })

const {sysConfig,logoUrl} = storeToRefs(useSysConfigStore())

const { getPrefixCls } = useDesign()

const prefixCls = getPrefixCls('logo')

const appStore = useAppStore()

const show = ref(true)

const title = computed(() => appStore.getMenuTile)

const layout = computed(() => appStore.getLayout)

const collapse = computed(() => appStore.getCollapse)

onMounted(() => {
  if (unref(collapse)) show.value = false
})

watch(
  () => collapse.value,
  (collapse: boolean) => {
    if (unref(layout) === 'topLeft' || unref(layout) === 'cutMenu') {
      show.value = true
      return
    }
    if (!collapse) {
      setTimeout(() => {
        show.value = !collapse
      }, 400)
    } else {
      show.value = !collapse
    }
  }
)

watch(
  () => layout.value,
  (layout) => {
    if (layout === 'top' || layout === 'cutMenu') {
      show.value = true
    } else {
      if (unref(collapse)) {
        show.value = false
      } else {
        show.value = true
      }
    }
  }
)

function toIndex() {
  router.push({ path: '/' })
}
</script>

<template>
  <div>
    <div
    @click="toIndex"
      :class="[
        prefixCls,
        layout !== 'classic' ? `${prefixCls}__Top` : '',
        'cdf padding-sm items-center cursor-pointer  relative decoration-column overflow-hidden'
      ]"
    >
      <img
        class="h-[calc(var(--logo-height)-5px)] w-[calc(var(--logo-height))]"
        :src="sysConfig.logo?logoUrl:logoPng"
      />
      <div
        v-if="show"
        :class="[
          'mt-10px text-16px font-700',
          {
            'text-[var(--logo-title-text-color)]': layout === 'classic',
            'text-[var(--top-header-text-color)]':
              layout === 'topLeft' || layout === 'top' || layout === 'cutMenu'
          }
        ]"
      >
       {{ title }}
      </div>
    </div>
  </div>
</template>
