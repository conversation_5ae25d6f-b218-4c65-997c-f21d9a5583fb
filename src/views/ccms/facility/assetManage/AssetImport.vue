<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <div class="batch-import">
      <SelectFile
        class="select-file"
        accept=".xls,.xlsx,.XLS,.XLSX"
        :drag="true"
        @update:out-files="fileSelect"
        :show-file-list="false"
      >
        <template #default>
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="tips"
            >文件后缀名必须为xls 或xlsx （即Excel格式） 文件大小不得大于10M，最多支持导入3000条数据
          </div>
        </template>
      </SelectFile>
      <div class="file-list" v-if="isFile">
        <div class="left">
          <i class="icon icon-uniE020"></i>
          <span>{{ file.name }}</span>
        </div>
        <div class="right">
          <el-icon v-if="isOverSize" class="fail">
            <WarningFilled />
          </el-icon>
          <el-icon class="success" v-else>
            <SuccessFilled />
          </el-icon>
          <!-- 删除-->
          <el-icon class="remove" @click="removeFile">
            <CircleCloseFilled />
          </el-icon>
        </div>
      </div>
      <div class="notice" v-if="isOverSize">文件大小超过10M</div>
    </div>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { ref } from 'vue'
import SelectFile from '@/components/SelectFile.vue'
import { CircleCloseFilled, SuccessFilled, WarningFilled } from '@element-plus/icons-vue'

const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const file = ref<any>({})

const isFile = computed(() => {
  return JSON.stringify(file.value) !== '{}'
})

const isOverSize = computed(() => {
  return file.value.size / 1024 / 1024 >= 10
})

/** 打开弹窗 */
const open = async () => {
  dialogVisible.value = true
  dialogTitle.value = '导入数据'
  file.value = {}
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['import-excel']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 提交请求
  formLoading.value = true
  try {
    if (isFile.value && !isOverSize.value) {
      const p = new Promise((resolve, reject) => {
        emit('import-excel', file.value, resolve, reject)
      })
      await p
    } else {
      message.error('请选择文件')
      return
    }
    dialogVisible.value = false
  } finally {
    formLoading.value = false
  }
}

function fileSelect(e: any) {
  if (e) {
    file.value = e
  }
}

function removeFile() {
  file.value = {}
}
</script>
<style lang="scss" scoped>
.batch-import {
  .select-file {
    width: 100%;

    .tips {
      margin: 10px 0;
      padding: 0 40px;
      font-size: 12px;
      color: #aaaaaa;
    }

    :deep(.el-upload) {
      width: 100%;

      .el-upload-dragger {
        width: 100%;
      }
    }
  }

  .file-list {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 30px;
    line-height: 30px;
    font-size: 13px;
    margin-bottom: 10px;
    padding: 0 20px;
    background: rgba(0, 110, 255, 0.05);

    .left {
      display: flex;
      align-items: center;

      .icon-uniE020 {
        margin-right: 4px;
      }
    }

    .right {
      display: flex;
      align-items: center;
    }

    .success {
      color: #36b34a;
      margin-right: 10px;
    }

    .remove {
      cursor: pointer;
      color: #cccccc;
    }

    .fail {
      color: red;
      margin-right: 10px;
    }
  }

  .notice {
    color: red;
    font-size: 12px;
  }
}
</style>
