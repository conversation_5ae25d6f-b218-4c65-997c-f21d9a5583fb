<template>
  <!-- 列表 -->
  <ContentWrap class="full-height">
    <vxe-grid ref="gridRef" v-bind="gridOptions" v-on="gridEvents">
      <template #status="{ row }">
        <dict-tag :type="DICT_TYPE.ASSETS_STATUS" :value="row.status" />
      </template>
      <template #filter_status="{ column }">
        <div v-for="(option, index) in column.filters" :key="index">
          <vxe-select
            v-model="option.data"
            :options="getStrDictOptions(DICT_TYPE.ASSETS_STATUS)"
            @change="changeFilter(option)"
          />
        </div>
      </template>
      <template #operate="{ row }">
        <el-button
          type="primary"
          size="small"
          @click="openForm('update', row)"
          v-hasPermi="['ccms:asset:editAsset']"
          plain
          >编辑
        </el-button>
        <el-button
          type="danger"
          size="small"
          @click="handleDelete(row.id)"
          v-hasPermi="['ccms:asset:deleteAsset']"
          plain
          >删除
        </el-button>
      </template>
    </vxe-grid>
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <AssetManageForm ref="formRef" @success="getList" />

  <GlobalConfigForm ref="globalConfigRef" @success="getList" />
  <!-- 搜索抽屉 -->
  <SearchDrawer ref="searchRef" v-model="queryParams" @search="getList" />

  <AssetImport ref="assetImportRef" @import-excel="importExcel" />
</template>

<script setup lang="ts">
import AssetManageForm from './AssetManageForm.vue'
import type { VxeGridInstance, VxeGridListeners, VxeGridProps } from 'vxe-table'
import SearchDrawer from './SearchDrawer.vue'
import { tms_web } from '@/api/ccms'
import { DICT_TYPE, getStrDictOptions } from '@/utils/dict'
import { hasPermission } from '@/directives/permission/hasPermi'
import request from '@/config/axios'
import download from '@/utils/download'
import { ElLoading } from 'element-plus'
import AssetImport from './AssetImport.vue'
import { sysType } from '@/utils/sysType'
import GlobalConfigForm from './GlobalConfigForm.vue'
defineOptions({ name: 'AssetManage' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  simNo: undefined,
  assetsId: undefined,
  factoryId: undefined,
  status: undefined
})

const searchRef = ref()
const assetImportRef = ref()
const globalConfigRef = ref()
// 表格实例
const gridRef = ref<VxeGridInstance>()

// 调用vxe grid query方法重载表格
const getList = () => {
  if (gridRef.value) {
    gridRef.value.commitProxy('query') // 触发表格重新加载数据
  }
}
// 获取表格数据
const fetchApi = () => {
  return new Promise(async (resolve) => {
    const res = await tms_web.asset_queryAsset_post({ data: queryParams })
    gridOptions.data = res.data?.list || []
    resolve({
      page: {
        total: res.data?.total
      },
      result: res.data?.list
    })
  })
}

const gridOptions = reactive<
  VxeGridProps<GetApiResByListItem<typeof tms_web.asset_queryAsset_post>>
>({
  stripe: true,
  border: true,
  keepSource: true,
  height: '100%',
  columnConfig: {
    resizable: true
  },
  pagerConfig: {
    enabled: true,
    pageSize: 10
  },
  editConfig: {
    trigger: 'click',
    mode: 'row',
    showStatus: true
  },
  filterConfig: {
    remote: true
  },
  toolbarConfig: {
    zoom: true,
    custom: true,
    tools: [
      { name: '', code: 'search', icon: 'vxe-icon-search', status: 'info' },
      { name: '', code: 'customExport', icon: 'vxe-table-icon-download', status: 'info' },
      { name: '', code: 'customImport', icon: 'vxe-table-icon-upload', status: 'info' }
    ]
  },
  proxyConfig: {
    props: {
      result: 'result',
      total: 'page.total'
    },
    ajax: {
      // 接收 Promise
      query: ({ page }) => {
        queryParams.pageNo = page.currentPage
        queryParams.pageSize = page.pageSize
        return fetchApi()
      }
    }
  },
  columns: [
    { type: 'seq', width: 70 },
    { field: 'assetsId', title: '资产ID', minWidth: '120px' },
    { field: 'factoryName', title: '设备厂商', minWidth: '120px' },
    { field: 'assetsModel', title: '型号', minWidth: '120px' },
    { field: 'ssid', title: 'SSID', minWidth: '120px' },
    { field: 'imei', title: 'IMEI号', minWidth: '120px' },
    { field: 'simNo', title: 'SIM卡号', minWidth: '120px' },
    { field: 'activeWaitTime', title: '数据上传周期(分钟)', minWidth: '150px' },
    { field: 'intervalTime', title: '数据采集周期(分钟)', minWidth: '150px' },
    { field: 'tempCalibration', title: '温度校准值', minWidth: '120px' },
    { field: 'humCalibration', title: '湿度校准值', minWidth: '120px' },
    {
      field: 'periodStatus',
      title: '在线状态',
      minWidth: '120px'
    },
    {
      field: 'status',
      title: '状态',
      minWidth: '70px',
      filters: [
        {
          data: ''
        }
      ],
      slots: {
        default: 'status',
        filter: 'filter_status'
      }
    },
    { title: '操作', width: 200, slots: { default: 'operate' }, fixed: 'right' }
  ],
  data: []
})

//全局参数设置
if (hasPermission(['ccms:asset:editAssetWholeConfig'])) {
  gridOptions.toolbarConfig!.tools!.unshift({
    name: '全局参数',
    code: 'wholeConfig',
    icon: 'vxe-icon-setting',
    status: 'primary'
  })
}

//新增资产权限验证
if (hasPermission(['ccms:asset:saveAsset'])) {
  gridOptions.toolbarConfig!.tools!.unshift({
    name: '新增资产',
    code: 'add',
    icon: 'vxe-icon-add',
    status: 'primary'
  })
}

const changeFilter = (option) => {
  const $grid = gridRef.value
  if ($grid) {
    $grid.updateFilterOptionStatus(option, !!option.data)
  }
}

const gridEvents: VxeGridListeners = {
  filterChange(params) {
    queryParams.status = params.datas[0]
    getList()
  },
  toolbarToolClick({ code }) {
    switch (code) {
      case 'search':
        handelSearch()
        break
      case 'add':
        openForm('create')
        break
      case 'wholeConfig':
        globalConfigRef.value.open()
        break
      case 'customExport':
        handleExport()
        break
      case 'customImport':
        assetImportRef.value.open()
        break
    }
  }
}

const handelSearch = () => {
  searchRef.value?.open()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, rowData?: Required<typeof gridOptions>['data'][number]) => {
  formRef.value.open(type, rowData)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await tms_web.asset_deleteAsset_post({ data: { id } })
    message.success(t('common.delSuccess'))
    // 刷新列表
    getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.confirm('是否下载资产导入模版.xlsx?')
    ElLoading.service({
      lock: true,
      text: '正在下载'
    })
    const data = await request.download({
      url: `/${sysType}/asset/exportExcelTemplate`,
      method: 'post'
    })
    download.excel(data, '资产导入模版.xlsx')
  } catch {
  } finally {
    ElLoading.service().close()
  }
}

async function importExcel(file: File, success: () => void, fail: () => void) {
  try {
    await request.upload({
      url: `/${sysType}/asset/importExcel`,
      data: { file }
    })
    message.success('导入成功')
    success()
    getList()
  } catch {
    message.error('请检查导入数据是否符合规范')
    fail()
  }
}
</script>
