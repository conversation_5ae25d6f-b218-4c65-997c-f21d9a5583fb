<template>
  <el-drawer v-model="drawerVisible" title="搜索" direction="rtl" size="350px">
    <template #header>
      <span class="text-16px font-700">搜索</span>
    </template>
    <div class="search-drawer" style="height: 100%">
      <el-form
        ref="queryFormRef"
        :inline="true"
        :model="queryParams"
        class="-mb-15px"
        label-width="68px"
      >
        <el-form-item label="SIM卡号" prop="simNo">
          <el-input
            v-model="queryParams.simNo"
            placeholder="请输入SIM卡号"
            clearable
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="资产ID" prop="assetsId">
          <el-input
            v-model="queryParams.assetsId"
            placeholder="请输入资产ID"
            clearable
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="设备厂商" prop="factoryId">
          <el-select
            v-model="queryParams.factoryId"
            placeholder="请选择设备厂商"
            clearable
            class="!w-240px"
          >
            <el-option
              v-for="(item, index) in factoryList"
              :key="index"
              :label="item.name"
              :value="item.id || ''"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div style="flex: auto">
        <el-button @click="reset">重置</el-button>
        <el-button type="primary" @click="search">搜索</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { tms_web } from '@/api/ccms'

// 定义 props 和 emits
const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update:modelValue', 'search', 'reset'])

// 控制抽屉显示
const drawerVisible = ref(false)

// 使用 computed 实现双向绑定
const queryParams = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const factoryList = ref<GetApiResByList<typeof tms_web.factoryManage_queryFactoryManage_post>>([])

function queryFactoryList() {
  //查询厂商列表
  tms_web
    .factoryManage_queryFactoryManage_post({
      data: {
        pageNo: 1,
        pageSize: 999999
      }
    })
    .then((res) => {
      factoryList.value = res.data?.list || []
    })
}

// 打开抽屉
function open() {
  queryFactoryList()
  drawerVisible.value = true
}

// 关闭抽屉
function close() {
  drawerVisible.value = false
}

// 搜索
function search() {
  close()
  emit('search', queryParams.value)
}

const queryFormRef = ref()

// 重置
function reset() {
  queryFormRef.value.resetFields()
  emit('reset', queryParams.value)
}

// 暴露 open 方法
defineExpose({
  open
})
</script>

<style lang="scss" scoped>
.search-drawer {
  ::v-deep {
    .el-form {
      height: 98%;
    }
  }
}
</style>
