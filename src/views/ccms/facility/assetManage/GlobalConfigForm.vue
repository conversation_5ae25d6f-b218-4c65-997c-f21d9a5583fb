<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="150px"
      v-loading="formLoading"
    >
      <el-form-item label="数据上传周期(分钟)" prop="activeWaitTime">
        <el-input v-model="formData.activeWaitTime" placeholder="默认5分钟" />
      </el-form-item>
      <el-form-item label="数据采集周期(分钟)" prop="intervalTime">
        <el-input v-model="formData.intervalTime" placeholder="默认5分钟" />
      </el-form-item>
      <el-form-item label="温度校准值" prop="tempCalibration">
        <el-input v-model="formData.tempCalibration" placeholder="默认0.1" />
      </el-form-item>
      <el-form-item label="湿度校准值" prop="humCalibration">
        <el-input v-model="formData.humCalibration" placeholder="默认0.1" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { ref } from 'vue'
import { tms_web } from '@/api/ccms'

const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formData = ref<GetApiParams<typeof tms_web.asset_saveAsset_post>>({})
const formRules = reactive({
  activeWaitTime: [{ required: true, message: '请输入数据上传周期(分钟)', trigger: 'blur' }],
  intervalTime: [{ required: true, message: '请输入数据采集周期(分钟)', trigger: 'blur' }],
  tempCalibration: [{ required: true, message: '请输入温度校准值', trigger: 'blur' }],
  humCalibration: [{ required: true, message: '请输入湿度校准值', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async () => {
  dialogVisible.value = true
  dialogTitle.value = '全局参数配置'
  resetForm()
  formData.value.activeWaitTime = 5
  formData.value.intervalTime = 5
  formData.value.tempCalibration = 0.1
  formData.value.humCalibration = 0.1
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗
/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value
    await tms_web.asset_editAssetWholeConfig_post({ data })
    message.success('操作成功')
    emit("success")
    dialogVisible.value = false
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {}
  formRef.value?.resetFields()
}
</script>
