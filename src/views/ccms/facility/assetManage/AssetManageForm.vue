<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="150px"
      v-loading="formLoading"
    >
      <el-form-item label="资产ID" prop="assetsId">
        <el-input v-model="formData.assetsId" placeholder="请输入资产ID" />
      </el-form-item>
      <el-form-item label="设备厂商" prop="factoryId">
        <el-select v-model="formData.factoryId" placeholder="请选择设备厂商">
          <el-option
            v-for="(item, index) in factoryList"
            :key="index"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="SIM卡号" prop="simNo">
        <el-input v-model="formData.simNo" placeholder="请输入SIM卡号" />
      </el-form-item>
      <el-form-item label="型号" prop="assetsModel">
        <el-input v-model="formData.assetsModel" placeholder="请输入型号" />
      </el-form-item>
      <el-form-item label="SSID" prop="ssid">
        <el-input v-model="formData.ssid" placeholder="请输入SSID" />
      </el-form-item>
      <el-form-item label="IMEI号" prop="imei">
        <el-input v-model="formData.imei" placeholder="请输入IMEI号" />
      </el-form-item>
      <el-form-item label="数据上传周期(分钟)" prop="activeWaitTime">
        <el-input v-model="formData.activeWaitTime" placeholder="默认5分钟" />
      </el-form-item>
      <el-form-item label="数据采集周期(分钟)" prop="intervalTime">
        <el-input v-model="formData.intervalTime" placeholder="默认5分钟" />
      </el-form-item>
      <el-form-item label="温度校准值" prop="tempCalibration">
        <el-input v-model="formData.tempCalibration" placeholder="默认0.1" />
      </el-form-item>
      <el-form-item label="湿度校准值" prop="humCalibration">
        <el-input v-model="formData.humCalibration" placeholder="默认0.1" />
      </el-form-item>
      <el-form-item label="状态变更：" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio
            :key="item.value"
            :label="item.label"
            :value="item.value"
            v-for="item in getStrDictOptions(DICT_TYPE.ASSETS_STATUS)"
          />
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { ref } from 'vue'
import { tms_web } from '@/api/ccms'
import { cloneDeep } from 'lodash-es'
import { removeCommonField } from '@/utils/businessData'
import { DICT_TYPE, getStrDictOptions } from '@/utils/dict'

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref<GetApiParams<typeof tms_web.asset_saveAsset_post>>({})
const formRules = reactive({
    assetsId: [
        { required: true, message: "请输入资产ID", trigger: "blur" }
    ],
    factoryId: [
        { required: true, message: "请选择设备厂商", trigger: "change" }
    ],
    activeWaitTime: [
        { required: true, message: "请输入数据上传周期(分钟)", trigger: "blur" }
    ],
    intervalTime: [
        { required: true, message: "请输入数据采集周期(分钟)", trigger: "blur" }
    ],
    tempCalibration: [
        { required: true, message: "请输入温度校准值", trigger: "blur" }
    ],
    humCalibration: [
        { required: true, message: "请输入湿度校准值", trigger: "blur" }
    ]
})
const formRef = ref() // 表单 Ref

const factoryList = ref<any[]>([])

/** 打开弹窗 */
const open = async (type: string, rowData?: typeof formData.value) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()

  //查询厂商列表
  tms_web
    .factoryManage_queryFactoryManage_post({
      data: {
        pageNo: 1,
        pageSize: 999999
      }
    })
    .then((res) => {
      factoryList.value = res.data?.list || []
    })

  // 修改时，设置数据
  if (rowData) {
    formLoading.value = true
    try {
      formData.value = removeCommonField(cloneDeep(rowData))
    } finally {
      formLoading.value = false
    }
  } else {
    //设置状态默认值
    const assetsStatusDicList = getStrDictOptions(DICT_TYPE.ASSETS_STATUS)
    if (assetsStatusDicList && assetsStatusDicList[0]) {
      formData.value.status = assetsStatusDicList[0].value
    }
    formData.value.activeWaitTime = 5
    formData.value.intervalTime = 5
    formData.value.tempCalibration = 0.1
    formData.value.humCalibration = 0.1
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value
    if (formType.value === 'create') {
      await tms_web.asset_saveAsset_post({ data })
      message.success(t('common.createSuccess'))
    } else {
      await tms_web.asset_editAsset_post({ data })
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {}
  formRef.value?.resetFields()
}
</script>
