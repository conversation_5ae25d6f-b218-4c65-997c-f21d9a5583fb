<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="方案名称：" prop="name">
        <el-input size="large" type="text" v-model.trim="formData.name" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="资产型号：" prop="assetModel">
        <el-input
          size="large"
          type="text"
          v-model.trim="formData.assetModel"
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item label="适用温度：" prop="safeTemp">
        <el-input size="large" type="text" v-model.trim="formData.safeTemp" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="适用湿度：" prop="safeHumidity">
        <el-input
          size="large"
          type="text"
          v-model.trim="formData.safeHumidity"
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item label="最少租赁时长：" prop="minLeaseTime">
        <el-input
          size="large"
          type="text"
          v-model.trim="formData.minLeaseTime"
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item label="租赁单价：" prop="leasePrice">
        <el-input
          size="large"
          type="text"
          v-model.trim="formData.leasePrice"
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item label="销售单价：" prop="salePrice">
        <el-input size="large" type="text" v-model.trim="formData.salePrice" placeholder="请输入" />
      </el-form-item>

      <el-form-item class="file-label" label="产品图片" required>
        <el-upload
          v-model:file-list="fileList"
          class="upload-demo"
          multiple
          list-type="picture"
          :http-request="uploadFile"
          :on-remove="removeFile"
          :limit="1"
        >
          <el-button type="primary">点击上传</el-button>
          <template #tip>
            <div class="el-upload__tip">
              请上传图片格式文件，单个文件不超过5M。文件个数最多不超过1个。
            </div>
          </template>
        </el-upload>
        <!-- <file-upload
          @on-progress="uploadFile"
          :view="getType"
          type="picture"
          :file="path2"
          :file-length="1"
          @on-remove="removeFile"
        /> -->
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { tms_web } from '@/api/ccms'
import request from '@/config/axios'
import { ElLoading, ElMessage } from 'element-plus'
import { sysType } from '@/utils/sysType'

/** 合同信息 表单 */
defineOptions({ name: 'AddSalePlanIndex' })
const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
// 图片上传
const fileList = ref([])
// 文件上传
const uploadFile = async (file: any) => {
  console.log(file, 'file')

  try {
    ElLoading.service({
      lock: true,
      text: '正在保存...'
    })
    await request.upload({
      url: `/${sysType}/file/uploadFile`,
      data: {
        file: file.file,
        fileType: 'picture',
        modulType: 'salePlan'
      }
    })
    //提示上传成功
    ElMessage({
      message: '上传成功',
      type: 'success'
    })
  } finally {
    ElLoading.service().close()
  }
}
const getType = () => {
  return 'edit'
}
// 删除图片
const removeFile = (data: any) => {
  tms_web
    .file_delete_post({
      data: {
        path: data.address
      }
    })
    .then(() => {
      spliceArr(path2, data.address)
      ElMessage({
        message: '删除成功',
        type: 'success'
      })
      // $notify({
      //   title: "成功",
      //   message: "删除成功",
      //   type: "success"
      // });
    })
}
const spliceArr = (list: any, address: string) => {
  let index = list.findIndex((item: any) => item.address === address)
  if (index !== -1) {
    list.splice(index, 1)
  }
}
const path2: any = []
const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const updateId = ref(null)
const formData = reactive({
  name: undefined,
  assetModel: undefined,
  safeHumidity: undefined,
  safeTemp: undefined,
  minLeaseTime: undefined,
  leasePrice: undefined,
  salePrice: undefined
})
const formRules = reactive({
  name: [{ required: true, message: '方案名称', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref
interface Object {
  id: any
  name: any
  contactsName: any
  phone: any
  address: any
}
/** 打开弹窗 */
const loading = ref(false)
const open = async (type: string, row: any) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  console.log(row)
  resetForm()
  // 修改时，设置数据
  if (row?.id) {
    updateId.value = row.id
    Object.assign(formData, row)
    //查询厂商列表
    loading.value = true
    tms_web
      .assetSale_getAssetsSalePlan_post({
        data: {
          id: updateId.value
        }
      })
      .then((res: any) => {
        if (res && res.data) {
          for (let item of res.data.fileList ? res.data.fileList : []) {
            // console.log(item);
            fileList.value.push(item)
          }
        }
      })
      .finally(() => {
        loading.value = false
      })
    // formData.value.name = row.name
    // formData.value.contactsName = row.contactsName
    // formData.value.phone = row.phone
    // formData.value.address = row.address

    // formLoading.value = true
    // try {
    //   formData.value = await ContractMessageApi.getContractMessage(id)
    // } finally {
    //   formLoading.value = false
    // }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗
// id更新/新增
// const  expenseType= async()=> {
//     try {
//       if (id) {
//         await tms_web.factoryManage_editFactoryManage_post({
//           data: {
//             id: id,
//             ...formData
//           }
//         });
//         $notify({
//           title: "成功",
//           message: "修改成功！",
//           type: "success"
//         });
//       } else {
//         await tms_web.factoryManage_saveFactoryManage_post({
//           data: {
//             ...formData
//           }
//         });
//       }
//       $emit("on-submit");
//       $refs.open.close();
//     } catch (e) {
//       console.log(e);
//     }
//   }
/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData
    if (formType.value === 'create') {
      // console.log(data, 'formType');
      await tms_web.assetSale_saveAssetsSalePlan_post({
        data: {
          ...data
        }
      })
      // console.log(res, '新增成功');

      message.success(t('common.createSuccess'))
    } else {
      await tms_web.assetSale_updateAssetsSalePlan_post({
        data: {
          id: updateId.value,
          ...data
        }
      })
      // console.log(res, '修改成功');

      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formRef.value?.resetFields()
}
</script>
