<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      v-loading="formLoading"
    >
      <!-- <el-form-item label="快递名称：" prop="assetsIdsList">
        <el-input
          size="large"
          type="text"
          v-model.trim="formData.assetsIdsList"
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item label="快递单号：" prop="fastMailNumber">
        <el-input
          size="large"
          type="text"
          v-model.trim="formData.fastMailNumber"
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item label="" prop="stageBilling">
        <StageBilling :detailList="detailList" ref="stageBillingRef" />
      </el-form-item> -->
      <el-form-item label="" prop="stageBilling">
        <StageBilling :detailList="detailList" @change-items="changeItem()" ref="stageBillingRef" />
      </el-form-item>

      <el-form-item label="">
        <SelectFile
          class="mr-3"
          :showFileList="false"
          accept=".xlsx,.xls"
          @update:out-files="selectImportFile"
        >
          <el-button type="primary"> 批量导入 </el-button>
        </SelectFile>
      </el-form-item>

      <el-form-item label="" prop="assetsIdsList">
        <el-input
          size="large"
          type="textarea"
          v-model="formData.assetsIdsList"
          placeholder=""
          disabled
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { tms_web } from '@/api/ccms'
import StageBilling from './StageBilling.vue'
import SelectFile from '@/components/SelectFile.vue'
import { ElMessage } from 'element-plus'

/** 合同信息 表单 */
defineOptions({ name: 'ReturnShipments' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
// const formType = ref('') // 表单的类型：create - 新增；update - 修改
const updateId = ref()
const formData = ref({
  assetsIdsList: undefined
})
const formRules = reactive({
  assetsIdsList: [{ required: true, message: '快递名称', trigger: 'blur' }],
  fastMailNumber: [{ required: true, message: '快递单号', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref
interface Object {
  id: any
  assetsIdsList: any
}
// detailList
const detailList = ref([])
/** 打开弹窗 */
const open = async (row?: Object) => {
  dialogVisible.value = true
  // dialogTitle.value = t('action.' + type)
  // formType.value = type
  resetForm()
  // 修改时，设置数据
  if (row?.id) {
    updateId.value = row.id
  }
}
const stageBillingRef = ref()

defineExpose({ open }) // 提供 open 方法，用于打开弹窗
//监听动态输入框的变化
const changeItem = () => {
  let temp = stageBillingRef.value.items.map((item: any) => item.assetsId).join(',')
  formData.value.assetsIdsList = temp
}
const selectImportFile = async (file: File | File[]) => {
  if (!file) {
    return
  }
  let data
  try {
    data = file as File
    let temp: any = []

    //循环动态添加数据
    let itemList: any = []
    for (let item of stageBillingRef.value.items) {
      itemList.push(item.assetsId)
    }

    //循环导入数据
    for (let item of data) {
      let tmp = item['资产id']
      temp.push(tmp)

      //将数据 设置到 动态添加列表
      if (stageBillingRef.value.items[0].assetsId === '') {
        stageBillingRef.value.items[0].assetsId = tmp
      } else {
        //判断是否已经存在，存在的话就不再加入
        if (!itemList.includes(tmp)) {
          stageBillingRef.value.items.push({
            assetsId: tmp
          })
        }
      }
    }
  } catch (error) {
    console.error(error)
    ElMessage({
      message: '数据导入失败,请检查模版格式',
      type: 'error'
    })
    return
  }
  //数据不能为空
  if (!data || !data.length) {
    ElMessage({
      message: '导入数据为空',
      type: 'error'
    })
    return
  }
}
/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    // let temp = stageBillingRef.value.items.map((item: any) => item.assetsId).join(',')
    const data = formData.value
    await tms_web
      .assetSale_assetWarehousing_post({
        data: {
          ...data
        }
      })
      .then((res) => {
        console.log(res, '资产新增成功');
        message.success(t('common.updateSuccess'))
      })

    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    assetsIdsList: undefined
  }
  formRef.value?.resetFields()
}
</script>
