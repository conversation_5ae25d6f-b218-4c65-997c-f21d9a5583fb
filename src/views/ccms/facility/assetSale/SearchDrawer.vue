<template>
  <el-drawer v-model="drawerVisible" title="搜索" direction="rtl" size="350px">
    <template #header>
      <span class="text-16px font-700">搜索</span>
    </template>
    <div class="search-drawer" style="height: 100%">
      <el-form
        ref="queryFormRef"
        :inline="true"
        :model="queryParams"
        class="-mb-15px"
        label-width="68px"
        v-if="modelValue.dataCategory === 'sale'"
      >
        <el-form-item label="客户名称" prop="customerName">
          <el-input
            v-model="queryParams.customerName"
            placeholder="请输入"
            clearable
            class="!w-240px"
          />
        </el-form-item>

        <el-form-item label="联系人" prop="contactsName">
          <el-input
            v-model="queryParams.contactsName"
            placeholder="请输入"
            clearable
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="联系人电话" prop="contactsPhone">
          <el-input
            v-model="queryParams.contactsPhone"
            placeholder="请输入"
            clearable
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="租售状态">
          <el-select
            class="!w-240px"
            style="width: 250px"
            v-model="queryParams.status"
            placeholder="请选择"
            clearable
            filterable
          >
            <el-option
              v-for="(item, index) in levelList"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="订单时间" v-if="modelValue.status === 'history'" prop="dataTimeList">
          <el-date-picker
            v-model="dataTimeList"
            @change="dataTimeFn"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-240px"
          />
        </el-form-item>
      </el-form>
      <el-form
        ref="queryFormRef"
        :inline="true"
        :model="PlanParams"
        class="-mb-15px"
        label-width="68px"
        v-else
      >
        <el-form-item label="方案名称" prop="name">
          <el-input
            v-model="PlanParams.name"
            placeholder="请输入方案名称"
            clearable
            class="!w-240px"
          />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div style="flex: auto">
        <el-button @click="reset()">重置</el-button>
        <el-button type="primary" @click="search">搜索</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { getStrDictOptions } from '@/utils/dict'
defineOptions({ name: 'AssetSaleInfoPage' })

const levelList = ref(getStrDictOptions('sale_status'))

// 定义 props 和 emits
const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update:modelValue', 'search', 'reset'])
// 时间段数组
const dataTimeList = ref([])
// 处理时间函数
const dataTimeFn = (val: any) => {
  queryParams.value.startTime = val ? val[0] : ''
  queryParams.value.endTime = val ? val[1] : ''
}
// 控制抽屉显示
const drawerVisible = ref(false)
interface queryParams {
  customerName: any
  accidentLevel: any
  contactsName: any
  startTime: any
  endTime: any
  contactsPhone: any
  status: any
}
// 使用 computed 实现双向绑定
const queryParams = ref({
  customerName: undefined,
  accidentLevel: undefined,
  contactsName: undefined,
  startTime: '',
  endTime: '',
  contactsPhone: undefined,
  status: undefined
})
const PlanParams = ref({
  name: ''
})

// 打开抽屉
function open() {
  drawerVisible.value = true
}

// 关闭抽屉
function close() {
  drawerVisible.value = false
}

// 搜索
function search() {
  close()
  // console.log(queryParams.value);
  if (props.modelValue.dataCategory === 'sale') {
    emit('search', queryParams.value)
  } else {
    emit('search', PlanParams.value)
  }
}

const queryFormRef = ref()
// 重置
function reset() {
  queryFormRef.value?.resetFields()
  dataTimeList.value.length = 0
  queryParams.value.startTime = ''
  queryParams.value.endTime = ''
  emit('reset', queryParams.value)
}

// 暴露 open 方法
defineExpose({
  open,
  reset
})
</script>

<style lang="scss" scoped>
.search-drawer {
  ::v-deep {
    .el-form {
      height: 98%;
    }
  }
}
</style>
