<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <!-- <el-form-item label="厂商名称" required prop="name">
        <el-input v-model="formData.name" placeholder="请输入厂商名称" />
      </el-form-item> -->
      <el-form-item label="客户名称：" prop="customerId">
        <el-select
          class="customer-name"
          style="width: 420px"
          v-model.number="formData.customerId"
          placeholder="请选择"
          clearable
          filterable
        >
          <el-option
            v-for="(item, index) in customerList"
            :key="index"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="联系人：" prop="contactsName">
        <el-input
          type="text"
          style="width: 420px"
          v-model.trim="formData.contactsName"
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item label="联系电话：" prop="contactsPhone">
        <el-input
          style="width: 420px"
          type="text"
          v-model.trim="formData.contactsPhone"
          placeholder="请输入"
        />
      </el-form-item>

      <el-form-item label="租售方案：" prop="saleConfigId">
        <el-select
          class="customer-name"
          style="width: 420px"
          v-model="formData.saleConfigId"
          placeholder="请选择"
          clearable
          filterable
          @change="changeSaleConfig(formData.saleConfigId)"
        >
          <el-option
            v-for="(item, index) in saleConfigList"
            :key="index"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="租售类型：" prop="saleType" required>
        <el-radio-group
          @change="changeSaleConfig(formData.saleConfigId)"
          v-model="formData.saleType"
        >
          <el-radio label="lease" :value="'lease'">租赁</el-radio>
          <el-radio label="sale" :value="'sale'">销售</el-radio>
        </el-radio-group>
      </el-form-item>
      <!-- <el-form-item label="联系人" required prop="contactsName">
        <el-input v-model="formData.contactsName" placeholder="请输入联系人" />
      </el-form-item> -->
      <el-form-item v-if="formData.saleType === 'lease'" label="租赁时长：" prop="leaseTime">
        <el-input-number
          v-model.trim="formData.leaseTime"
          placeholder="请输入"
          style="width: 395px"
        />
        <span style="padding: 0 10px;">月</span>
      </el-form-item>
      <el-form-item label="数  量：" prop="number">
        <el-input-number v-model.trim="formData.number" placeholder="请输入" style="width: 395px" />
        <span style="padding: 0 10px;">套</span>
      </el-form-item>

      <el-form-item label="单  价：" prop="price">
        <el-input style="width: 420px" type="number" v-model.trim="formData.price" disabled />
      </el-form-item>
      <el-form-item label="总  价：" prop="totalPrice">
        <el-input style="width: 420px" type="number" v-model.trim="formData.totalPrice" disabled />
      </el-form-item>

      <el-form-item label="支付状态：" prop="status" required>
        <el-radio-group v-model="formData.status">
          <el-radio label="not_send">已支付</el-radio>
          <el-radio label="not_pay">未支付</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="备  注：" prop="remark">
        <el-input type="textarea" v-model.trim="formData.remark" placeholder="请输入" />
      </el-form-item>
      <!-- <el-form-item label="联系电话" required prop="phone">
        <el-input v-model="formData.phone" placeholder="请输入联系人电话" />
      </el-form-item>
      <el-form-item label="地址" required prop="address">
        <el-input v-model="formData.address" placeholder="请输入地址" />
      </el-form-item> -->
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { tms_web } from '@/api/ccms'

/** 合同信息 表单 */
defineOptions({ name: 'AddSaleOrderForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const loading = ref()
const saleConfigList = ref([])
const customerList = ref([])
const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const updateId = ref(null)
const formData = reactive({
  customerId: undefined,
  contactsPhone: undefined,
  saleConfigId: undefined,
  contactsName: undefined,
  phone: undefined,
  address: undefined,
  leaseTime: 0,
  number: 0,
  price: 0,
  totalPrice: 0,
  remark: undefined,
  status: undefined,
  saleType: 'lease',
  customerName: undefined
})
const formRules = reactive({
  customerId: [{ required: true, message: '请选择客户', trigger: 'blur' }],
  contactsName: [{ required: true, message: '请输入联系人', trigger: 'blur' }],
  contactsPhone: [{ required: true, message: '请输入联系电话', trigger: 'blur' }],
  saleConfigId: [{ required: true, message: '请选择租售方案', trigger: 'blur' }],
  leaseTime: [{ required: true, message: '请输入租赁时长', trigger: 'blur' }],
  number: [{ required: true, message: '请输入数量', trigger: 'blur' }],
  status:[{ required: true, message: '请选择支付状态', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref
// interface Object {
//   customerId: any
//   id: any
//   saleConfigId: any
//   contactsName: any
//   phone: any
//   address: any
//   leaseTime: any
//   number: any
//   price: any
//   totalPrice: any
//   remark: any
//   status: any
//   saleType: any
// }
/** 打开弹窗 */
const open = async (type: any, row?: any) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  //查询客户列表
  tms_web
    .customer_query_post({
      data: {}
    })
    .then((res: any) => {
      let list = res.data.list
      console.log(list,'list');
      
      customerList.value = list.map((item: any) => {
        return { name: item.name, id: Number(item.id) }
        // return item
      })
      console.log( customerList.value,' customerList.value');

      // console.log(res, '客户列表')
    })
    .finally(() => {
      loading.value = false
    })

  //查询销售方案列表
  tms_web
    .assetSale_queryAssetsSalePlan_post({
      data: {}
    })
    .then((res: any) => {
      saleConfigList.value = res.data.list
      // console.log(res, '销售方案列表')
    })
    .finally(() => {
      loading.value = false
    })
  // 修改时，设置数据
  if (row?.id) {
    updateId.value = row.id
    // Object.assign(formData, row)
    for (const key in formData) {
      if (Object.prototype.hasOwnProperty.call(formData, key)) {
        // const element = formData[key];
        formData[key] = row[key]
      }
    }

    // formData.name = row.name
    // formData.contactsName = row.contactsName
    // formData.phone = row.phone
    // formData.address = row.address

    // formLoading.value = true
    // try {
    //   formData.value = await ContractMessageApi.getContractMessage(id)
    // } finally {
    //   formLoading.value = false
    // }
  }
}
//选择销售方案时，回填价格和计算总价
const changeSaleConfig = (item: any) => {
  for (let i = 0; i < saleConfigList.value.length; i++) {
    let tmp = saleConfigList.value[i]
    if (item === saleConfigList.value[i].id) {
      if (formData.saleType === 'lease') {
        formData.price = tmp.leasePrice
      } else {
        formData.price = tmp.salePrice
      }
    }
  }
}
watch(
  () => [formData.number, formData.leaseTime,formData.price],
  (newValue, oldValue) => {
    // console.log(`count changed from ${oldValue} to ${newValue}`)
    if (formData.saleType === 'lease') {
      if (formData.price >= 0 && formData.number >= 0 && formData.leaseTime >= 0) {
        formData.totalPrice = (formData.price * formData.number * formData.leaseTime).toFixed(2)
      }
    } else {
      if (formData.price >= 0 && formData.number >= 0) {
        formData.totalPrice = (formData.price * formData.number).toFixed(2)
      }
    }
  },{ deep: true }
)
//选择销售方案时，回填价格和计算总价
// @Watch("formData.number", {
//   deep: true,
//   immediate: true
// })
//  const  watchList =()=> {
//     if (formData.price && formData.number) {
//       formData.totalPrice = (formData.price * formData.number).toFixed(2);
//     }
//   }

defineExpose({ open }) // 提供 open 方法，用于打开弹窗
// id更新/新增
// const  expenseType= async()=> {
//     try {
//       if (id) {
//         await tms_web.factoryManage_editFactoryManage_post({
//           data: {
//             id: id,
//             ...formData
//           }
//         });
//         $notify({
//           title: "成功",
//           message: "修改成功！",
//           type: "success"
//         });
//       } else {
//         await tms_web.factoryManage_saveFactoryManage_post({
//           data: {
//             ...formData
//           }
//         });
//       }
//       $emit("on-submit");
//       $refs.open.close();
//     } catch (e) {
//       console.log(e);
//     }
//   }
/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = { ...formData }
    for (let i = 0; i < customerList.value.length; i++) {
      let temp = customerList.value[i]
      if (data.customerId == temp.id) {
        data.customerName = temp.name
      }
    }
    if (formType.value === 'create') {
      // console.log(data, 'formType');
      await tms_web.assetSale_saveSalesOrders_post({
        data: {
          ...data
        }
      })
      // console.log(res, '新增成功');

      message.success(t('common.createSuccess'))
    } else {
      await tms_web
        .assetSale_updateSalesOrders_post({
          data: {
            id: updateId.value,
            ...data
          }
        })
        .then((data) => {
          console.log(data, '修改成功')
          message.success(t('common.updateSuccess'))
        })
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  // formData = {
  //   name: undefined,
  //   contactsName: undefined,
  //   phone: undefined,
  //   address: undefined
  // }
  formRef.value?.resetFields()
}
</script>
