<template>
  <div class="stage-billing">
    <!-- 表单区域 -->
    <table>
      <thead>
      <tr>
        <th>资产ID</th>
      </tr>
      </thead>
      <tbody>
      <tr v-for="(item, index) in items" :key="index">
        <td>
          <el-input porp="assetsId" v-model="item.assetsId" />
        </td>
        <td style="width:150px">
          <el-button @click="addItem" style="background-color: #7597db">+</el-button>
          <el-button @click="removeItem(index)" :disabled="items.length <= 1">-</el-button>
        </td>
      </tr>
      </tbody>
    </table>
  </div>
</template>

<script>
import { ref, watch } from "vue";
// import InfoField from "@/components/infoBlock/InfoField.vue";

export default {
  name: "StageBilling",
  props: {
    detailList: {
      type: Array,
      required: true
    }
  },
  setup(props) {
    const items = ref([{
      assetsId: ""
    }]);

    const addItem = () => {
      items.value.push({
        assetsId: ""
      });
    };

    const removeItem = index => {
      if (items.value.length > 1) {
        items.value.splice(index, 1);
      } 
    };
    //
    // // 验证表单有效性
    // watch(items, newValue => {
    //   // let isValid = false;
    //   //
    //   // for (let i = 0; i < newValue.length; i++) {
    //   //   if (newValue[i].assetsId) {
    //   //     isValid = true;
    //   //     break;
    //   //   }
    //   // }
    //   //
    //   // if (!isValid) {
    //   //   alert("设备ID不能为空");
    //   // }
    //   this.$emit("change-items");
    //
    // }, { deep: true });

    return {
      items,
      addItem,
      removeItem
    };
  },
  watch:{
    items:{
      handler(){
        // let isValid = false;
        //
        // for (let i = 0; i < newValue.length; i++) {
        //   if (newValue[i].assetsId) {
        //     isValid = true;
        //     break;
        //   }
        // }
        //
        // if (!isValid) {
        //   alert("设备ID不能为空");
        // }
        this.$emit("change-items");
      },
      deep:true
    }
  },
  methods: {
    setItems(data) {
      this.items = data;
    }
  },
 
  
};
</script>
<style>
.stage-billing {
  display: flex;
  flex-direction: column;
}

.stage-billing table {
  border-collapse: collapse;
  text-align: center;
}

.stage-billing th {
  font-weight: normal;
}

.stage-billing td {
  padding-right: 5px;

}

.stage-billing button {
  background-color: white;
  color: black;
  font-size: 13px;
  border-color: #dcdfe6;
  cursor: pointer;
  border-radius: 5px;
  margin-top: 5px;
}

.stage-billing button:hover {
  background-color: deepskyblue;
}

.stage-billing select {
  height: 40px;
  padding: 1px;
  border-color: #dcdfe6;
  margin-bottom: 5px;
}

.stage-billing input[type="number"] {
  height: 40px;
  padding: 1px;
  border-color: #dcdfe6;
  margin-bottom: 5px;
}
</style>