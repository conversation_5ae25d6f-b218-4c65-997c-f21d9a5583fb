<template>
  <!-- 列表 -->
  <ContentWrap class="full-height">
    <vxe-grid ref="gridRef" v-bind="gridOptions" v-on="gridEvents">
      <template #tools>
        <div v-if="queryParams.dataCategory === 'sale'">
          <vxe-button
            v-hasPermi="['ccms:assetSale:assetWarehousing']"
            status="primary"
            icon="vxe-icon-add"
            @click="ReturnShipmentsRefdealFn()"
            >资产入库</vxe-button
          >
          <VxeButton
            @click="dealFn('create')"
            v-hasPermi="['ccms:assetSale:saveSalesOrders']"
            status="primary"
            icon="vxe-icon-add"
            >新增租售单</VxeButton
          >
        </div>
        <div v-else>
          <vxe-button
            @click="SalePlandealFn('create')"
            v-hasPermi="['ccms:assetSale:saveAssetsSalePlan']"
            status="primary"
            >新增方案</vxe-button
          >
        </div>
        <VxeButton
          style="margin-left: 20px"
          @click="handelSearch"
          status="info"
          icon="vxe-icon-search"
        />
      </template>
      <template #toolbar_buttons>
        <VxeRadioGroup v-model="queryParams.dataCategory" @change="tabFn">
          <VxeRadioButton label="sale" content="租售订单" />
          <VxeRadioButton label="plan" content="租售方案" />
        </VxeRadioGroup>
      </template>
      <template #status="{ row }">
        <DictTag :type="DICT_TYPE.SALE_STATUS" :value="row.status" />
      </template>
      <template #type="{ row }">
        <DictTag :type="DICT_TYPE.SALE_TYPE" :value="row.saleType" />
      </template>
      <template #planstatus="{ row }">
        <DictTag :type="DICT_TYPE.ENABLE_STATUS" :value="row.status" />
      </template>
      <template #planbut="{ row }">
        <el-button
          type="primary"
          size="small"
          @click="updateStatus(row)"
          v-hasPermi="['ccms:assetSale:enable']"
          plain
          :class=" row.status == 'y' ? 'red' : 'blue' "
          >{{ row.status == 'n' ? '启用' : '停用' }}</el-button
        >
        <el-button
          type="primary"
          size="small"
          @click="SalePlandealFn('update', row)"
          v-hasPermi="['ccms:assetSale:updateAssetsSalePlan']"
          plain
          >编辑</el-button
        >
      </template>
      <template #operate="{ row }">
        <el-button
          type="primary"
          size="small"
          @click="goPage(row)"
          v-hasPermi="['ccms:assetSale:get']"
          plain
          >详情</el-button
        >
        <el-button
          type="primary"
          size="small"
          v-hasPermi="['ccms:assetSale:updateSalesOrders']"
          v-if="row.status === 'not_pay'"
          @click="dealFn('update', row)"
          plain
          >编辑</el-button
        >
        <el-button
          type="danger"
          size="small"
          v-if="row.status === 'not_pay'"
          @click="handleDelete(row.id)"
          v-hasPermi="['ccms:assetSale:deleteAssetSale']"
          plain
          >删除
        </el-button>
        <el-button
          type="primary"
          size="small"
          v-if="row.status === 'not_send'"
          @click="ShipmentsdealFn(row)"
          v-hasPermi="['ccms:assetSale:shipments']"
          plain
          >发货</el-button
        >
        <!-- <el-button
            type="danger"
            size="small"
            v-if="row.status !== 'finish'"
            @click="goPage('deal', row.id)"
            v-hasPermi="['ccms:accidentFaultDamageReport:exceptionHandling']"
            plain
            >处理</el-button
          > -->
      </template>
    </vxe-grid>
  </ContentWrap>
  <!-- 组件 -->
  <AddSaleOrder ref="SaleOrder" @success="getList" />
  <AddSalePlanIndex ref="SalePlan" @success="getList" />
  <AddShipments ref="Shipments" @success="getList" />
  <ReturnShipments ref="ReturnShipmentsRef" @success="getList" />
  <!-- 表单弹窗：添加/修改 -->
  <!-- <ContractMessageForm ref="imageRef" @success="getList" /> -->
  <!-- 搜索抽屉 -->
  <SearchDrawer ref="searchRef" v-model="queryParams" @search="getList" />
</template>

<script setup lang="ts">
import { dateFormatterByOptions } from '@/utils/formatTime'
// import download from '@/utils/download'
// import ContractMessageForm from './ContractMessageForm.vue'
import DictTag from '@/components/DictTag/src/DictTag.vue'
import { DICT_TYPE } from '@/utils/dict'
import { tms_web } from '@/api/ccms'
import type { VxeGridProps, VxeGridListeners, VxeGridInstance } from 'vxe-table'
import SearchDrawer from './SearchDrawer.vue'
import AddSaleOrder from './AddSaleOrder.vue'
import AddSalePlanIndex from './AddSalePlanIndex.vue'
import AddShipments from './AddShipments.vue'
import ReturnShipments from './ReturnShipments.vue'
import { ElMessage, ElMessageBox } from 'element-plus'
// import { rowContextKey } from 'element-plus';
// import { values } from 'lodash-es';
import type { TabsPaneContext } from 'element-plus'
import router from '@/router'
defineOptions({ name: 'AssetSale' })
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
interface queryParams {
  dataCategory?: string
  pageNo: number // 可选属性
  pageSize: number
}
const initialState = {
  pageNo: 1,
  dataCategory: 'sale',
  pageSize: 10
}
const queryParams = reactive<queryParams>({
  ...initialState
})

const searchRef = ref()
// 表格实例
const gridRef = ref<VxeGridInstance>()
// tab栏方法
// 获取初始键名
const initialKeys = Object.keys(initialState)
// 重置方法：删除所有非初始属性
function resetToInitial() {
  const currentKeys = Object.keys(queryParams)
  currentKeys.forEach((key) => {
    if (!initialKeys.includes(key)) {
      delete queryParams[key]
    } else {
      queryParams[key] = initialState[key]
    }
  })
}
const tabFn = (val: any) => {
  searchRef.value?.reset()
  resetToInitial()
  queryParams.dataCategory = val.label
  if (queryParams.dataCategory === 'sale') {
    gridOptions.columns = [
      { type: 'seq', width: 70 },
      {
        field: 'orderTime',
        title: '订单时间',
        minWidth: '150px',
        formatter: dateFormatterByOptions
      },
      { field: 'customerName', title: '客户名称', minWidth: '80px' },
      { field: 'contactsName', title: '联系人', minWidth: '80px' },
      { field: 'contactsPhone', title: '联系人电话', minWidth: '120px' },
      { field: 'saleType', title: '租售类型', minWidth: '80px', slots: { default: 'type' } },
      {
        field: 'leaseStartTime',
        title: '租赁开始时间',
        minWidth: '150px',
        formatter: dateFormatterByOptions
      },
      { field: 'leaseTime', title: '租赁时长（月）', minWidth: '120px' },
      {
        field: 'leaseEndTime',
        title: '租赁结束时间',
        minWidth: '150px',
        formatter: dateFormatterByOptions
      },
      { field: 'number', title: '数量', minWidth: '50px' },
      { field: 'totalPrice', title: '总价', minWidth: '80px' },
      { field: 'status', title: '状态', minWidth: '80px', slots: { default: 'status' } },
      { title: '操作', width: 200, slots: { default: 'operate' } }
      //   { field: 'accidentCarNumber', title: '事故车辆', minWidth: '100px' },
      //   { field: 'driverName', title: '驾驶员姓名', minWidth: '100px' },
      //   { field: 'driverPhone', title: '联系电话', minWidth: '110px' },
      //   { field: 'status', title: '状态', minWidth: '100px', slots: { default: 'status' } },
      //   { title: '操作', width: 200, slots: { default: 'operate' } }
    ]
  } else {
    gridOptions.columns = [
      { type: 'seq', width: 70 },
      { field: 'name', title: '方案名称', minWidth: '100px' },
      { field: 'assetModel', title: '资产型号', minWidth: '100px' },
      { field: 'safeTemp', title: '适用温度', minWidth: '100px' },
      { field: 'safeHumidity', title: '适用湿度', minWidth: '100px' },
      { field: 'leasePrice', title: '租赁单价', minWidth: '100px' },
      { field: 'salePrice', title: '销售单价', minWidth: '100px' },
      { field: 'minLeaseTime', title: '租赁时长要求', minWidth: '100px' },
      { field: 'status', title: '状态', minWidth: '100px', slots: { default: 'planstatus' } },
      { title: '操作', width: 200, slots: { default: 'planbut' } }
    ]
  }
  getList()
}
// 停用/ 启用
const expenseTypeEnable = (params: object) => {
  tms_web
    .assetSale_enable_post({
      data: {
        ...params
      }
    })
    .then(() => {
      getList()
    })
}
// 更新任务启用状态
const updateStatus = (row: any) => {
  // console.log(row,'row');
  let params = {
    id: row.id,
    status: row.status === 'n' ? 'y' : 'n'
  }
  if (row.status === 'y') {
    ElMessageBox.confirm(
      `您确定要停用吗？停用后不能被后续功能选择，需重新启用后恢复可选择，请慎重考虑！`,
      '停用提醒！',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
      .then(() => {
        expenseTypeEnable(params)
        ElMessage({
          type: 'success',
          message: '停用完成'
        })
      })
      .catch(() => {
        ElMessage({
          type: 'info',
          message: '停用取消'
        })
      })
  } else {
    expenseTypeEnable(params)
  }
}
// 调用vxe grid query方法重载表格
const getList = (item: any = {}) => {
  Object.assign(queryParams, { ...queryParams, ...item })
  if (gridRef.value) {
    gridRef.value.commitProxy('query') // 触发表格重新加载数据
  }
}
/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await tms_web.assetSale_deleteAssetSale_post({ data: { id } })
    message.success(t('common.delSuccess'))
    // 刷新列表
    getList()
  } catch {}
}
// 获取表格数据
const fetchApi = () => {
  return new Promise(async (resolve) => {
    if (queryParams.dataCategory === 'sale') {
      const res = await tms_web.assetSale_querySalesOrders_post({
        data: {
          ...queryParams
        }
      })
      //   console.log(res, '签到记录List')
      resolve({
        page: {
          total: res.data?.total
        },
        result: res.data?.list
      })
    } else {
      const res = await tms_web.assetSale_queryAssetsSalePlan_post({
        data: {
          ...queryParams
        }
      })
      //   console.log(res, '签到历史List')
      // res.data?.list.forEach((element) => {
      //   // element.signStatus = element.signStatus?.name
      // })
      resolve({
        page: {
          total: res.data?.total
        },
        result: res.data?.list
      })
    }
    // const res = await ContractMessageApi.getContractMessagePage(queryParams)

    // gridOptions.data = res.data.list
  })
}

const gridOptions = reactive({
  stripe: true,
  border: true,
  keepSource: true,
  height: '100%',
  columnConfig: {
    resizable: true
  },
  pagerConfig: {
    enabled: true,
    pageSize: 10
  },
  editConfig: {
    trigger: 'click',
    mode: 'row',
    showStatus: true
  },
  toolbarConfig: {
    zoom: true,
    custom: true,
    slots: {
      buttons: 'toolbar_buttons',
      tools: 'tools'
    }
    // tools: [
    //   { name: '新增', code: 'add', status: 'primary' },
    //   { name: '', code: 'search', icon: 'vxe-icon-search', status: 'info' }
    //   // { name: '', code: 'customExport', icon: 'vxe-table-icon-download', status: 'info' }
    // ]
  },
  proxyConfig: {
    props: {
      result: 'result',
      total: 'page.total'
    },
    ajax: {
      // 接收 Promise
      query: ({ page }) => {
        queryParams.pageNo = page.currentPage
        queryParams.pageSize = page.pageSize
        return fetchApi()
      }
      // body 对象： { removeRecords }
    }
  },
  columns: [
    { type: 'seq', width: 70 },
    {
      field: 'orderTime',
      title: '订单时间',
      minWidth: '150px',
      formatter: dateFormatterByOptions
    },
    { field: 'customerName', title: '客户名称', minWidth: '80px' },
    { field: 'contactsName', title: '联系人', minWidth: '80px' },
    { field: 'contactsPhone', title: '联系人电话', minWidth: '120px' },
    { field: 'saleType', title: '租售类型', minWidth: '80px', slots: { default: 'type' } },
    {
      field: 'leaseStartTime',
      title: '租赁开始时间',
      minWidth: '150px',
      formatter: dateFormatterByOptions
    },
    { field: 'leaseTime', title: '租赁时长（月）', minWidth: '120px' },
    {
      field: 'leaseEndTime',
      title: '租赁结束时间',
      minWidth: '150px',
      formatter: dateFormatterByOptions
    },
    { field: 'number', title: '数量', minWidth: '50px' },
    { field: 'totalPrice', title: '总价', minWidth: '80px' },
    { field: 'status', title: '状态', minWidth: '80px', slots: { default: 'status' } },
    { title: '操作', width: 200, slots: { default: 'operate' } }

    //   { field: 'accidentCarNumber', title: '事故车辆', minWidth: '100px' },
    //   { field: 'driverName', title: '驾驶员姓名', minWidth: '100px' },
    //   { field: 'driverPhone', title: '联系电话', minWidth: '110px' },
    //   { field: 'status', title: '状态', minWidth: '100px', slots: { default: 'status' } },
    //   { title: '操作', width: 200, slots: { default: 'operate' } }
  ],
  data: []
})

const gridEvents: VxeGridListeners = {
  toolbarButtonClick(params) {
    console.log(params.code)
  },

  toolbarToolClick({ code }) {
    // console.log(123, code, tool)
    switch (code) {
      case 'search':
        handelSearch()
        break
      case 'add':
        openForm()
        break
      case 'del':
        if (gridRef.value) {
          gridRef.value.commitProxy('delete')
        }
        break
      case 'customExport':
        console.log('自定义导出')
        break
    }
  }
}

const handelSearch = () => {
  searchRef.value?.open()
}

/** 添加/修改操作 */
const imageRef = ref()
const openForm = (id?: number) => {
  // console.log(imageRef.value)
  imageRef.value.open(id)
}
function goPage(row: any) {
  let path = 'assetSale/InfoPage'
  let query: any = { id: row.id }
  //   query.driverName = driverName
  //   query.carNumber = carNumber
  router.push({
    path: path,
    query: {
      ...query
    }
  })
}

const SaleOrder = ref()
const dealFn = (type: string, row?: object) => {
  SaleOrder.value.open(type, row)
}
const SalePlan = ref()
const SalePlandealFn = (type: string, row?: Object) => {
  SalePlan.value.open(type, row)
}
const Shipments = ref()
const ShipmentsdealFn = (row?: Object) => {
  Shipments.value.open(row)
}
const ReturnShipmentsRef = ref()
const ReturnShipmentsRefdealFn = (row?: Object) => {
  ReturnShipmentsRef.value.open(row)
}
</script>
<style scoped lang="scss">
:deep(.el-tabs__header) {
  margin-bottom: 0px;
}
:deep(.el-tabs__nav-wrap:after) {
  background-color: rgba($color: #000000, $alpha: 0);
}
.red{
  color: #e04f48;
  background-color: rgba(224, 79, 72, 0.1);
  border: solid 1px #e04f48;

}
.blue{
  color: #36b336;
  background-color: rgba(54, 179, 54, 0.1);
  border: solid 1px #36b336;
}
</style>
