<template>
    <div class="flex flex-col gap-5">
      <div
        class="flex items-center gap-[10px] rounded-[10px] bg-white p-5 leading-none"
      >
        <div class="text-[18px] font-bold text-black">
          {{ "租售订单" }}
        </div>
        <div
          class="flex cursor-pointer items-center gap-[6px] text-[13px] text-[#A8ABB2]"
        >
          <i class="icon icon-uniE00A"></i>
          <span @click="router.back()">返回</span>
        </div>
      </div>
      <div class="flex flex-col gap-[30px] rounded-[10px] bg-white p-10">
        <InfoBlock name="订单详情">
          <div class="grid grid-cols-2">
            <InfoField name="客户名称" :text="info.customerName"/>
            <InfoField name="联系人" :text="info.contactsName"/>
          </div>
          <div class="grid grid-cols-2">
            <InfoField name="联系人电话" :text="info.contactsPhone"/>
            <InfoField name="租售类型">{{ getDictLabel('sale_type',info.saleType) }}</InfoField>
          </div>
          <div class="grid grid-cols-2">
            <InfoField name="租赁时长">{{ info.leaseTime }}月</InfoField>
            <InfoField name="租赁开始时间">{{ dateFormatterByOptions({cellValue:info.leaseStartTime})  }}</InfoField>
          </div>
          <div class="grid grid-cols-2">
            <InfoField name="租赁结束时间">{{ dateFormatterByOptions({cellValue:info.leaseEndTime}) }}</InfoField>
            <InfoField name="数量">{{ info.number }}套</InfoField>
          </div>
          <div class="grid grid-cols-2">
            <InfoField name="单价">{{ info.price }}元</InfoField>
            <InfoField name="总价">{{ info.totalPrice }}元</InfoField>
          </div>
          <div class="grid grid-cols-1">
            <InfoField name="备注" :text="info.remark"/>
          </div>
        </InfoBlock>
        <InfoBlock name="发货详情" v-if="assertsIds.value">
          <div class="grid grid-cols-2">
            <InfoField name="快递名称" :text="info.fastMailName"/>
            <InfoField name="快递单号" :text="info.fastMailNumber"/>
          </div>
          <div class="grid grid-cols-1">
            <InfoField name="设备列表" :text="assertsIds.value"/>
          </div>
        </InfoBlock>
        <div class="flex justify-center">
          <el-button @click="router.back()">返回</el-button>
        </div>
      </div>
    </div>
  </template>
  <script lang="ts" setup>
  import { onMounted, ref } from "vue";
  import { tms_web } from "@/api/ccms";
//   import { useRoute, useRouter } from "@/util/composition-helpers";
  import InfoBlock from "@/components/infoBlock/InfoBlock.vue";
  import InfoField from "@/components/infoBlock/InfoField.vue";
  import { useRoute } from 'vue-router'
import { dateFormatterByOptions } from '@/utils/formatTime'
import { DICT_TYPE, getDictLabel, getStrDictOptions } from '@/utils/dict'
import router from "@/router";
// useRoute
  const route = useRoute();
//   const router = useRouter();
//   const { getDisName: getSaleType } = useEnums("SaleType");
  console.log(route.query,'route');
  
  const info = ref<Required<GetApiRes<typeof tms_web.assetSale_get_post>>>(
    {} as any
  );
  
  const assertsIds = {} as any;
  
  function getInfo() {
    tms_web
      .assetSale_get_post({
        data: {
          id: route.query.id
        }
      })
      .then((data) => {
        info.value = data.data || {};
        if (info.value.detailList) {
          let temp = info.value.detailList.map((item: any) => item.assetsId).join(",");
          assertsIds.value = temp;
        }
      });
  }
  
  onMounted(() => {
    getInfo();
  });
  
  </script>
  
  <style scoped lang="scss">
  .not-require {
    left: 1000px;
  }
  
  </style>
  