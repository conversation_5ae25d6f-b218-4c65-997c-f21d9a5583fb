<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="快递名称：" prop="fastMailName">
        <el-input
          size="large"
          type="text"
          v-model.trim="formData.fastMailName"
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item label="快递单号：" prop="fastMailNumber">
        <el-input
          size="large"
          type="text"
          v-model.trim="formData.fastMailNumber"
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item label="" prop="stageBilling">
        <StageBilling :detailList="detailList" ref="stageBillingRef" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { tms_web } from '@/api/ccms'
import StageBilling from './StageBilling.vue'

/** 合同信息 表单 */
defineOptions({ name: 'AddShipments' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
// const formType = ref('') // 表单的类型：create - 新增；update - 修改
const updateId = ref()
const formData = ref({
  fastMailName: undefined,
  fastMailNumber: undefined
})
const formRules = reactive({
  fastMailName: [{ required: true, message: '快递名称', trigger: 'blur' }],
  fastMailNumber: [{ required: true, message: '快递单号', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref
interface Object {
  id: any
  fastMailName: any
  fastMailNumber: any
}
// detailList
const detailList = ref([])
/** 打开弹窗 */
const open = async (row?: Object) => {
  dialogVisible.value = true
  // dialogTitle.value = t('action.' + type)
  // formType.value = type
  resetForm()
  // 修改时，设置数据
  if (row?.id) {
    updateId.value = row.id
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗
/** 提交表单 */
const stageBillingRef = ref()
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    let temp = stageBillingRef.value.items.map((item: any) => item.assetsId).join(',')
    const data = formData.value
    await tms_web
      .assetSale_shipments_post({
        data: {
          id: updateId.value,
          assetsIdList: temp,
          ...data
        }
      })
      .then((res) => {
        console.log(res, '发货成功')
        message.success(t('common.updateSuccess'))
      })
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    fastMailName: undefined,
    fastMailNumber: undefined
  }
  formRef.value?.resetFields()
}
</script>
