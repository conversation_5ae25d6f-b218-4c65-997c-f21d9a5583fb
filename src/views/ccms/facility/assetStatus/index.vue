<template>
  <!-- 列表 -->

  <ContentWrap class="full-height">
    <vxe-grid ref="gridRef" v-bind="gridOptions" v-on="gridEvents">
      <template #toolbar_buttons>
        <VxeRadioGroup v-model="queryParams.status" @change="getList">
          <VxeRadioButton label="all" content="全部"/>
          <VxeRadioButton label="online" content="在线"/>
          <VxeRadioButton label="offline" content="离线"/>
        </VxeRadioGroup>
        <!-- <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
          <el-tab-pane label="全部" name="all" />
          <el-tab-pane label="在线" name="online" />
          <el-tab-pane label="离线" name="offline" />
        </el-tabs> -->
      </template>
      <template #type="{ row }">
        <DictTag :type="DICT_TYPE.SALE_TYPE" :value="row.saleType" />
      </template>
      <template #status="{ row }">
        <DictTag :type="DICT_TYPE.ASSETS_ONLINE_STATUS" :value="row.status" />
      </template>
      <template #operate="{ row }">
        <el-button
          type="primary"
          size="small"
          @click="openForm(row.id)"
          v-hasPermi="['ccms:carInsure_get']"
          plain
          >查看报单</el-button
        >
        <!-- <el-button type="danger" size="small" @click="handleDelete(row.id)" v-hasPermi="['wms:contract-message:delete']"
              plain>删除</el-button> -->
      </template>
    </vxe-grid>
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <!-- 搜索抽屉 -->
  <SearchDrawer ref="searchRef" v-model="queryParams" @search="getList" />
</template>

<script setup lang="ts">
import { dateFormatter2ByOptions } from '@/utils/formatTime'
// import download from '@/utils/download'
import DictTag from '@/components/DictTag/src/DictTag.vue'
import { DICT_TYPE } from '@/utils/dict'
import { tms_web } from '@/api/ccms'
import type { VxeGridProps, VxeGridListeners, VxeGridInstance } from 'vxe-table'
import SearchDrawer from './SearchDrawer.vue'
// import { rowContextKey } from 'element-plus';
// import { values } from 'lodash-es';
import type { TabsPaneContext } from 'element-plus'
defineOptions({ name: 'ContractMessage' })
const activeName = ref('all')
const handleClick = (tab: TabsPaneContext, event: Event) => {
  // console.log(tab, event)
  // console.log(tab.paneName)
  queryParams.status = tab.paneName
  getList()
}
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

interface queryParams {
  status?: any
  pageNo: number // 可选属性
  pageSize: number
}
const queryParams = reactive<queryParams>({
  status: 'all',
  pageNo: 1,
  pageSize: 10
})

const searchRef = ref()
// 表格实例
const gridRef = ref<VxeGridInstance>()

// 调用vxe grid query方法重载表格
const getList = (item: any={}) => {
  Object.assign(queryParams, { ...queryParams, ...item })
  if (gridRef.value) {
    gridRef.value.commitProxy('query') // 触发表格重新加载数据
  }
}
// 获取表格数据
const fetchApi = () => {
  return new Promise(async (resolve) => {
    const params = { ...queryParams }

    if (params.status === 'all') {
      delete params.status
    }
    // ContractMessageApi.getContractMessagePage(params)
    // const res = await ContractMessageApi.getContractMessagePage(params)
    const res = await tms_web.assetSale_queryAssetStatus_post({
      data: {
        ...params
      }
    })
    // console.log(res, '资产状态List')

    // gridOptions.data = res.data.list

    resolve({
      page: {
        total: res.data?.total
      },
      result: res.data?.list
    })
  })
}

const gridOptions = reactive({
  stripe: true,
  border: true,
  keepSource: true,
  height: '100%',
  columnConfig: {
    resizable: true
  },
  pagerConfig: {
    enabled: true,
    pageSize: 10
  },
  editConfig: {
    trigger: 'click',
    mode: 'row',
    showStatus: true
  },
  toolbarConfig: {
    zoom: true,
    custom: true,
    slots: {
      buttons: 'toolbar_buttons'
    },
    tools: [
      { name: '', code: 'search', icon: 'vxe-icon-search', status: 'info' },
      // { name: '', code: 'customExport', icon: 'vxe-table-icon-download', status: 'info' }
    ]
  },
  proxyConfig: {
    props: {
      result: 'result',
      total: 'page.total'
    },
    ajax: {
      // 接收 Promise
      query: ({ page }) => {
        queryParams.pageNo = page.currentPage
        queryParams.pageSize = page.pageSize
        return fetchApi()
      }
      // body 对象： { removeRecords }
    }
  },
  columns: [
    { type: 'seq', width: 70 },
    { field: 'assetsId', title: '资产ID', minWidth: '100px' },
    { field: 'customerName', title: '客户名称', minWidth: '100px' },
    { field: 'saleType', title: '租赁类型', minWidth: '100px',slots: { default: 'type' } },
    { field: 'status', title: '状态', minWidth: '100px', slots: { default: 'status' } }
    //   { title: '操作', width: 200, slots: { default: 'operate' } }
  ],
  data: []
})

const gridEvents: VxeGridListeners = {
  toolbarButtonClick(params) {
    console.log(params.code)
  },

  toolbarToolClick({ code }) {
    // console.log(123, code, tool)
    switch (code) {
      case 'search':
        handelSearch()
        break
      case 'add':
        openForm()
        break
      case 'del':
        if (gridRef.value) {
          gridRef.value.commitProxy('delete')
        }
        break
      case 'customExport':
        console.log('自定义导出')
        break
    }
  }
}

const handelSearch = () => {
  searchRef.value?.open()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (id?: number) => {
  // console.log(formRef.value)

  formRef.value.open(id)
}

/** 删除按钮操作 */
// const handleDelete = async (id: number) => {
//   try {
//     // 删除的二次确认
//     await message.delConfirm()
//     // 发起删除
//     await ContractMessageApi.deleteContractMessage(id)
//     message.success(t('common.delSuccess'))
//     // 刷新列表
//     await getList()
//   } catch { }
// }

/** 导出按钮操作 */
// const handleExport = async () => {
//   try {
//     // 导出的二次确认
//     await message.exportConfirm()
//     // 发起导出
//     const data = await ContractMessageApi.exportContractMessage(queryParams)
//     download.excel(data, '合同信息.xls')
//   } catch {
//   } finally {
//   }
// }
</script>
<style scoped lang="scss">
:deep(.el-tabs__header) {
  margin-bottom: 0px;
}
:deep(.el-tabs__nav-wrap:after) {
  background-color: rgba($color: #000000, $alpha: 0);
}
</style>
