<template>
  <el-drawer v-model="drawerVisible" title="搜索" direction="rtl" size="350px">
    <template #header>
      <span class="text-16px font-700">搜索</span>
    </template>
    <div class="search-drawer" style="height: 100%">
      <el-form
        ref="queryFormRef"
        :inline="true"
        :model="queryParams"
        class="-mb-15px"
        label-width="68px"
      >
        <el-form-item label="厂商名称" prop="name">
          <el-input
            v-model="queryParams.name"
            placeholder="请输入厂商名称"
            clearable
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="联系人" prop="contactsName">
          <el-input
            v-model="queryParams.contactsName"
            placeholder="请输入联系人"
            clearable
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="联系人电话" prop="phone">
          <el-input
            v-model="queryParams.phone"
            placeholder="请输入联系人电话"
            clearable
            class="!w-240px"
          />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div style="flex: auto">
        <el-button @click="reset()">重置</el-button>
        <el-button type="primary" @click="search">搜索</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 定义 props 和 emits
const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update:modelValue', 'search', 'reset'])

// 控制抽屉显示
const drawerVisible = ref(false)
interface queryParams {
  name: any
  contactsName: any
  phone: any
}
// 使用 computed 实现双向绑定
const queryParams = ref({
  name: undefined,
  contactsName: undefined,
  phone: undefined
})

// 打开抽屉
function open() {
  drawerVisible.value = true
}

// 关闭抽屉
function close() {
  drawerVisible.value = false
}

// 搜索
function search() {
  close()
  emit('search', queryParams.value)
}

const queryFormRef = ref(null)
// 重置
function reset() {
  queryFormRef.value?.resetFields()
  emit('reset', queryParams.value)
}

// 暴露 open 方法
defineExpose({
  open
})
</script>

<style lang="scss" scoped>
.search-drawer {
  ::v-deep {
    .el-form {
      height: 98%;
    }
  }
}
</style>
