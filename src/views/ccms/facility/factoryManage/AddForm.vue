<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="厂商名称" required prop="name">
        <el-input v-model="formData.name" placeholder="请输入厂商名称" />
      </el-form-item>
      <el-form-item label="联系人" required prop="contactsName">
        <el-input v-model="formData.contactsName" placeholder="请输入联系人" />
      </el-form-item>
      <el-form-item label="联系电话" required prop="phone">
        <el-input v-model="formData.phone" placeholder="请输入联系人电话" />
      </el-form-item>
      <el-form-item label="地址" required prop="address">
        <el-input v-model="formData.address" placeholder="请输入地址" />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" :rows="3" type="textarea" placeholder="请输入" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button
        @click="submitForm"
        type="primary"
        v-hasPermi="[
          'ccms:factoryManage:saveFactoryManage',
          'ccms:factoryManage:editFactoryManage'
        ]"
        :disabled="formLoading"
        >确 定</el-button
      >
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { tms_web } from '@/api/ccms'

/** 合同信息 表单 */
defineOptions({ name: 'ContractMessageForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const updateId = ref(null)
const formData = ref({
  name: undefined,
  contactsName: undefined,
  phone: undefined,
  address: undefined,
  remark: undefined
})
const formRules = reactive({
  name: [{ required: true, message: '厂商名称', trigger: 'blur' }],
  contactsName: [{ required: true, message: '联系人', trigger: 'blur' }],
  phone: [{ required: true, message: '联系电话', trigger: 'blur' }],
  address: [{ required: true, message: '地址', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref
interface Object {
  id: any
  name: any
  contactsName: any
  phone: any
  address: any
  remark: any
}
/** 打开弹窗 */
const open = async (type: string, row?: Object) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (row?.id) {
    updateId.value = row.id
    formData.value.name = row.name
    formData.value.contactsName = row.contactsName
    formData.value.phone = row.phone
    formData.value.address = row.address
    formData.value.remark = row.remark

    // formLoading.value = true
    // try {
    //   formData.value = await ContractMessageApi.getContractMessage(id)
    // } finally {
    //   formLoading.value = false
    // }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗
// id更新/新增
// const  expenseType= async()=> {
//     try {
//       if (id) {
//         await tms_web.factoryManage_editFactoryManage_post({
//           data: {
//             id: id,
//             ...formData
//           }
//         });
//         $notify({
//           title: "成功",
//           message: "修改成功！",
//           type: "success"
//         });
//       } else {
//         await tms_web.factoryManage_saveFactoryManage_post({
//           data: {
//             ...formData
//           }
//         });
//       }
//       $emit("on-submit");
//       $refs.open.close();
//     } catch (e) {
//       console.log(e);
//     }
//   }
/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value
    if (formType.value === 'create') {
      // console.log(data, 'formType');
      await tms_web.factoryManage_saveFactoryManage_post({
        data: {
          ...data
        }
      })
      // console.log(res, '新增成功');

      message.success(t('common.createSuccess'))
    } else {
      await tms_web.factoryManage_editFactoryManage_post({
        data: {
          id: updateId.value,
          ...data
        }
      })
      // console.log(res, '修改成功');

      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    name: undefined,
    contactsName: undefined,
    phone: undefined,
    address: undefined
  }
  formRef.value?.resetFields()
}
</script>
