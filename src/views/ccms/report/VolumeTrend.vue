<template>
  <div>
    <div class="header">
      <el-card class="card" shadow="never">
        <div class="icon"><img src="@/assets/images/icon/h.png" alt=""/></div>
        <div class="num">{{seriesDataCount.goodWeight}}</div>
        <div class="text">货物总重（吨）</div>
      </el-card>
      <el-card class="card" shadow="never">
        <div class="icon"><img src="@/assets/images/icon/j.png" alt=""/></div>
        <div class="num">{{seriesDataCount.goodVolume}}</div>
        <div class="text">货物体积（方）</div>
      </el-card>
      <el-card class="card" shadow="never">
        <div class="icon"><img src="@/assets/images/icon/k.png" alt=""/></div>
        <div class="num">{{seriesDataCount.goodType}}</div>
        <div class="text">货物类型</div>
      </el-card>
      <el-card class="card" shadow="never">
        <div class="icon"><img src="@/assets/images/icon/a.png" alt=""/></div>
        <div class="num">{{seriesDataCount.transportOrderMoney}}</div>
        <div class="text">运单金额（万元）</div>
      </el-card>
    </div>

    <div class="threeOne-box">
      <el-card class="card" shadow="never">
        <div class="max-tit">货物类型占比</div>
        <div class="chart">
          <PieChart idRef="Ethiwbywbution" :pieData="pieDataOne" v-if="pieDataOne.seriesData.length > 0" />
        </div>
      </el-card>
      <!-- <el-card class="card" shadow="never">
        <div class="max-tit">货物品类占比Top5</div>
        <div class="chart">
          <PieChart idRef="Ethibqwqwon" :pieData="pieDataTwo" v-if="pieDataTwo.seriesData.length > 0" />
        </div>
      </el-card> -->
      <el-card class="card" shadow="never">
        <div class="dis-tit">
          <div class="max-tit">货损类别占比</div>
        </div>
        <div class="chart">
          <PieChart idRef="Etqwxqswtion" :pieData="pieDataThree" v-if="pieDataThree.seriesData.length > 0" />
        </div>
      </el-card>
    </div>

    <div class="three-box">
      <el-card class="card" shadow="never">
        <div class="dis-tit">
          <div class="max-tit">货量趋势（吨）</div>
          <div class="tit-right">
            <el-radio-group @change="changeDDType" v-model="radioAccept">
              <el-radio-button value='day'>今日</el-radio-button>
              <el-radio-button value='weekend'>本周</el-radio-button>
              <el-radio-button value='month'>本月</el-radio-button>
              <el-radio-button value='year'>全年</el-radio-button>
            </el-radio-group>
          </div>
        </div>
        <div class="chart">
          <DataBraChart :braData='DDserverDataList' idRef="Etaswnqiodaon" v-if="DDserverDataList?.yData"  />
        </div>
      </el-card>
      <el-card class="card" shadow="never">
        <div class="max-tit">客户发货量排名（吨）</div>
        <!-- <div class="list">
          <div class="item" v-for="(item, index) in countCustListDataONE" :key="index">
            <div class="text">
              <span v-if="index === 0"><img src="@/assets/images/icon/icon-a.png" alt=""/></span>
              <span v-else-if="index === 1"><img src="@/assets/images/icon/icon-b.png" alt=""/></span>
              <span v-else-if="index === 2"><img src="@/assets/images/icon/icon-c.png" alt=""/></span>
              <span v-else>{{index+1}}</span>
              {{item.name}}
            </div>
            <div class="num">{{item.value}}</div>
          </div>
        </div> -->
        <div class="list">
          <CustomVueSeamlessScroll :listData="countCustListDataONE" :height='200' />
        </div>
      </el-card>
      <el-card class="card" shadow="never">
        <div class="dis-tit">
          <div class="max-tit">客户收货量排名（吨）</div>
        </div>
        <!-- <div class="list">
          <div class="item" v-for="(item, index) in countCustListDataTWO" :key="index">
            <div class="text">
              <span v-if="index === 0"><img src="@/assets/images/icon/icon-a.png" alt=""/></span>
              <span v-else-if="index === 1"><img src="@/assets/images/icon/icon-b.png" alt=""/></span>
              <span v-else-if="index === 2"><img src="@/assets/images/icon/icon-c.png" alt=""/></span>
              <span v-else>{{index+1}}</span>
              {{item.name}}
            </div>
            <div class="num">{{item.value}}</div>
          </div>
        </div> -->
        <div class="list">
          <CustomVueSeamlessScroll :listData="countCustListDataTWO" :height='200' />
        </div>
      </el-card>
    </div>

  </div>
</template>
<script>
import PieChart from "@/components/PieChart.vue";
import DataBraChart from "@/components/DataBraChart.vue";
import { tms_web } from "@/api/ccms";
import CustomVueSeamlessScroll from "@/components/CustomVueSeamlessScroll.vue";

export default  {
  components: {
    PieChart,
    DataBraChart,
    CustomVueSeamlessScroll
  },
  data(){
    return {
      seriesDataCount: {},
      pieDataOne: {
        seriesData: [],
        color: ['#2D57CC ', '#36B336', '#D68711','#E04F48', '#A23AF3']
      },
      pieDataTwo: {
        seriesData: [],
        color: ['#2D57CC ', '#36B336', '#D68711','#E04F48', '#A23AF3']
      },
      pieDataThree: {
        seriesData: [],
        color: ['#2D57CC ', '#36B336', '#D68711','#E04F48', '#A23AF3']
      },
      countCustListDataONE: [],
      countCustListDataTWO: [],
      radioAccept: "day",
      DDserverDataList: {},

      radio: "1",
      pieData: {
        seriesData: [
          { value: 80, name: '接单率' },
          { value: 50, name: '未接单率' }
        ],
        color: ['#3d64cf', '#ff9900']
      },
      braData: {
        xData: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
        yData: [20, 30, 40, 50, 60, 70, 80, 90, 100, 110, 120, 130],
        braColor: '#000', // 线条颜色
        lineAreaColor: 'rgba(0, 178, 255, 1)',
      }
    }
  },
  methods:{
    // 统计数据
    getTopCountHomeTotal() {
      tms_web.countGood_countGoodCountVo_post({
        data: {},
      }).then((res) => {
        this.seriesDataCount = res.data
      })
    },
    // 货物类型占比
    getOneTotal() {
      tms_web.countGood_countGoodTypeNameRatio_post({
        data: {},
      }).then((res) => {
        this.pieDataOne.seriesData = res.data
      })
    },
    // 货物品类占比Top5
    getTwoTotal() {
      tms_web.countGood_countGoodTypeNameRatio_post({
        data: {},
      }).then((res) => {
        this.pieDataTwo.seriesData = res.data
      })
    },
    // 货损类别占比
    getThreeTotal() {
      tms_web.countGood_countGoodFaultLevelRatio_post({
        data: {},
      }).then((res) => {
        this.pieDataThree.seriesData = res.data
      })
    },

    // 客户发货量排名（吨）
    getDataOneList() {
      tms_web.countGood_countCustomerGoodWeightRank_post({
        data: {},
      }).then((res) => {
        this.countCustListDataONE = res.data
      })
    },
    // 客户收货量排名（吨）
    getDataTwoList() {
      tms_web.countGood_countCustomerAcceptWeightRank_post({
        data: {},
      }).then((res) => {
        this.countCustListDataTWO = res.data
      })
    },

    // 货量趋势（吨）
    getDriverActivity() {
      tms_web.countGood_countGoodWeightByTime_post({
        data: {
          timeType: this.radioAccept,
        },
      }).then((res) => {
        console.log(res)
        let data = res.data || {};
        let Xdata = []
        let Ydata = []
        data.forEach((item) => {
          Xdata.push(item.name)
          Ydata.push(item.value)
        })
        this.DDserverDataList = {
          xData: Xdata,
          yData: Ydata,
          lineColor: '#2D57CC', // 线条颜色
          lineAreaColor: 'rgba(45, 87, 204, 0.8)',  // 线条区域颜色
        }
      })
    },
    // 订单统计change
    changeDDType(value) {
      this.DDserverDataList = []
      this.radioAccept = value;
      this.getDriverActivity();
    }
  },
  mounted () {
    this.getTopCountHomeTotal()
    this.getOneTotal()
    this.getTwoTotal()
    this.getThreeTotal()

    this.getDataOneList()
    this.getDataTwoList()

    this.getDriverActivity()
  }
}
</script>
<style scoped lang="scss">
.max-tit {
  font-size: 18px;
  color: #000;
  font-weight: bold;
}
.header {
  width: 100%;
  justify-content: space-between;
  display: flex;
  margin-bottom: 20px;
  .card {
    width: calc((100% - 60px) / 4);
    height: 120px;
    border-radius: 15px;
    cursor: pointer;
    text-align: center;
    margin: 0;
    padding:0;
    .icon {
      img {
        width: 24px;
        height: 24px;
        margin: 0 auto;
      }
      i {
        font-size: 20px;
        color: #2d57cc;
      }
    }
    .num {
      font-weight: bold;
      font-size: 24px;
      color: #2D57CC;
      margin: 5px 0;
    }
    .text {
      font-size: 14px;
    }
  }
}

.threeOne-box {
  width: 100%;
  justify-content: space-between;
  display: flex;
  margin-bottom: 20px;
  .card {
    width: calc((100% - 20px) / 2);
    height: 300px;
    border-radius: 15px;
    margin: 0;
    padding:0;
    // cursor: pointer;
    .max-tit {
      font-size: 18px;
      color: #000;
      font-weight: bold;
    }
    .chart {
      height: 230px;
      // background: #ccc;
      margin: 0px 0 10px;
    }
    .mess-b {
      display: flex;
      justify-content: space-around;
      color: #666;
    }
    .dis-tit {
      display: flex;
      justify-content: space-between;
    }
  }
}

.three-box {
  width: 100%;
  justify-content: space-between;
  display: flex;
  margin-bottom: 20px;
  .card {
    width: calc((100% - 40px) / 3);
    height: 300px;
    border-radius: 15px;
    margin: 0;
    padding:0;
    // cursor: pointer;
    .max-tit {
      font-size: 18px;
      color: #000;
      font-weight: bold;
    }
    .chart {
      height: 230px;
      // background: #ccc;
      margin: 0px 0 10px;
    }
    .mess-b {
      display: flex;
      justify-content: space-around;
      color: #666;
    }
    .dis-tit {
      display: flex;
      justify-content: space-between;
    }
  }
}
.list {
  margin-top: 15px;
  // background: #eee;
  .item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    .text {
      width: calc(100% - 50px);
      // background: #ccc;
      // 单行省略号
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      span {
        margin-right: 7px;
        width: 24px;
        height: 24px;
        display: inline-block;
        img {
          width: 24px;
          height: 24px;
          display: inline-block;
          vertical-align: middle;
        }
      }
    }
    .num {
      color: #2d57cc;
      font-size: 15px;
      font-weight: 600;
    }
  }
}
</style>
