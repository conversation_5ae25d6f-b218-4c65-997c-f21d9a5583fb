<template>
  <div>
    <div class="header">
      <el-card class="card" shadow="never">
        <div class="icon"><img src="@/assets/images/icon/g.png" alt=""/></div>
        <div class="num">{{seriesDataCount.driverNum}}</div>
        <div class="text">驾驶员数量</div>
      </el-card>
      <el-card class="card" shadow="never">
        <div class="icon"><img src="@/assets/images/icon/b.png" alt=""/></div>
        <div class="num">{{seriesDataCount.transportNum}}</div>
        <div class="text">运单数</div>
      </el-card>
      <el-card class="card" shadow="never">
        <div class="icon"><img src="@/assets/images/icon/d.png" alt=""/></div>
        <div class="num">{{seriesDataCount.registerCarNum}}</div>
        <div class="text">注册车辆数</div>
      </el-card>
      <el-card class="card" shadow="never">
        <div class="icon"><img src="@/assets/images/icon/i.png" alt=""/></div>
        <div class="num">{{seriesDataCount.transportCarNum}}</div>
        <div class="text">运单发车数</div>
      </el-card>
    </div>

    <div class="four-box">
      <el-card class="card" shadow="never">
        <div class="max-tit">接单率分析</div>
        <div class="chart">
          <DataPieChart
            idRef="EthasddnicDis"
            :seriesData="seriesDataCountA"
            v-if="seriesDataCountA.valNum"
          />
        </div>
      </el-card>
      <el-card class="card" shadow="never">
        <div class="max-tit">正常完单率分析</div>
        <div class="chart">
          <DataPieChart
            idRef="EthasqweDis"
            :seriesData="seriesDataCountB"
          />
        </div>
      </el-card>
      <el-card class="card" shadow="never">
        <div class="dis-tit">
          <div class="max-tit">运力类型占比</div>
        </div>
        <div class="chart">
          <PieChart idRef="Etasxqtion" :pieData="pieDataYltype" v-if="pieDataYltype.seriesData.length > 0" />
        </div>
      </el-card>
      <el-card class="card" shadow="never">
        <div class="dis-tit">
          <div class="max-tit">车辆类型占比</div>
        </div>
        <div class="chart">
          <PieChart idRef="Etasqwon" :pieData="pieDataCLtype" v-if="pieDataCLtype.seriesData.length > 0" />
        </div>
      </el-card>
    </div>
    <div class="five-box">
      <el-card class="card" shadow="never">
        <div class="max-tit">运单量趋势</div>
        <div class="chart">
          <DataBraChart :braData='braData'  idRef="Etasdsbasjkaon"  v-if="braData.xData?.length > 0" />
        </div>
      </el-card>
      <el-card class="cardTwo" shadow="never">
        <div class="max-tit">驾驶员接单量排名</div>
        <!-- <div class="list">
          <div class="item" v-for="(item, index) in countCustListData" :key="index">
            <div class="text">
              <span v-if="index === 0"><img src="@/assets/images/icon/icon-a.png" alt=""/></span>
              <span v-else-if="index === 1"><img src="@/assets/images/icon/icon-b.png" alt=""/></span>
              <span v-else-if="index === 2"><img src="@/assets/images/icon/icon-c.png" alt=""/></span>
              <span v-else>{{index+1}}</span>
              {{item.name}}
            </div>
            <div class="num">{{item.value}}</div>
          </div>
        </div> -->
        <div class="list">
          <CustomVueSeamlessScroll :listData="countCustListData" :height='200' />
        </div>
      </el-card>
    </div>
  </div>
</template>
<script>
import DataPieChart from "@/components/DataPieChart.vue";
import PieChart from "@/components/PieChart.vue";
import DataBraChart from "@/components/DataBraChart.vue";
import { tms_web } from "@/api/ccms";
import CustomVueSeamlessScroll from "@/components/CustomVueSeamlessScroll.vue";
export default  {
  components: {
    DataPieChart,
    PieChart,
    DataBraChart,
    CustomVueSeamlessScroll
  },
  data(){
    return {
      seriesDataCount: {},
      seriesDataCountA: {},
      seriesDataCountB: {},

      pieDataYltype: {
        seriesData: [],
        color: ['#2D57CC ', '#36B336', '#D68711','#E04F48']
      },
      pieDataCLtype: {
        seriesData: [],
        color: ['#2D57CC ', '#36B336', '#D68711','#E04F48']
      },
      countCustListData: [],
      radio: "1",
      seriesData: {
        bgColor: '#d7f0d7',
        zbColor: '#36b336',
        totalNum: 100,
        valNum: 50,
        valText: '接单率',
        unin: '50%'
      },
      lineData: {
        xData: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
        yData: [20, 30, 40, 50, 60, 70, 80, 90, 100, 110, 120, 130],
        lineColor: '#3d64cf', // 线条颜色
        lineAreaColor: 'rgba(0, 178, 255, 1)',  // 线条区域颜色
      },
      pieData: {
        seriesData: [
          { value: 80, name: '接单率' },
          { value: 50, name: '未接单率' }
        ],
        color: ['#3d64cf', '#ff9900']
      },
      braData: {}
    }
  },
  methods:{
    // 统计数据
    getTopCountHomeTotal() {
      tms_web.countDriver_countDriverCountVo_post({
        data: {},
      }).then((res) => {
        console.log(res, '统计数据');
        this.seriesDataCount = res.data
        this.seriesDataCountA ={
          bgColor: '#2D57CC',
          zbColor: '#d5ddf5',
          valNum: parseFloat((res.data.acceptOrderRatio*100).toFixed(2)),
          valText: '接单率',
          unin: parseFloat((res.data.acceptOrderRatio*100).toFixed(2)) + '%',
        }
        this.seriesDataCountB ={
          bgColor: '#36B336',
          zbColor: '#d7f0d7',
          valNum:parseFloat((res.data.finishOrderRatio*100).toFixed(2)),
          valText: '完单率',
          unin: parseFloat((res.data.finishOrderRatio*100).toFixed(2)) + '%',
        }
      })
    },
    // 运力类型占比
    getKHtypeTotal() {
      tms_web.countDriver_countDriverTypeRatio_post({
        data: {},
      }).then((res) => {
        this.pieDataYltype.seriesData = res.data
      })
    },
    // 车辆类型占比
    getKCHELl() {
      tms_web.countDriver_countCarTypeRatio_post({
        data: {},
      }).then((res) => {
        this.pieDataCLtype.seriesData = res.data
      })
    },
    // 运单量趋势
    getDdqsData() {
      tms_web.countDriver_countTransportMonthOrder_post({
        data: {},
      }).then((res) => {
        let data = res.data
        let xData = []
        let yData = []
        data.forEach((item) => {
          xData.push(item.name)
          yData.push(item.value)
        })
        this.braData = {
          xData: xData,
          yData: yData,
          braColor: '#000', // 线条颜色
          lineAreaColor: 'rgba(45, 87, 204, 1)',
        }
      })
    },
    // 驾驶员接单量排名- list
    getDataQXList() {
      tms_web.countDriver_countDriverOrder_post({
        data: {},
      }).then((res) => {
        this.countCustListData = res.data
      })
    }
  },
  mounted () {
    this.getTopCountHomeTotal()
    this.getKHtypeTotal()
    this.getKCHELl()
    this.getDdqsData()
    this.getDataQXList()
  }
}
</script>
<style scoped lang="scss">
.max-tit {
  font-size: 18px;
  color: #000;
  font-weight: bold;
}
.header {
  width: 100%;
  justify-content: space-between;
  display: flex;
  margin-bottom: 20px;
  .card {
    width: calc((100% - 60px) / 4);
    height: 125px;
    border-radius: 15px;
    cursor: pointer;
    text-align: center;
    margin: 0;
    padding:0;
    .icon {
        img {
          width: 24px;
          height: 24px;
          margin: 0 auto;
        }
        i {
          font-size: 20px;
          color: #2d57cc;
        }
      }
      .num {
        font-weight: bold;
        font-size: 24px;
        color: #2D57CC;
        margin: 5px 0;
      }
    .text {
      font-size: 14px;
    }
  }
}

.four-box {
  width: 100%;
  justify-content: space-between;
  display: flex;
  margin-bottom: 20px;
  .card {
    width: calc((100% - 60px) / 4);
    height: 280px;
    border-radius: 15px;
    cursor: pointer;
    margin: 0;
    padding:0;
    // background: #eee;
    .max-tit {
      font-size: 18px;
      color: #000;
      font-weight: bold;
    }
    .chart {
      height: 200px;
      // background: #ccc;
      margin: 10px 0 10px;
    }
    .mess-b {
      display: flex;
      justify-content: space-around;
      color: #666;
    }
    .dis-tit {
      display: flex;
      justify-content: space-between;
    }
  }
}
.five-box {
  width: 100%;
  justify-content: space-between;
  display: flex;
  .card {
    width: calc((65%) / 1);
    height: 300px;
    border-radius: 15px;
    cursor: pointer;
    margin: 0;
    padding:0;
    // text-align: center;
    .chart {
      height: 240px;
      // background: #ccc;
    }
  }
  .cardTwo {
    width: calc((35% - 20px) / 1);
    height: 300px;
    border-radius: 15px;
    margin: 0;
    padding:0;
     .list {
      margin-top: 15px;
      .item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        .text {
          width: calc(100% - 50px);
          // 单行省略号
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          span {
            margin-right: 7px;
            img {
              width: 24px;
              height: 24px;
              display: inline-block;
              vertical-align: middle;
            }
          }
        }
        .num {
          color: #2d57cc;
          font-size: 15px;
          font-weight: 600;
        }
      }
    }
  }
}
</style>
