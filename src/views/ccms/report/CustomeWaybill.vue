<script>
import <PERSON><PERSON><PERSON><PERSON>hart from '@/components/DataPieChart.vue'
import <PERSON><PERSON>hart from '@/components/PieChart.vue'
import DataBraChart from '@/components/DataBraChart.vue'
import { tms_web } from '@/api/ccms'
import CustomVueSeamlessScroll from '@/components/CustomVueSeamlessScroll.vue'

export default {
  components: {
    DataPieChart,
    Pie<PERSON><PERSON>,
    DataBraChart,
    CustomVueSeamlessScroll
  },
  data() {
    return {
      seriesDataCount: {},
      seriesDataCountA: {},
      seriesDataCountB: {},

      countType: '', // 接单率分析

      pieDataKHType: {
        seriesData: [],
        color: ['#2D57CC ', '#36B336']
      },
      pieDataYSYQ: {
        seriesData: [],
        color: ['#2D57CC ', '#36B336']
      },
      pieDataYSType: {
        seriesData: [],
        color: ['#2D57CC ', '#36B336']
      },
      pieDataHWType: {
        seriesData: [],
        color: ['#2D57CC ', '#36B336', '#D68711', '#E04F48']
      },
      countCustListData: [],

      radio: '1',
      seriesData: {
        bgColor: '#d7f0d7',
        zbColor: '#36b336',
        totalNum: 100,
        valNum: 50,
        valText: '接单率',
        unin: '50%'
      },
      lineData: {
        xData: [
          '1月',
          '2月',
          '3月',
          '4月',
          '5月',
          '6月',
          '7月',
          '8月',
          '9月',
          '10月',
          '11月',
          '12月'
        ],
        yData: [20, 30, 40, 50, 60, 70, 80, 90, 100, 110, 120, 130],
        lineColor: '#3d64cf', // 线条颜色
        lineAreaColor: 'rgba(0, 178, 255, 1)' // 线条区域颜色
      },
      pieData: {
        seriesData: [
          { value: 80, name: '接单率' },
          { value: 50, name: '未接单率' }
        ],
        color: ['#3d64cf', '#ff9900']
      },
      braData: {}
    }
  },
  methods: {
    // 统计数据
    getTopCountHomeTotal() {
      tms_web
        .countCustomer_countCustomerCountVo_post({
          data: {
            type: this.countType
          }
        })
        .then((res) => {
          this.seriesDataCount = res.data
          this.seriesDataCountA = {
            bgColor: '#2D57CC',
            zbColor: '#d5ddf5',
            valNum: res.data.acceptOrderRatio * 100,
            valText: '接单率',
            unin: res.data.acceptOrderRatio * 100 + '%'
          }
          this.seriesDataCountB = {
            bgColor: '#36B336',
            zbColor: '#d7f0d7',
            valNum: res.data.finishOrderRatio * 100,
            valText: '完单率',
            unin: res.data.finishOrderRatio * 100 + '%'
          }

          console.log(this.seriesDataCountA.valNum, 'valNumvalNumvalNum')
          console.log(this.seriesDataCountB.valNum, 'seriesDataCountB')
        })
    },
    // 客户类型占比
    getKHtypeTotal() {
      tms_web
        .countCustomer_countCustomerRatio_post({
          data: {
            type: this.countType
          }
        })
        .then((res) => {
          this.pieDataKHType.seriesData = res.data
        })
    },
    // 导航页统计-统计数据
    changeCountType(val) {
      this.countType = val
      // this.getCountHomeTotal();
      this.getTopCountHomeTotal()
      // this.getKHtypeTotal()
      this.getYSYQTotal()
      this.getYSYQTypeData()
      this.getHWTypeData()
      this.getDdqsData()
      this.getDataQXList()
    },

    // 运输要求占比
    getYSYQTotal() {
      tms_web
        .countCustomer_countTransportRequireRatio_post({
          data: {
            type: this.countType
          }
        })
        .then((res) => {
          this.pieDataYSYQ.seriesData = res.data
        })
    },
    // 运输类型
    getYSYQTypeData() {
      tms_web
        .countCustomer_countTransportTypeRatio_post({
          data: {
            type: this.countType
          }
        })
        .then((res) => {
          this.pieDataYSType.seriesData = res.data
        })
    },
    // 货物类型占比
    getHWTypeData() {
      tms_web
        .countCustomer_countGoodTypeNameRatio_post({
          data: {
            type: this.countType
          }
        })
        .then((res) => {
          this.pieDataHWType.seriesData = res.data
        })
    },
    // 订单趋势
    getDdqsData() {
      tms_web
        .countCustomer_countMonthOrder_post({
          data: {
            type: this.countType
          }
        })
        .then((res) => {
          let data = res.data
          let xData = []
          let yData = []
          data.forEach((item) => {
            xData.push(item.name)
            yData.push(item.value)
          })
          this.braData = {
            xData: xData,
            yData: yData,
            braColor: '#000', // 线条颜色
            lineAreaColor: 'rgba(45, 87, 204, 1)'
          }
        })
    },
    // 订单趋势- list
    getDataQXList() {
      tms_web
        .countCustomer_countCustomerOrder_post({
          data: {
            type: this.countType
          }
        })
        .then((res) => {
          this.countCustListData = res.data
        })
    }
  },
  mounted() {
    this.getTopCountHomeTotal()
    this.getKHtypeTotal()
    this.getYSYQTotal()
    this.getYSYQTypeData()
    this.getHWTypeData()
    this.getDdqsData()
    this.getDataQXList()
  }
}
</script>

<template>
  <div>
    <div class="header">
      <el-card class="card" shadow="never">
        <div class="icon"><img src="@/assets/images/icon/g.png" alt="" /></div>
        <div class="num">{{ seriesDataCount.customerNum||0 }}</div>
        <div class="text">客户数量</div>
      </el-card>
      <el-card class="card" shadow="never">
        <div class="icon"><img src="@/assets/images/icon/b.png" alt="" /></div>
        <div class="num">{{ seriesDataCount.orderNum||0 }}</div>
        <div class="text">订单数量</div>
      </el-card>
      <el-card class="card" shadow="never">
        <div class="icon"><img src="@/assets/images/icon/d.png" alt="" /></div>
        <div class="num">{{ seriesDataCount.transportNum||0 }}</div>
        <div class="text">运单数</div>
      </el-card>
      <el-card class="card" shadow="never">
        <div class="icon"><img src="@/assets/images/icon/h.png" alt="" /></div>
        <div class="num">{{ seriesDataCount.goodWeight||0 }}</div>
        <div class="text">货物总重（吨）</div>
      </el-card>
      <el-card class="card" shadow="never">
        <div class="icon"><img src="@/assets/images/icon/a.png" alt="" /></div>
        <div class="num">{{ seriesDataCount.transportOrderMoney || 0 }}</div>
        <div class="text">运单金额（万元）</div>
      </el-card>
    </div>

    <div class="chart-tab">
      <el-radio-group @change="changeCountType" v-model="countType">
        <el-radio-button value="" label="">所有</el-radio-button>
        <el-radio-button value="company" label="company">企业客户</el-radio-button>
        <el-radio-button value="personal" label="personal">个人客户</el-radio-button>
      </el-radio-group>
    </div>
    <div class="Two-box">
      <el-card class="card" shadow="never">
        <div class="dis-tit">
          <div class="max-tit">接单率分析</div>
          <div class="tit-right"></div>
        </div>
        <div class="chart-two">
          <DataPieChart
            idRef="EthasdasadnicDis"
            :seriesData="seriesDataCountA"
            v-if="seriesDataCountA.valNum || seriesDataCountA.valNum == 0"
          />
        </div>
      </el-card>
      <el-card class="card" shadow="never">
        <div class="dis-tit">
          <div class="max-tit">正常完单率分析</div>
          <div class="tit-right"></div>
        </div>
        <div class="chart-two">
          <DataPieChart
            idRef="EthaasdwesqweDis"
            :seriesData="seriesDataCountB"
            v-if="seriesDataCountB.valNum || seriesDataCountB.valNum == 0"
          />
        </div>
      </el-card>
    </div>

    <div class="four-box">
      <el-card class="card" shadow="never">
        <div class="max-tit">客户类型占比</div>
        <div class="chart">
          <PieChart
            idRef="Ethibuasxnqwution"
            :pieData="pieDataKHType"
            v-if="pieDataKHType.seriesData.length > 0"
          />
        </div>
      </el-card>
      <el-card class="card" shadow="never">
        <div class="max-tit">运输要求占比</div>
        <div class="chart">
          <PieChart
            idRef="Etasdasdbution"
            :pieData="pieDataYSYQ"
            v-if="pieDataYSYQ.seriesData.length > 0"
          />
        </div>
      </el-card>
      <el-card class="card" shadow="never">
        <div class="dis-tit">
          <div class="max-tit">运输类型</div>
        </div>
        <div class="chart">
          <PieChart
            idRef="Eth1ferbution"
            :pieData="pieDataYSType"
            v-if="pieDataYSType.seriesData.length > 0"
          />
        </div>
      </el-card>
      <el-card class="card" shadow="never">
        <div class="dis-tit">
          <div class="max-tit">货物类型占比</div>
        </div>
        <div class="chart">
          <PieChart
            idRef="Eqwethibution"
            :pieData="pieDataHWType"
            v-if="pieDataHWType.seriesData.length > 0"
          />
        </div>
      </el-card>
    </div>

    <div class="five-box">
      <el-card class="card" shadow="never">
        <div class="max-tit">订单趋势</div>
        <div class="chart">
          <DataBraChart v-if="braData.xData?.length > 0" :braData="braData" idRef="Etasdaonasjl" />
        </div>
      </el-card>
      <el-card class="cardTwo" shadow="never">
        <div class="max-tit">客户下单量排名</div>
        <!-- <div class="list">
          <div class="item" v-for="(item, index) in countCustListData" :key="index">
            <div class="text">
              <span v-if="index === 0"><img src="@/assets/images/icon/icon-a.png" alt=""/></span>
              <span v-else-if="index === 1"><img src="@/assets/images/icon/icon-b.png" alt=""/></span>
              <span v-else-if="index === 2"><img src="@/assets/images/icon/icon-c.png" alt=""/></span>
              <span v-else>{{index+1}}</span>
              {{item.name}}
            </div>
            <div class="num">{{item.value}}</div>
          </div>
        </div> -->
        <div class="list">
          <CustomVueSeamlessScroll :listData="countCustListData" :height="200" />
        </div>
      </el-card>
    </div>
  </div>
</template>

<style scoped lang="scss">
.max-tit {
  font-size: 18px;
  color: #000;
  font-weight: bold;
}

.header {
  width: 100%;
  justify-content: space-between;
  display: flex;
  margin-bottom: 20px;

  .card {
    width: calc((100% - 80px) / 5);
    height: 120px;
    border-radius: 15px;
    cursor: pointer;
    text-align: center;
    margin: 0;
    padding:0;

    .icon {
      img {
        width: 24px;
        height: 24px;
        margin: 0 auto;
      }

      i {
        font-size: 20px;
        color: #2d57cc;
      }
    }

    .num {
      font-weight: bold;
      font-size: 24px;
      color: #2d57cc;
      margin: 5px 0;
    }

    .text {
      font-size: 14px;
    }
  }
}

.Two-box {
  width: 100%;
  justify-content: space-between;
  display: flex;
  margin-bottom: 20px;

  .card {
    width: calc((100% - 20px) / 2);
    height: 270px;
    border-radius: 15px;
    cursor: pointer;
    margin: 0;
    padding:0;

    .dis-tit {
      display: flex;
      justify-content: space-between;
    }

    .tit-right {
      display: flex;
      align-items: center;

      .mess {
        font-size: 14px;
        margin-right: 20px;
      }
    }

    .chart-two {
      width: 100%;
      height: 200px;
      // background: #ccc;
    }
  }
}

.four-box {
  width: 100%;
  justify-content: space-between;
  display: flex;
  margin-bottom: 20px;

  .card {
    width: calc((100% - 60px) / 4);
    height: 220px;
    border-radius: 15px;
    cursor: pointer;
    margin: 0;
    padding:0;

    .max-tit {
      font-size: 18px;
      color: #000;
      font-weight: bold;
    }

    .chart {
      width: 100%;
      height: 150px;
      // background: #ccc;
      margin: 0px 0 10px;
    }

    .mess-b {
      display: flex;
      justify-content: space-around;
      color: #666;
    }

    .dis-tit {
      display: flex;
      justify-content: space-between;
    }
  }
}

.five-box {
  width: 100%;
  justify-content: space-between;
  display: flex;
  // margin-bottom: 20px;
  .card {
    width: calc((65%) / 1);
    height: 300px;
    border-radius: 15px;
    cursor: pointer;
    margin: 0;
    padding:0;
    // text-align: center;
    .chart {
      height: 240px;
      // background: #ccc;
    }
  }

  .cardTwo {
    width: calc((35% - 20px) / 1);
    height: 300px;
    border-radius: 15px;
    margin: 0;
    padding:0;
    // cursor: pointer;
    // text-align: center;
    .list {
      margin-top: 15px;

      .item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;

        .text {
          width: calc(100% - 50px);
          // background: #ccc;
          // 单行省略号
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;

          span {
            margin-right: 7px;
            width: 24px;
            height: 24px;
            display: inline-block;

            img {
              width: 24px;
              height: 24px;
              display: inline-block;
              vertical-align: middle;
            }
          }
        }

        .num {
          color: #2d57cc;
          font-size: 15px;
          font-weight: 600;
        }
      }
    }
  }
}

.chart-tab {
  // background: #fff;
  // padding: 20px;
  margin-bottom: 20px;
}
</style>
