<template>
  <div>
    <div class="header">
      <el-card class="card" shadow="never">
        <div class="icon"><img src="@/assets/images/icon/t.png" alt="" /></div>
        <div class="num">{{ countHomeTotal.coldConfigNum }}</div>
        <div class="text">冷链设施总数</div>
      </el-card>
      <el-card class="card" shadow="never">
        <div class="icon"><img src="@/assets/images/icon/r.png" alt="" /></div>
        <div class="num">{{ countHomeTotal.coldMonitorNum }}</div>
        <div class="text">冷链监控次数</div>
      </el-card>
      <el-card class="card" shadow="never">
        <div class="icon"><img src="@/assets/images/icon/cr.png" alt="" /></div>
        <div style="color: #d68711" class="num">{{ countHomeTotal.errorNum }}</div>
        <div class="text">异常信息数</div>
      </el-card>
      <el-card class="card" shadow="never">
        <div class="icon"><img src="@/assets/images/icon/dr.png" alt="" /></div>
        <div style="color: #e04f48" class="num">{{ countHomeTotal.warnNum }}</div>
        <div class="text">告警信息数</div>
      </el-card>
    </div>

    <div class="three-box">
      <el-card class="card" shadow="never">
        <div class="dis-tit">
          <div class="max-tit">客户监控次数排名 Top 10</div>
          <div class="tit-right"></div>
        </div>
        <!-- <div class="list">
          <div class="item" v-for="(item, index) in countCustListData" :key="index">
            <div class="text">
              <span v-if="index === 0"><img src="@/assets/images/icon/icon-a.png" alt=""/></span>
              <span v-else-if="index === 1"><img src="@/assets/images/icon/icon-b.png" alt=""/></span>
              <span v-else-if="index === 2"><img src="@/assets/images/icon/icon-c.png" alt=""/></span>
              <span v-else>{{index+1}}</span>
              {{item.name}}
            </div>
            <div class="num">{{item.value}}</div>
          </div>
        </div> -->
        <div class="list">
          <CustomVueSeamlessScroll :listData="countCustListData" />
        </div>
      </el-card>
      <el-card class="card" shadow="never">
        <div class="dis-tit">
          <div class="max-tit">冷链监控量</div>
          <div class="tit-right">
            <el-radio-group @change="changeDDType" v-model="radioAccept">
              <el-radio-button value="day">今日</el-radio-button>
              <el-radio-button value="weekend">本周</el-radio-button>
              <el-radio-button value="month">本月</el-radio-button>
              <el-radio-button value="year">全年</el-radio-button>
            </el-radio-group>
          </div>
        </div>
        <div class="chart-two">
          <DataBraChart
            :braData="DDserverDataList"
            idRef="Etaswction"
            v-if="DDserverDataList?.yData"
          />
        </div>
      </el-card>
      <el-card class="card" shadow="never">
        <div class="dis-tit">
          <div class="max-tit">监控异常占比</div>
          <div class="tit-right"></div>
        </div>
        <div class="chart-two">
          <PieChart
            idRef="Etctionon"
            :pieData="pieDataThree"
            v-if="pieDataThree.seriesData.length > 0"
          />
        </div>
      </el-card>
    </div>

    <div class="Two-box">
      <el-card class="card" shadow="never">
        <div class="dis-tit">
          <div class="max-tit">监控量增长趋势</div>
          <div class="tit-right">
            <el-select
              @change="changeSevMY"
              v-model="sevOneVal"
              style="width: 100px; margin-right: 10px"
              placeholder="请选择"
            >
              <el-option value="月度">月度</el-option>
              <el-option value="年度">年度</el-option>
            </el-select>
            <el-date-picker
              @change="changeSevTYD"
              v-if="sevOneVal == '月度'"
              v-model="sevTwoVal"
              type="month"
              :clearable="false"
              value-format="YYYY-MM"
              style="width: 110px"
              placeholder="请选择"
            />
            <el-date-picker
              @change="changeSevTND"
              v-else
              v-model="sevThreeVal"
              :clearable="false"
              style="width: 110px"
              value-format="YYYY"
              type="year"
              placeholder="请选择"
            />
          </div>
        </div>
        <div class="total-list">
          <div class="num-item">
            <div class="top-box">
              <div class="txt">{{ countIncreaseData[0].name }}</div>
              <div class="num">{{ countIncreaseData[0].value }}</div>
            </div>
            <div class="top-box">
              <div class="txt">同比</div>
              <div class="num">{{ countIncreaseData[0].yearOnYearRatio }}%</div>
            </div>
            <div class="top-box">
              <div class="txt">环比</div>
              <div class="num">{{ countIncreaseData[0].monthOnMonthRatio }}%</div>
            </div>
          </div>
          <div class="num-item">
            <div class="top-box">
              <div class="txt">{{ countIncreaseData[1].name }}</div>
              <div class="num">{{ countIncreaseData[1].value }}</div>
            </div>
            <div class="top-box">
              <div class="txt">同比</div>
              <div class="num">{{ countIncreaseData[1].yearOnYearRatio }}%</div>
            </div>
            <div class="top-box">
              <div class="txt">环比</div>
              <div class="num">{{ countIncreaseData[1].monthOnMonthRatio }}%</div>
            </div>
          </div>
          <div class="num-item">
            <div class="top-box">
              <div class="txt">{{ countIncreaseData[2].name }}</div>
              <div class="num">{{ countIncreaseData[2].value }}</div>
            </div>
            <div class="top-box">
              <div class="txt">同比</div>
              <div class="num">{{ countIncreaseData[1].yearOnYearRatio }}%</div>
            </div>
            <div class="top-box">
              <div class="txt">环比</div>
              <div class="num">{{ countIncreaseData[2].monthOnMonthRatio }}%</div>
            </div>
          </div>
        </div>
      </el-card>
      <el-card class="cardTwo" shadow="never">
        <div class="dis-tit">
          <div class="max-tit">货损金额</div>
          <div class="tit-right">
            <el-radio-group @change="changeLineype" v-model="radioLine">
              <el-radio-button value="day">今日</el-radio-button>
              <el-radio-button value="weekend">本周</el-radio-button>
              <el-radio-button value="month">本月</el-radio-button>
              <el-radio-button value="year">全年</el-radio-button>
            </el-radio-group>
          </div>
        </div>
        <div class="chart-two">
          <DataLineChart idRef="Ethctionon" :lineData="serveLineList" v-if="serveLineList?.yData" />
        </div>
      </el-card>
    </div>
  </div>
</template>
<script>
import PieChart from '@/components/PieChart.vue'
import DataBraChart from '@/components/DataBraChart.vue'
import DataLineChart from '@/components/DataLineChart.vue'
import { tms_web } from '@/api/ccms'
import CustomVueSeamlessScroll from '@/components/CustomVueSeamlessScroll.vue'

export default {
  components: {
    PieChart,
    DataBraChart,
    DataLineChart,
    CustomVueSeamlessScroll
  },
  data() {
    return {
      countHomeTotal: {},
      countCustListData: [],
      radioAccept: 'day',
      DDserverDataList: {},
      pieDataThree: {
        seriesData: [],
        color: ['#D68711 ', '#36B336', '#D68711', '#E04F48', '#A23AF3']
      },

      sevOneVal: '月度',
      sevTwoVal: '',
      sevThreeVal: '',
      countIncreaseData: [[], [], []],

      serveLineList: [], // 货损金额
      radioLine: 'day',

      radio: '1',
      value: '',
      lineData: {
        xData: [
          '1月',
          '2月',
          '3月',
          '4月',
          '5月',
          '6月',
          '7月',
          '8月',
          '9月',
          '10月',
          '11月',
          '12月'
        ],
        yData: [20, 30, 40, 50, 60, 70, 80, 90, 100, 110, 120, 130],
        lineColor: '#D32F2F', // 线条颜色
        lineAreaColor: 'rgba(211, 47, 47, 0.5)' // 线条区域颜色
      },
      pieData: {
        seriesData: [
          { value: 80, name: '接单率' },
          { value: 50, name: '未接单率' }
        ],
        color: ['#3d64cf', '#ff9900']
      },
      braData: {
        xData: [
          '1月',
          '2月',
          '3月',
          '4月',
          '5月',
          '6月',
          '7月',
          '8月',
          '9月',
          '10月',
          '11月',
          '12月'
        ],
        yData: [20, 30, 40, 50, 60, 70, 80, 90, 100, 110, 120, 130],
        braColor: '#000', // 线条颜色
        lineAreaColor: 'rgba(0, 178, 255, 1)'
      }
    }
  },
  methods: {
    // 统计数据 -- top
    getTopCountHomeTotal() {
      tms_web
        .countCold_countColdCountVo_post({
          data: {}
        })
        .then((res) => {
          console.log(res)
          this.countHomeTotal = res.data
        })
    },
    // 客户监控次数排名 Top 10
    getDataQXList() {
      tms_web
        .countCold_countCustomerMonitorRankTop10_post({
          data: {}
        })
        .then((res) => {
          this.countCustListData = res.data
        })
    },
    // 冷链监控量change
    changeDDType(value) {
      // this.DDserverDataList = []
      this.radioAccept = value
      this.getDriverActivity()
    },
    // 冷链监控量
    getDriverActivity() {
      tms_web
        .countCold_countColdMonitorByTime_post({
          data: {
            timeType: this.radioAccept
          }
        })
        .then((res) => {
          console.log(res)
          let data = res.data || {}
          let Xdata = []
          let Ydata = []
          data.forEach((item) => {
            Xdata.push(item.name)
            Ydata.push(item.value)
          })
          this.DDserverDataList = {
            xData: Xdata,
            yData: Ydata,
            lineColor: '#2D57CC', // 线条颜色
            lineAreaColor: 'rgba(45, 87, 204, 0.8)' // 线条区域颜色
          }
        })
    },
    // 监控异常占比
    getThreeTotal() {
      tms_web
        .countCold_countColdMonitorError_post({
          data: {}
        })
        .then((res) => {
          this.pieDataThree.seriesData = res.data
        })
    },

    // 交易额增长趋势
    getlIncrease() {
      let startTime = ''
      let endTime = ''
      if (this.sevOneVal == '月度') {
        if (!this.sevTwoVal) {
          return
        }
        startTime = this.sevTwoVal + '-01 00:00:00'
        endTime = this.sevTwoVal + '-31 23:59:59'
      } else if (this.sevOneVal == '年度') {
        if (!this.sevThreeVal) {
          return
        }
        startTime = this.sevThreeVal + '-01-01 00:00:00'
        endTime = this.sevThreeVal + '-12-31 23:59:59'
      }
      tms_web
        .countCold_countIncrease_post({
          data: {
            startTime: startTime,
            endTime: endTime
          }
        })
        .then((res) => {
          this.countIncreaseData = res.data
        })
    },
    changeSevMY(val) {
      this.sevOneVal = val
      if (this.sevOneVal == '年度') {
        let date = new Date()
        let year = date.getFullYear()
        console.log(year)
        this.sevThreeVal = String(year)
      }
      this.getlIncrease()
    },
    changeSevTYD(val) {
      this.sevTwoVal = val
      this.getlIncrease()
    },
    changeSevTND(val) {
      this.sevThreeVal = val
      this.getlIncrease()
    },

    // 货损金额-列表
    getCountLinelList() {
      tms_web
        .countCold_countDamageMoneyByTime_post({
          data: {
            timeType: this.radioLine
          }
        })
        .then((res) => {
          let data = res.data
          let Xdata = []
          let Ydata = []
          data.forEach((item) => {
            Xdata.push(item.name)
            Ydata.push(item.value)
          })
          this.serveLineList = {
            xData: Xdata,
            yData: Ydata,
            lineColor: '#2D57CC', // 线条颜色
            lineAreaColor: 'rgba(45, 87, 204, 0.8)' // 线条区域颜色
          }
        })
    },
    changeLineype(val) {
      this.radioLine = val
      this.getCountLinelList()
    }
  },
  mounted() {
    // 获取当前年月 格式是  2022-01
    let date = new Date()
    let year = date.getFullYear()
    let month = date.getMonth() + 1
    if (month < 10) {
      month = '0' + month
    }
    this.sevTwoVal = year + '-' + month
    this.sevThreeVal = year

    this.getTopCountHomeTotal()
    this.getDataQXList()
    this.getDriverActivity()
    this.getThreeTotal()
    this.getlIncrease()
    this.getCountLinelList()
  }
}
</script>
<style scoped lang="scss">
:deep(.el-date-editor.el-input, .el-date-editor.el-input__inner) {
  height: auto !important;
}
.max-tit {
  font-size: 18px;
  color: #000;
  font-weight: bold;
}
.header {
  width: 100%;
  justify-content: space-between;
  display: flex;
  margin-bottom: 20px;
  .card {
    width: calc((100% - 60px) / 4);
    height: 120px;
    border-radius: 15px;
    cursor: pointer;
    text-align: center;
    margin: 0;
    padding: 0;
    .icon {
      img {
        width: 24px;
        height: 24px;
        margin: 0 auto;
      }
      i {
        font-size: 20px;
        color: #2d57cc;
      }
    }
    .num {
      font-weight: bold;
      font-size: 24px;
      color: #2d57cc;
      margin: 5px 0;
    }
    .text {
      font-size: 14px;
    }
  }
}

.three-box {
  width: 100%;
  justify-content: space-between;
  display: flex;
  margin-bottom: 20px;
  .card {
    width: calc((100% - 40px) / 3);
    height: 270px;
    border-radius: 15px;
    // cursor: pointer;
    margin: 0;
    padding: 0;
    .dis-tit {
      display: flex;
      justify-content: space-between;
    }
    .tit-right {
      display: flex;
      align-items: center;
      .mess {
        font-size: 14px;
        margin-right: 20px;
      }
    }
    .chart-two {
      height: 200px;
      // background: #ccc;
    }
  }
}

.Two-box {
  width: 100%;
  justify-content: space-between;
  display: flex;
  margin-bottom: 20px;
  .card {
    width: calc((33%) / 1);
    height: 320px;
    border-radius: 15px;
    cursor: pointer;
    margin: 0;
    padding: 0;
    .dis-tit {
      display: flex;
      justify-content: space-between;
    }
    .tit-right {
      display: flex;
      align-items: center;
      .mess {
        font-size: 14px;
        margin-right: 20px;
      }
    }
    .chart-two {
      height: 200px;
      // background: #ccc;
    }
  }
  .cardTwo {
    width: calc((66%) / 1);
    height: 320px;
    border-radius: 15px;
    cursor: pointer;
    margin: 0;
    padding: 0;
    .dis-tit {
      display: flex;
      justify-content: space-between;
    }
    .tit-right {
      display: flex;
      align-items: center;
      .mess {
        font-size: 14px;
        margin-right: 20px;
      }
    }
    .chart-two {
      height: 260px;
      // background: #ccc;
    }
  }
  // .total-list {
  //   display: flex;
  //   justify-content: space-between;
  //   .num-item {
  //     width: calc((100% - 40px ) / 3);
  //     // width: calc((100% - 40px) / 3);
  //     height: 50px;
  //     // border-radius: 15px;
  //     background: #eaeffa;
  //     border-radius: 10px;
  //     margin: 5px 0;
  //     padding: 5px 10px;
  //     font-size: 14px;
  //     .num {
  //       color: #2d57cc;
  //     }
  //   }
  // }
  .total-list {
    margin-top: 20px;
    .num-item {
      height: 70px;
      width: 100%;
      margin: 10px 0;
      display: flex;
      align-items: center;
      border-radius: 8px;
      justify-content: space-between;
      padding: 0 10px;
      .num {
        margin-top: 5px;
        font-weight: bold;
        font-size: 16px;
      }
    }
    .num-item:nth-child(1) {
      background: rgba(45, 87, 204, 0.1);
      .num {
        color: #2d57cc;
      }
    }
    .num-item:nth-child(2) {
      background: rgba(214, 135, 17, 0.1);
      .num {
        color: #d68711;
      }
    }
    .num-item:nth-child(3) {
      background: rgba(224, 79, 72, 0.1);
      .num {
        color: #e04f48;
      }
    }
  }
}
// .list {
//   margin-top: 15px;
//   background: #eee;
//   height: 190px;
//   .scroller {
//     height: 190px;
//   }
//   .item {
//     display: flex;
//     justify-content: space-between;
//     margin-bottom: 10px;
//     .text {
//       width: calc(100% - 50px);
//       // background: #ccc;
//       // 单行省略号
//       overflow: hidden;
//       text-overflow: ellipsis;
//       white-space: nowrap;
//       span {
//         margin-right: 7px;
//         width: 24px;
//         height: 24px;
//         display: inline-block;
//         img {
//           width: 24px;
//           height: 24px;
//           display: inline-block;
//           vertical-align: middle;
//         }
//       }
//     }
//     .num {
//       color: #2d57cc;
//       font-size: 15px;
//       font-weight: 600;
//     }
//   }
// }
</style>
