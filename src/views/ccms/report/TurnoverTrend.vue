<template>
  <div>
    <div class="header">
      <el-card class="card" shadow="never">
        <div class="icon"><img src="@/assets/images/icon/m.png" alt="" /></div>
        <div class="num">{{ seriesDataCount.transportOrderMoney }}</div>
        <div class="text">运单总金额（万元）</div>
      </el-card>
      <el-card class="card" shadow="never">
        <div class="icon"><img src="@/assets/images/icon/n.png" alt="" /></div>
        <div class="num">{{ seriesDataCount.waitPayMoney }}</div>
        <div class="text">待支付金额（万元）</div>
      </el-card>
      <el-card class="card" shadow="never">
        <div class="icon"><img src="@/assets/images/icon/q.png" alt="" /></div>
        <div class="num">{{ seriesDataCount.payedMoney }}</div>
        <div class="text">已支付金额（万元）</div>
      </el-card>
      <el-card class="card" shadow="never">
        <div class="icon"><img src="@/assets/images/icon/w.png" alt="" /></div>
        <div class="num">{{ seriesDataCount.damageMoney }}</div>
        <div class="text">货损金额（万元）</div>
      </el-card>
    </div>

    <div class="three-box">
      <el-card class="card" shadow="never">
        <div class="dis-tit">
          <div class="max-tit">客户交易额排名（万元）</div>
          <div class="tit-right"></div>
        </div>
        <!-- <div class="list">
          <div class="item" v-for="(item, index) in countCustList" :key="index">
            <div class="text">
              <span v-if="index === 0"><img src="@/assets/images/icon/icon-a.png" alt=""/></span>
              <span v-else-if="index === 1"><img src="@/assets/images/icon/icon-b.png" alt=""/></span>
              <span v-else-if="index === 2"><img src="@/assets/images/icon/icon-c.png" alt=""/></span>
              <span v-else>{{index+1}}</span>
              {{item.name}}
            </div>
            <div class="num">{{item.value}}</div>
          </div>
        </div> -->
        <div class="list">
          <CustomVueSeamlessScroll :listData="countCustList" />
        </div>
      </el-card>
      <el-card class="card" shadow="never">
        <div class="dis-tit">
          <div class="max-tit">交易金额</div>
          <div class="tit-right">
            <el-radio-group @change="changeDDType" v-model="radioAccept">
              <el-radio-button value="day">今日</el-radio-button>
              <el-radio-button value="weekend">本周</el-radio-button>
              <el-radio-button value="month">本月</el-radio-button>
              <el-radio-button value="year">全年</el-radio-button>
            </el-radio-group>
          </div>
        </div>
        <div class="chart-two">
          <DataBraChart
            :braData="DDserverDataList"
            idRef="Etaewcbwysdaon"
            v-if="DDserverDataList?.yData"
          />
        </div>
      </el-card>
      <el-card class="card" shadow="never">
        <div class="dis-tit">
          <div class="max-tit">支付方式占比</div>
          <div class="tit-right"></div>
        </div>
        <div class="chart-two">
          <PieChart
            idRef="Ethibqbuution"
            :pieData="pieDataOne"
            v-if="pieDataOne.seriesData.length > 0"
          />
        </div>
      </el-card>
    </div>

    <div class="Two-box">
      <el-card class="card" shadow="never">
        <div class="dis-tit">
          <div class="max-tit">交易额增长趋势</div>
          <div class="tit-right">
            <el-select
              @change="changeSevMY"
              v-model="sevOneVal"
              style="width: 100px; margin-right: 10px"
              placeholder="请选择"
            >
              <el-option value="月度">月度</el-option>
              <el-option value="年度">年度</el-option>
            </el-select>
            <el-date-picker
              @change="changeSevTYD"
              v-if="sevOneVal == '月度'"
              v-model="sevTwoVal"
              type="month"
              :clearable="false"
              value-format="YYYY-MM"
              style="width: 110px"
              placeholder="请选择"
            />
            <el-date-picker
              @change="changeSevTND"
              v-else
              :clearable="false"
              v-model="sevThreeVal"
              style="width: 110px"
              value-format="YYYY"
              type="year"
              placeholder="请选择"
            />
          </div>
        </div>
        <div class="total-list">
          <div class="num-item">
            <div class="num-title">{{ severDataJYparmet.name }}</div>
            <div class="num">
              {{ severDataJYparmet.value }}
              <span class="unit">万吨</span>
            </div>
          </div>
          <div class="num-item">
            <div class="num-title">同比</div>
            <div class="num">
              {{ severDataJYparmet.yearOnYearRatio }}%
              <!-- <span class="unit">%</span> -->
            </div>
          </div>
          <div class="num-item">
            <div class="num-title">环比</div>
            <div class="num">
              {{ severDataJYparmet.monthOnMonthRatio }}%
              <!-- <span class="unit">%</span> -->
            </div>
          </div>
        </div>
        <div class="chart-two">
          <PieChart
            idRef="Ethqtion"
            :pieData="pieDataJYparmet"
            v-if="pieDataJYparmet.seriesData.length > 0"
          />
        </div>
      </el-card>
      <el-card class="cardTwo" shadow="never">
        <div class="dis-tit">
          <div class="max-tit">货损金额</div>
          <div class="tit-right">
            <el-radio-group @change="changeCellType" v-model="radioCell">
              <el-radio-button value="day">今日</el-radio-button>
              <el-radio-button value="weekend">本周</el-radio-button>
              <el-radio-button value="month">本月</el-radio-button>
              <el-radio-button value="year">全年</el-radio-button>
            </el-radio-group>
          </div>
        </div>
        <div class="chart-two">
          <DataLineChart
            idRef="Ethxsqw"
            :lineData="YDserverDataList"
            v-if="YDserverDataList?.yData"
          />
        </div>
      </el-card>
    </div>
  </div>
</template>
<script>
import PieChart from '@/components/PieChart.vue'
import DataBraChart from '@/components/DataBraChart.vue'
import DataLineChart from '@/components/DataLineChart.vue'
import { tms_web } from '@/api/ccms'
import CustomVueSeamlessScroll from '@/components/CustomVueSeamlessScroll.vue'
export default {
  components: {
    PieChart,
    DataBraChart,
    DataLineChart,
    CustomVueSeamlessScroll
  },
  data() {
    return {
      seriesDataCount: {},
      countCustList: [],
      radioAccept: 'day',
      DDserverDataList: {},
      pieDataOne: {
        seriesData: [],
        color: ['#2D57CC ', '#36B336', '#D68711', '#E04F48', '#A23AF3']
      },
      severDataJYparmet: {},
      pieDataJYparmet: {
        seriesData: [],
        color: ['#2D57CC ', '#36B336', '#D68711', '#E04F48', '#A23AF3']
      },

      radioCell: 'day',
      YDserverDataList: [], // 正常完单率分析-list

      sevOneVal: '月度',
      sevTwoVal: '',
      sevThreeVal: '',

      radio: '1',
      value3: '',
      lineData: {
        xData: [
          '1月',
          '2月',
          '3月',
          '4月',
          '5月',
          '6月',
          '7月',
          '8月',
          '9月',
          '10月',
          '11月',
          '12月'
        ],
        yData: [20, 30, 40, 50, 60, 70, 80, 90, 100, 110, 120, 130],
        lineColor: '#D32F2F', // 线条颜色
        lineAreaColor: 'rgba(211, 47, 47, 0.5)' // 线条区域颜色
      },
      pieData: {
        seriesData: [
          { value: 80, name: '接单率' },
          { value: 50, name: '未接单率' }
        ],
        color: ['#3d64cf', '#ff9900']
      },
      braData: {
        xData: [
          '1月',
          '2月',
          '3月',
          '4月',
          '5月',
          '6月',
          '7月',
          '8月',
          '9月',
          '10月',
          '11月',
          '12月'
        ],
        yData: [20, 30, 40, 50, 60, 70, 80, 90, 100, 110, 120, 130],
        braColor: '#000', // 线条颜色
        lineAreaColor: 'rgba(0, 178, 255, 1)'
      }
    }
  },
  methods: {
    // 统计数据
    getTopCountHomeTotal() {
      tms_web
        .countTrade_countTradeCountVo_post({
          data: {}
        })
        .then((res) => {
          this.seriesDataCount = res.data
        })
    },
    // 客户交易额排名（万元）- list
    getDataQXList() {
      tms_web
        .countTrade_countCustomerMoney_post({
          data: {}
        })
        .then((res) => {
          this.countCustList = res.data
        })
    },

    // 交易金额
    getDriverActivity() {
      tms_web
        .countTrade_countTransactionAmountByTime_post({
          data: {
            timeType: this.radioAccept
          }
        })
        .then((res) => {
          console.log(res)
          let data = res.data || {}
          let Xdata = []
          let Ydata = []
          data.forEach((item) => {
            Xdata.push(item.name)
            Ydata.push(item.value)
          })
          this.DDserverDataList = {
            xData: Xdata,
            yData: Ydata,
            lineColor: '#2D57CC', // 线条颜色
            lineAreaColor: 'rgba(45, 87, 204, 0.8)' // 线条区域颜色
          }
        })
    },
    // 订单统计change
    changeDDType(value) {
      this.DDserverDataList = []
      this.radioAccept = value
      this.getDriverActivity()
    },
    // 支付方式占比
    getOneTotal() {
      tms_web
        .countTrade_countPayType_post({
          data: {}
        })
        .then((res) => {
          this.pieDataOne.seriesData = res.data
        })
    },
    // 交易额增长趋势
    getJYaccert() {
      let startTime = ''
      let endTime = ''
      if (this.sevOneVal == '月度') {
        startTime = this.sevTwoVal + '-01 00:00:00'
        endTime = this.sevTwoVal + '-31 23:59:59'
      } else if (this.sevOneVal == '年度') {
        startTime = this.sevThreeVal + '-01-01 00:00:00'
        endTime = this.sevThreeVal + '-12-31 23:59:59'
      }
      tms_web
        .countTrade_countGoodIncrease_post({
          data: {
            startTime: startTime,
            endTime: endTime
          }
        })
        .then((res) => {
          this.severDataJYparmet = res.data[0]
          // this.pieDataJYparmet.seriesData = res.data
        })
    },
    // 支付方式占比
    getOneTotalTwo() {
      let startTime = ''
      let endTime = ''
      if (this.sevOneVal == '月度') {
        if (!this.sevTwoVal) {
          return
        }
        startTime = this.sevTwoVal + '-01 00:00:00'
        endTime = this.sevTwoVal + '-31 23:59:59'
      } else if (this.sevOneVal == '年度') {
        if (!this.sevThreeVal) {
          return
        }
        startTime = this.sevThreeVal + '-01-01 00:00:00'
        endTime = this.sevThreeVal + '-12-31 23:59:59'
      }
      tms_web
        .countTrade_countPayType_post({
          data: {
            startTime: startTime,
            endTime: endTime
          }
        })
        .then((res) => {
          this.pieDataJYparmet.seriesData = res.data
        })
    },
    changeSevMY(val) {
      this.sevOneVal = val
      if (this.sevOneVal == '年度') {
        let date = new Date()
        let year = date.getFullYear()
        console.log(year)
        this.sevThreeVal = String(year)
      }
      this.getOneTotalTwo()
      this.getJYaccert()
    },
    changeSevTYD(val) {
      this.sevTwoVal = val
      this.getOneTotalTwo()
      this.getJYaccert()
    },
    changeSevTND(val) {
      this.sevThreeVal = val
      this.getOneTotalTwo()
      this.getJYaccert()
    },

    // 货损金额-列表
    getYdCountTotalList() {
      tms_web
        .countTrade_countDamageMoneyByTime_post({
          data: {
            timeType: this.radioCell
          }
        })
        .then((res) => {
          let data = res.data
          let Xdata = []
          let Ydata = []
          data.forEach((item) => {
            Xdata.push(item.name)
            Ydata.push(item.value)
          })
          this.YDserverDataList = {
            xData: Xdata,
            yData: Ydata,
            lineColor: '#E04F48', // 线条颜色
            lineAreaColor: 'rgba(224, 79, 72, 0.8)' // 线条区域颜色
          }
        })
    },
    changeCellType(value) {
      this.radioCell = value
      this.YDserverDataList = {}
      this.getYdCountTotalList()
    }
  },
  mounted() {
    // 获取当前年月 格式是  2022-01
    let date = new Date()
    let year = date.getFullYear()
    let month = date.getMonth() + 1
    if (month < 10) {
      month = '0' + month
    }
    this.sevTwoVal = year + '-' + month
    this.sevThreeVal = year
    this.getTopCountHomeTotal()
    this.getDataQXList()
    this.getDriverActivity()
    this.getOneTotal()

    this.getJYaccert()
    this.getOneTotalTwo()
    this.getYdCountTotalList()
  }
}
</script>
<style scoped lang="scss">
:deep(.el-date-editor.el-input, .el-date-editor.el-input__inner) {
  height: auto !important;
}
.max-tit {
  font-size: 18px;
  color: #000;
  font-weight: bold;
}
.header {
  width: 100%;
  justify-content: space-between;
  display: flex;
  margin-bottom: 20px;
  .card {
    width: calc((100% - 60px) / 4);
    height: 120px;
    border-radius: 15px;
    cursor: pointer;
    text-align: center;
    margin: 0;
    padding: 0;
    .icon {
      img {
        width: 24px;
        height: 24px;
        margin: 0 auto;
      }
      i {
        font-size: 20px;
        color: #2d57cc;
      }
    }
    .num {
      font-weight: bold;
      font-size: 24px;
      color: #2d57cc;
      margin: 5px 0;
    }
    .text {
      font-size: 14px;
    }
  }
}

.three-box {
  width: 100%;
  justify-content: space-between;
  display: flex;
  margin-bottom: 20px;
  .card {
    width: calc((100% - 40px) / 3);
    height: 270px;
    border-radius: 15px;
    margin: 0;
    padding: 0;
    // cursor: pointer;
    .dis-tit {
      display: flex;
      justify-content: space-between;
    }
    .tit-right {
      display: flex;
      align-items: center;
      .mess {
        font-size: 14px;
        margin-right: 20px;
      }
    }
    .chart-two {
      height: 200px;
      // background: #ccc;
    }
  }
}

.Two-box {
  width: 100%;
  justify-content: space-between;
  display: flex;
  margin-bottom: 20px;
  .card {
    width: calc((33%) / 1);
    height: 320px;
    border-radius: 15px;
    margin: 0;
    padding: 0;
    // cursor: pointer;
    .dis-tit {
      display: flex;
      justify-content: space-between;
    }
    .tit-right {
      display: flex;
      align-items: center;
      .mess {
        font-size: 14px;
        margin-right: 20px;
      }
    }
    .chart-two {
      margin-top: 10px;
      height: 200px;
      // background: #ccc;
    }
  }
  .cardTwo {
    width: calc((66%) / 1);
    height: 320px;
    border-radius: 15px;
    cursor: pointer;
    margin: 0;
    padding: 0;
    .dis-tit {
      display: flex;
      justify-content: space-between;
    }
    .tit-right {
      display: flex;
      align-items: center;
      .mess {
        font-size: 14px;
        margin-right: 20px;
      }
    }
    .chart-two {
      height: 250px;
      // background: #ccc;
    }
  }
  .total-list {
    display: flex;
    justify-content: space-between;
    height: 52px;
    .num-item {
      width: calc((100% - 40px) / 3);
      // width: calc((100% - 40px) / 3);
      height: 50px;
      // border-radius: 15px;
      background: rgba(45, 87, 204, 0.1);
      border-radius: 4px 4px 4px 4px;
      margin: 10px 0 10px;
      padding: 5px 10px;
      font-weight: 400;
      font-size: 12px;
      color: #333333;
      .num {
        font-weight: bold;
        font-size: 16px;
        color: #2d57cc;
        line-height: 19px;
        span {
          font-size: 12px;
          font-weight: 400;
        }
      }
    }
  }
}
.list {
  margin-top: 15px;
  .item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    .text {
      width: calc(100% - 50px);
      // background: #ccc;
      // 单行省略号
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      span {
        margin-right: 7px;
        width: 24px;
        height: 24px;
        display: inline-block;
        img {
          width: 24px;
          height: 24px;
          display: inline-block;
          vertical-align: middle;
        }
      }
    }
    .num {
      color: #2d57cc;
      font-size: 15px;
      font-weight: 600;
    }
  }
}
</style>
