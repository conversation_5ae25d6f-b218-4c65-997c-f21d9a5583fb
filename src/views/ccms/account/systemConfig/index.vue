<template>
  <div class="system-index">
    <el-card shadow="never">
      <template #header>
        <div class="clearfix">
          <span class="left">系统设置</span>
        </div>
      </template>

      <!-- 表单-->
      <el-form
        ref="formValidate"
        :model="formData"
        :rules="rules"
        label-position="right"
        label-width="220px"
      >
        <el-form-item label="系统名称：" prop="name">
          <el-input class="input" v-model.trim="formData.name" type="text" />
        </el-form-item>
        <el-form-item label="系统LOGO：" prop="logoFile">
          <SelectImage :file="formData.logoFile" type="logo" @on-file-change="uploadFile" />
          <span class="explain"
            >用于变更系统左上角“白色LOGO”，建议上传白色png图片，图片尺寸80*80px。</span
          >
        </el-form-item>
        <el-form-item label="网址ICON：" prop="iconFile">
          <SelectImage :file="formData.iconFile" type="icon" @on-file-change="uploadFile" />
          <span class="explain"
            >用于变更浏览器标签页上的“favicon.ico”，建议上传尺寸 16*16px 或 32*32px。</span
          >
        </el-form-item>
        <el-form-item label="收款账号类型：" prop="accountType">
          <el-radio-group v-model="formData.accountType" size="small">
            <el-radio-button
              v-for="(item, index) in getStrDictOptions(DICT_TYPE.BANK_ACCOUNT_TYPE)"
              :label="item.label"
              :value="item.value"
              :key="index"
            />
          </el-radio-group>
        </el-form-item>
        <el-form-item label="收款银行：" prop="bankName" v-if="formData.accountType === 'bank'">
          <el-select
            v-model="formData.bankName"
            placeholder="请选择收款账号类型"
            style="width: 400px"
            clearable
          >
            <el-option
              v-for="(item, index) in getStrDictOptions(DICT_TYPE.BANK)"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="收款账号：" prop="account">
          <el-input
            class="input"
            placeholder="请输入收款账号"
            v-model.trim="formData.account"
            type="text"
          />
        </el-form-item>
        <el-form-item label="开启预冷预热：" prop="openColdHot">
          <el-radio-group v-model="formData.openColdHot">
            <el-radio
              v-for="(item, index) in getStrDictOptions(DICT_TYPE.OPEN_CLOSE)"
              :label="item.label"
              :value="item.value"
              :key="index"
            />
          </el-radio-group>
        </el-form-item>
        <el-form-item label="撤单时间限制：" prop="cancelLimitTime">
          <el-input
            class="input"
            placeholder="请输入撤单时间限制"
            v-model.trim="formData.cancelLimitTime"
            type="text"
          >
            <template #prepend>装货时间前</template>
            <template #suffix>分钟允许撤单</template>
          </el-input>
        </el-form-item>
      </el-form>
    </el-card>
    <!--底部-->
    <div class="card-footer">
      <el-button type="primary" @click="save">保存</el-button>
      <el-button type="info" @click="recovery">还原</el-button>
    </div>
  </div>
</template>
<script>
import { DICT_TYPE, getDictOptions, getStrDictOptions } from '@/utils/dict'
import { ElLoading, ElMessageBox } from 'element-plus'
import _ from 'lodash'
import { cloneDeep } from 'lodash-es'
import request from '@/config/axios'
import { sysType } from '@/utils/sysType'
import { useSysConfigStore } from '@/store/modules/sysConfig'
import { storeToRefs } from 'pinia'

export default {
  setup() {
    const { syncSysConfig } = useSysConfigStore()
    const { sysConfig } = storeToRefs(useSysConfigStore())
    const that = getCurrentInstance().proxy
    onBeforeRouteLeave((to, from, next) => {
      if (!that.isChange) {
        ElMessageBox.confirm(
          `您调整了【系统设置】里的内容，并且没有保存！是否需要保存后再跳转页面？`,
          '保存提醒！',
          {
            confirmButtonText: '保存并跳转',
            cancelButtonText: '取消',
            type: 'large',
            leftButtonText: '放弃保存并跳转',
            leftButtonTextShow: true
          }
        )
          ?.then((res) => {
            if (res === 'confirm') {
              that.save()
              if (that.valid) {
                next({
                  path: to.path
                })
              } else {
                next(false)
              }
            } else {
              next()
            }
          })
          .catch(() => {
            next(false)
          })
      } else {
        next()
      }
    })

    return {
      syncSysConfig,
      sysConfig
    }
  },
  data() {
    return {
      DICT_TYPE,
      formData: {
        accountType: ''
      },
      formDataCopy: {},
      list: [],
      rules: [],
      valid: true
    }
  },
  computed: {
    // 内容是否改变
    isChange() {
      return (
        Object.entries(this.formData).toString() === Object.entries(this.formDataCopy).toString()
      )
    }
  },
  created() {
    this.loadData()
    this.getRules()
  },
  methods: {
    getStrDictOptions,
    getDictOptions,
    uploadFile(e) {
      console.log(e)
      const temp = cloneDeep(this.formData)
      temp[`${e.type}File`] = e.file
      this.formData = temp
      this.$refs.formValidate.validate(() => {})
    },
    getRules() {
      this.rules = {
        name: [{ required: true, message: '请输入系统名称', trigger: 'blur' }],
        accountType: [{ required: true, message: '请选择收款账号类型', trigger: 'change' }],
        bankName: [{ required: true, message: '请输入收款银行', trigger: 'change' }],
        account: [{ required: true, message: '请输入收款账号', trigger: 'blur' }],
        logoFile: [
          {
            required: true,
            validator: this.validator,
            trigger: 'change'
          }
        ],
        iconFile: [
          {
            required: true,
            validator: this.validator,
            trigger: 'change'
          }
        ],
        openColdHot: [
          {
            required: true,
            message: '请选择是否开启预冷预热',
            trigger: 'change'
          }
        ],
        cancelLimitTime: [
          {
            required: true,
            message: '请输入撤单时间限制',
            trigger: 'blur'
          },
          {
            validator: (rule, value, callback) => {
              if (!/^[1-9]\d*$/.test(value)) {
                callback(new Error('撤单时间限制必须为正整数'))
              } else {
                callback()
              }
            }
          }
        ]
      }
    },
    loadData() {
      this.syncSysConfig().then(() => {
        this.formData = cloneDeep(this.sysConfig)
        this.formDataCopy = _.cloneDeep(this.formData)
      })
    },
    // 还原
    recovery() {
      this.loadData()
      // 移除该表单项的校验结果
      this.$refs.formValidate.clearValidate()
    },
    validator(rule, value, callback) {
      if (!value && rule.field === 'logoFile') {
        callback(new Error('请上传系统LOGO'))
      } else if (!value && rule.field === 'iconFile') {
        callback(new Error('请上传网址ICON'))
      } else if (value && rule.field === 'logoFile' && value.size && value.size / 1024 / 1024 > 2) {
        callback(new Error('系统LOGO大小不能超过2MB'))
      } else if (value && rule.field === 'iconFile' && value.size && value.size / 1024 > 512) {
        callback(new Error('网址ICON大小不能超过512KB'))
      } else {
        callback()
      }
    },
    // 判断是不是File类型
    instanceofFile(file) {
      return file instanceof File
    },
    save() {
      let { logoFile, iconFile, ...query } = this.formData

      let params = {
        ...query
      }

      this.instanceofFile(logoFile) ? (params.logoFile = logoFile) : ''
      this.instanceofFile(iconFile) ? (params.iconFile = iconFile) : ''

      this.$refs.formValidate.validate((valid) => {
        this.valid = valid
        if (valid) {
          this.updateData(params)
          this.formDataCopy = this.formData
        }
      })
    },
    async updateData(params) {
      try {
        ElLoading.service({
          lock: true,
          text: '正在保存...'
        })
        if (params.id) {
          await request.upload({
            url: `/${sysType}/systemConfig/update`,
            data: params
          })
        } else {
          await request.upload({
            url: `/${sysType}/systemConfig/save`,
            data: params
          })
        }
        //同步系统配置
        this.syncSysConfig().then()
        this.$notify({
          title: '提示',
          message: '操作成功！',
          type: 'success'
        })
      } finally {
        ElLoading.service().close()
      }
    }
  }
}
</script>

<style scoped lang="scss">
.system-index {
  :deep(.el-card) {
    border-radius: 20px 20px 0 0;

    .el-card__header {
      height: 50px;
      line-height: 50px;
      padding: 0 30px;
      border-radius: 20px 20px 0 0;
    }

    .el-card__body {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 30px 120px;
    }
  }

  .clearfix {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .left {
      color: #505050;
      font-size: 16px;
      font-weight: 400;
    }
  }

  :deep(.custom-table) {
    .custom-table-box {
      padding: 0;
    }

    .el-table__header-wrapper {
      border-radius: 0;
    }
  }

  :deep(.el-form) {
    .el-form-item {
      .el-form-item__label {
        font-size: 13px;
      }

      .el-form-item__content {
        display: flex;
        align-items: center;
        line-height: normal;

        .print-str {
          font-size: 13px;
          margin-left: 20px;
          text-decoration-line: underline;
        }

        .explain {
          line-height: 40px;
          font-size: 13px;
          color: #505050;
          margin-left: 20px;
          font-weight: 400;

          &.tips {
            opacity: 0.5;
          }
        }

        .input {
          &.el-input {
            height: 36px;
            width: 400px;

            .el-input__inner {
              height: 100%;
              font-size: 13px;
            }
          }
        }

        .el-select__wrapper {
          width: 400px;
          height: 36px;
          font-size: 13px;
        }

        .el-radio {
          line-height: 40px;

          .el-radio__label {
            font-size: 13px;
            font-weight: 400;
          }
        }

        .el-radio-group {
          height: 36px;

          .el-radio-button {
            width: 80px;
            height: 36px;

            .el-radio-button__inner {
              padding: 0;
              height: 100%;
              width: 100%;
              line-height: 36px;
            }
          }
        }

        .el-input-number {
          width: 160px;
          font-size: 13px;
        }

        .el-select {
          .el-input__inner {
            width: 400px;
            height: 36px;
            font-size: 13px;
          }
        }

        .el-textarea__inner {
          font-family: auto;
        }

        .el-textarea__inner {
          width: 250px;
          font-size: 13px;
        }
      }
    }
  }

  :deep(.cascader) {
    height: 36px;

    .el-input__inner {
      height: 36px;
      font-size: 13px;
    }
  }

  .address {
    display: flex;
    align-items: center;

    :deep(.el-form-item) {
      &:nth-child(2) {
        height: 36px;

        .el-form-item__content {
          margin-left: 10px !important;

          .el-input {
            width: 420px;
            height: 36px;

            .el-input__inner {
              font-size: 13px;
              height: 36px;
            }
          }
        }
      }
    }
  }

  .card-footer {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 60px;
    width: 100%;
    border-top: 1px solid #ebeef5;
    border-radius: 0px 0px 20px 20px;
    background-color: #fff;
    box-shadow: 0px -2px 4px 0px rgba(0, 0, 0, 0.2);
  }

  .outlets {
    display: flex;
    flex-direction: column;

    .btn {
      width: 75px;
      height: 34px;
      font-size: 13px;
      padding: 0;
      text-align: center;
      line-height: 34px;
      margin-bottom: 10px;
    }
  }

  :deep(.table-input) {
    .el-input__inner {
      height: 36px;
      font-size: 13px;
    }
  }
}
</style>
