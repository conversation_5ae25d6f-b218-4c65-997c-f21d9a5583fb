<template>
  <div class="add-type">
    <open ref="open" @on-save="submitData" :title="title" :width="600">
      <el-form
        :model="formData"
        :rules="rules"
        ref="formValidate"
        label-width="170px"
        class="demo-ruleForm"
      >
        <el-form-item label="员工：" prop="staffId">
          <el-select placeholder="请选择" v-model="formData.staffId" style="width: 250px">
            <el-option
              v-for="item in staffList"
              :value="item.id"
              :label="item.name"
              :key="item.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="费用名称：" prop="expenseName">
          <el-input
            type="text"
            v-model.trim="formData.expenseName"
            placeholder="请输入"
            class="staffInput"
          />
        </el-form-item>

        <el-form-item label="费用说明：" prop="expenseRemark">
          <el-input
            type="text"
            v-model.trim="formData.expenseRemark"
            placeholder="请输入"
            class="staffInput"
          />
        </el-form-item>

        <el-form-item label="费用金额：" prop="expenseMoney">
          <el-input
            type="number"
            v-model.trim="formData.expenseMoney"
            placeholder="请输入"
            class="staffInput"
          />
        </el-form-item>
      </el-form>
    </open>
  </div>
</template>

<script>
import Open from '@/components/Open.vue'
import { tms_web } from '@/api/ccms'

export default {
  components: { Open },
  setup() {
    const message = useMessage()
    return { message }
  },
  data() {
    return {
      id: '',
      formData: {},
      rules: {
        staffId: [{ required: true, message: '请选择员工', trigger: 'change' }],
        expenseName: [{ required: true, message: '请输入费用名称', trigger: 'blur' }],
        expenseMoney: [{ required: true, message: '请输入费用金额', trigger: 'blur' }]
      },
      //员工选择
      staffList: []
    }
  },
  computed: {
    title() {
      return '添加'
    }
  },
  methods: {
    // 提交表单数据
    submitData() {
      this.$refs.formValidate.validate((valid) => {
        if (valid) {
          this.saveOrUpdate()
        }
      })
    },
    // id更新/新增
    async saveOrUpdate() {
      try {
        for (const tmp of this.staffList) {
          if (tmp.id === this.formData.staffId) {
            this.formData.staffName = tmp.name
            break
          }
        }

        this.message.success('添加成功！')

        this.$emit('on-submit')
        this.$refs.open.close()
      } catch (e) {
        console.log(e)
      }
    },

    // 弹窗打开
    open(row) {
      this.orgFun()
      this.$refs.open.open()
    },
    orgFun() {
      tms_web
        .staffManage_queryStaffManage_post({
          data: {
            pageNo: 1,
            pageSize: 999999
          },
          load: { fullLoading: true }
        })
        .then((res) => {
          this.staffList = res?.data?.list || []
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.add-type {
  ::v-deep .el-form-item__label {
    font-size: 13px;
  }

  ::v-deep .el-form-item__content {
    width: 320px;
    font-size: 13px;

    .el-textarea__inner {
      font-family: auto;
      font-size: 13px;
    }
  }
}

.staffInput {
  width: 250px;
}
</style>
