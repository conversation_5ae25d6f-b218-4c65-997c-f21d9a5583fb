<template>
  <div class="flex flex-col gap-5">
    <div class="flex items-center gap-[10px] rounded-[10px] bg-white p-5 leading-none">
      <div class="text-[18px] font-bold text-black"
        >{{ '' }} {{ isView ? '结算单详情' : '' }}{{ isAdd ? '新增结算单' : ''
        }}{{ isUpdate ? '修改结算单' : '' }}
      </div>
      <div class="flex cursor-pointer items-center gap-[6px] text-[13px] text-[#A8ABB2]">
        <Icon icon="tdesign:rollback" />
        <span @click="router.back()">返回</span>
      </div>
    </div>
    <div class="flex flex-col gap-[30px] rounded-[10px] bg-white p-10">
      <InfoBlock name="运单" v-if="isAdd || isUpdate">
        <el-form
          :model="formData"
          :rules="rules"
          ref="formValidate"
          label-width="100px"
          class="demo-ruleForm"
        >
          <el-form-item label="选择运单：" prop="orderId">
            <el-select
              placeholder="请选择运单"
              v-model="formData.orderId"
              style="width: 250px"
              @change="changeOrder"
            >
              <el-option
                v-for="item in orderList"
                :value="item.id || ''"
                :label="item.transportNumber"
                :key="item.id"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="结算方式：" prop="settleType" required>
            <el-radio-group v-model="formData.settleType">
              <el-radio label="cash">现金</el-radio>
              <el-radio label="transfer">转账</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </InfoBlock>

      <InfoBlock name="客户信息">
        <div class="grid grid-cols-2">
          <InfoField name="客户名称" :text="orderInfo.customerName" />
          <InfoField name="下单时间" :text="dateFormatterByOptions({cellValue: orderInfo.orderTime})" />
        </div>
        <div class="grid grid-cols-2">
          <InfoField name="运 单 号" :text="orderInfo.transportNumber" />
          <InfoField name="联 系 人" :text="orderInfo.sendName" />
        </div>
        <div class="grid grid-cols-2">
          <InfoField name="联系电话" :text="orderInfo.sendPhone" />
          <InfoField name="车辆类型" :text="orderInfo.carModelName" />
        </div>
        <div class="grid grid-cols-2">
          <InfoField name="车辆规格"> {{ orderInfo.carLength || '' }}</InfoField>
          <InfoField name="预约时间" :text="dateFormatterByOptions({cellValue: orderInfo.loadingTime})" />
        </div>
        <div class="grid grid-cols-2">
          <InfoField name="装货地址">
            <span>
              {{
                `${orderInfo.sendProvince || ''}${orderInfo.sendCity || ''}${orderInfo.sendCounty || ''}${orderInfo.sendAddress || ''}`
              }}
            </span>
          </InfoField>
        </div>
      </InfoBlock>

      <InfoBlock name="配送信息">
        <custom-table
          :columns="deliveryColumns"
          :data="orderInfo.addressList || []"
          hide-paging
          :padding="0"
          show-index
        />
      </InfoBlock>

      <InfoBlock name="其它费用">
        <div style="margin-left: 90%" v-if="isUpdate || isAdd">
          <el-button type="primary" class="add-btn" round @click="addExpense">
            <Icon icon="ep:plus" />
            添加费用
          </el-button>
        </div>
        <custom-table
          :columns="detailColumns"
          :data="detailList"
          hide-paging
          :padding="0"
          show-index
        />
      </InfoBlock>

      <InfoBlock name="结算信息">
        <div class="grid">
          <InfoField name="其它费用"> {{ formData.totalMoney }}元</InfoField>
        </div>
        <div class="grid">
          <InfoField name="未结算金额" v-if="formData.settleStatus === 'n'"
            >{{ formData.totalMoney }}元
          </InfoField>
          <InfoField name="已结算金额" v-if="formData.settleStatus === 'y'"
            >{{ formData.totalMoney }}元
          </InfoField>
        </div>
      </InfoBlock>

      <div class="flex justify-center">
        <el-button type="primary" v-if="!isView" @click="saveOrUpdate">确定</el-button>
        <el-button @click="router.back()">返回</el-button>
      </div>
    </div>
    <GoodsInfoOpen ref="goodsInfoRef" />
    <AddIndex ref="addIndexRef" @on-submit="changeList" />
  </div>
</template>
<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue'
import { tms_web } from '@/api/ccms'
import InfoBlock from '@/components/infoBlock/InfoBlock.vue'
import InfoField from '@/components/infoBlock/InfoField.vue'
import GoodsInfoOpen from '@/views/ccms/delivery/order/GoodsInfoOpen.vue'
import AddIndex from './AddIndex.vue'
import { ElForm } from 'element-plus'
import CustomTable from '@/components/customTable/Index.vue'
import { removeCommonField } from '@/utils/businessData'
import { dateFormatterByOptions } from '@/utils/formatTime'
import { cloneDeep } from 'lodash-es'

const emits = defineEmits<{
  (e: 'saveEvent'): void
}>()

const message = useMessage()
const route = useRoute()
const router = useRouter()
const goodsInfoRef = ref<typeof GoodsInfoOpen.prototype>()
const addIndexRef = ref<typeof AddIndex.prototype>()
const formValidate = ref<InstanceType<typeof ElForm>>()
const detailList = ref<any[]>([])

const formData = ref({
  id: '',
  totalMoney: 0,
  settleType: 'cash',
  settleStatus: 'n',
  orderId: ''
})

const orderInfo = ref<GetApiRes<typeof tms_web.transportOrder_get_post>>({} as any)
const info = ref<GetApiRes<typeof tms_web.staffCashSettlement_getStaffCashSettlement_post>>(
  {} as any
)
const orderList = ref<Required<GetApiResByList<typeof tms_web.transportOrder_query_post>>>(
  {} as any
)

const isView = computed(() => {
  return route.query.type === 'view'
})

const isAdd = computed(() => {
  return route.query.type === 'add'
})

const isUpdate = computed(() => {
  return route.query.type === 'update'
})

const rules: any = {
  orderId: [{ required: true, message: '请选择运单', trigger: 'change' }]
}

const deliveryColumns = [
  {
    title: '卸货地址',
    key: 'address',
    minWidth: 200,
    render(h: any, params: any) {
      return h(
        'span',
        `${params.row.province || ''}${params.row.city || ''}${params.row.county || ''}${params.row.address || ''}`
      )
    }
  },
  {
    title: '收货人',
    key: 'receiverName',
    minWidth: 130
  },
  {
    title: '收货人联系电话',
    key: 'receiverPhone',
    minWidth: 150
  },
  {
    title: '操作',
    render: (h: any, params: any) => {
      let btn: any[] = []
      btn.push(
        h('span', {
          domProps: {
            innerHTML: '商品信息'
          },
          class: 'cyan',
          on: {
            click: () => {
              goodsInfoRef.value?.open(params.row.goodsList)
            }
          }
        })
      )
      return h(
        'div',
        {
          class: 'handleBtn'
        },
        btn
      )
    }
  }
]

const detailColumns = [
  {
    title: '员工姓名',
    key: 'staffName',
    minWidth: 150
  },
  {
    title: '费用名称',
    key: 'expenseName',
    minWidth: 100
  },
  {
    title: '费用说明',
    key: 'expenseRemark',
    minWidth: 150
  },
  {
    title: '费用金额',
    key: 'expenseMoney',
    minWidth: 150
  },
  {
    title: '操作',
    key: 'option',
    width: 100,
    fixed: 'right',
    render: (h: any, params: any) => {
      let btn: any[] = []
      if (isAdd.value || isUpdate.value) {
        btn.push(
          h('span', {
            domProps: {
              innerHTML: '删除'
            },
            on: {
              click: () => {
                deleteData(h, params.row.expenseMoney)
              }
            }
          })
        )
      }
      return h(
        'div',
        {
          class: 'handleBtn'
        },
        btn
      )
    }
  }
]

//查询结算详情
function getInfo() {
  tms_web
    .staffCashSettlement_getStaffCashSettlement_post({
      data: {
        id: route.query.id as unknown as number
      }
    })
    .then((data) => {
      info.value = removeCommonField(data.data || {})
      if (info.value.settlement?.orderId) {
        getOrderInfo(info.value.settlement.orderId)
        formData.value.id = info.value.settlement.id as unknown as string
        formData.value.orderId = info.value.settlement.orderId as unknown as string
        formData.value.settleType = info.value.settlement.settleType + ''
        formData.value.settleStatus = info.value.settlement.settleStatus + ''
        formData.value.totalMoney = info.value.settlement.totalMoney || 0
      }
      if (info.value.staffVos) {
        for(const [index,item] of info.value.staffVos.entries()){
          info.value.staffVos[index] = removeCommonField(item)
        }
        detailList.value = info.value.staffVos
      }
    })
}

function queryOrderList() {
  tms_web
    .transportOrder_query_post({
      data: {
        pageNo: 1,
        pageSize: 999999,
        transportStatus: 'finish'
      }
    })
    .then((data) => {
      orderList.value = data?.data?.list || []
    })
}

//查询订单详情
function getOrderInfo(orderId: any) {
  tms_web
    .transportOrder_get_post({
      data: {
        id: orderId
      }
    })
    .then((data) => {
      orderInfo.value = removeCommonField(data.data || {})
    })
}

//删除员工数据
function deleteData(index: any, expenseMoney: any) {
  detailList.value.splice(index, 1)
  formData.value.totalMoney = formData.value.totalMoney - parseFloat(expenseMoney)
}

//新增或修改
function saveOrUpdate() {
  formValidate.value?.validate((valid: any) => {
    if (valid) {
      if (isAdd.value) {
        tms_web
          .staffCashSettlement_save_post({
            data: {
              ...formData.value,
              staffCashSettlementList: detailList.value
            } as any
          })
          .then(() => {
            message.success('操作成功！')
            router.back()
          })
      }
      if (isUpdate.value) {
        tms_web
          .staffCashSettlement_update_post({
            data: {
              ...formData.value,
              staffCashSettlementList: detailList.value
            } as any
          })
          .then(() => {
            message.success('操作成功！')
            emits('saveEvent')
          })
      }
    }
  })
}

//新增费用
function addExpense() {
  addIndexRef.value?.open()
}

//将新增的加入费用列表
function changeList() {
  let tmp: any = cloneDeep(addIndexRef.value)
  detailList.value.unshift(tmp.formData)
  formData.value.totalMoney = formData.value.totalMoney + parseFloat(tmp.formData.expenseMoney)
}

//变更运单时，调整显示的订单详情
function changeOrder(orderId: any) {
  for (const tmp of orderList.value) {
    if (tmp.id === orderId) {
      orderInfo.value = tmp as any
      break
    }
  }
}

onMounted(() => {
  if (route.query.type === 'view') {
    getInfo()
  }
  if (route.query.type === 'update') {
    queryOrderList()
    getInfo()
  }
  if (route.query.type === 'add') {
    queryOrderList()
  }
})
</script>

<style scoped lang="scss"></style>
