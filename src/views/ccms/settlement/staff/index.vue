<template>
  <!-- 列表 -->
  <ContentWrap class="full-height">
    <vxe-grid ref="gridRef" v-bind="gridOptions" v-on="gridEvents">
      <template #tools>
        <vxe-button @click="goPage('add')" status="primary" icon="vxe-icon-add" >新增结算单</vxe-button>
        <vxe-button @click="handelSearch" status="info" icon="vxe-icon-search" />
      </template>
      <template #operate="{ row }">
        <el-button type="primary" size="small" @click="goPage('view', row)" plain>详情</el-button>
        <el-button type="primary" size="small" v-hasPermi="['ccms:staffCashSettlement:update']" @click="goPage('update', row)" plain>编辑</el-button>
        <el-button
          type="danger"
          size="small"
          @click="handleDelete(row.id)"
          v-hasPermi="['ccms:staffCashSettlement:delete']"
          plain
        >删除
        </el-button>
      </template>
    </vxe-grid>
  </ContentWrap>

  <!-- 搜索抽屉 -->
  <SearchDrawer ref="searchRef" v-model="queryParams" @search="getList" />
</template>

<script setup lang="ts">
import type { VxeGridInstance, VxeGridListeners, VxeGridProps } from 'vxe-table'
import SearchDrawer from './SearchDrawer.vue'
import { tms_web } from '@/api/ccms'
import { cloneDeep } from 'lodash-es'
import { dateFormatterByOptions } from '@/utils/formatTime'

defineOptions({ name: 'SettlementStaff' })

const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  transportNumber: undefined
})

const searchRef = ref()
// 表格实例
const gridRef = ref<VxeGridInstance>()

// 调用vxe grid query方法重载表格
const getList = () => {
  if (gridRef.value) {
    gridRef.value.commitProxy('query') // 触发表格重新加载数据
  }
}
// 获取表格数据
const fetchApi = () => {
  return new Promise(async (resolve) => {
    const params: any = cloneDeep(queryParams)
    params.settleStatus = 'n'
    const res = await tms_web.staffCashSettlement_query_post({ data: params })
    gridOptions.data = res.data?.list || []
    resolve({
      page: {
        total: res.data?.total
      },
      result: res.data?.list
    })
  })
}

const gridOptions = reactive<
  VxeGridProps<GetApiResByListItem<typeof tms_web.staffCashSettlement_query_post>>
>({
  stripe: true,
  border: true,
  keepSource: true,
  height: '100%',
  columnConfig: {
    resizable: true
  },
  pagerConfig: {
    enabled: true,
    pageSize: 10
  },
  editConfig: {
    trigger: 'click',
    mode: 'row',
    showStatus: true
  },
  toolbarConfig: {
    zoom: true,
    custom: true,
    slots: {
      tools: 'tools'
    }
  },
  proxyConfig: {
    props: {
      result: 'result',
      total: 'page.total'
    },
    ajax: {
      // 接收 Promise
      query: ({ page }) => {
        queryParams.pageNo = page.currentPage
        queryParams.pageSize = page.pageSize
        return fetchApi()
      }
    }
  },
  columns: [
    { type: 'seq', width: 70 },
    {
      field: 'transportNumber',
      title: '运单单号',
      minWidth: '230px'
    },
    { field: 'customerName', title: '客户名称', minWidth: '130px' },
    {
      field: 'settlementMethod',
      title: '结算方式',
      minWidth: '160px',
      formatter: () => {
        return '到付'
      }
    },
    { field: 'totalMoney', title: '结算金额', minWidth: '130px' },
    {
      field: 'settleStatus',
      title: '结算状态',
      minWidth: '100px',
      formatter: ({ cellValue }) => {
        return cellValue === 'y' ? '已结算' : '未结算'
      }
    },
    { field: 'payTime', title: '结算时间', minWidth: '160px', formatter: dateFormatterByOptions },
    { title: '操作', width: '220px', slots: { default: 'operate' }, fixed: 'right' }
  ],
  data: []
})

const gridEvents: VxeGridListeners = {}

const handelSearch = () => {
  searchRef.value?.open()
}
const router = useRouter()
const goPage = (type: string, rowData?: Required<typeof gridOptions>['data'][number]) => {
  router.push({
    path: 'staff/info',
    query: {
      id: rowData?.id,
      type
    }
  })
}

const {t} = useI18n()
const message = useMessage()
/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await tms_web.staffCashSettlement_delete_post({ data: { id } })
    message.success(t('common.delSuccess'))
    // 刷新列表
    getList()
  } catch {}
}
</script>
