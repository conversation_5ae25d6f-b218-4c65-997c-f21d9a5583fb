<template>
  <!-- 列表 -->
  <ContentWrap class="full-height">
    <vxe-grid ref="gridRef" v-bind="gridOptions" v-on="gridEvents">
      <template #toolbar_buttons>
        <vxe-radio-group v-model="queryParams.dataType" @change="getList">
          <vxe-radio-button label="company">企业结算</vxe-radio-button>
          <vxe-radio-button label="driver">驾驶员结算</vxe-radio-button>
          <vxe-radio-button label="staff">员工现金结算</vxe-radio-button>
        </vxe-radio-group>
      </template>
      <template #tools>
        <vxe-button @click="handelSearch" status="info" icon="vxe-icon-search" />
      </template>
      <template #identityType="{ row }">
        <dict-tag :type="DICT_TYPE.IDENTITY_TYPE" :value="row.identityType" />
      </template>
      <template #operate="{ row }">
        <el-button type="primary" size="small" @click="toInfoPage(row)" plain>详情</el-button>
      </template>
    </vxe-grid>
  </ContentWrap>

  <!-- 搜索抽屉 -->
  <SearchDrawer ref="searchRef" v-model="queryParams" @search="getList" />
</template>

<script setup lang="ts">
import type { VxeGridInstance, VxeGridListeners, VxeGridProps } from 'vxe-table'
import SearchDrawer from './SearchDrawer.vue'
import { tms_web } from '@/api/ccms'
import { DICT_TYPE, getDictLabel } from '@/utils/dict'
import { cloneDeep } from 'lodash-es'
import { dateFormatterByOptions } from '@/utils/formatTime'

defineOptions({ name: 'SettlementHistory' })

const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  dataType: 'company',
  transportNumber: undefined,
  customerName: undefined,
  transportDriverName: undefined
})

const searchRef = ref()
// 表格实例
const gridRef = ref<VxeGridInstance>()

// 调用vxe grid query方法重载表格
const getList = () => {
  if (gridRef.value) {
    gridRef.value.commitProxy('query') // 触发表格重新加载数据
  }
}
// 获取表格数据
const fetchApi = () => {
  return new Promise(async (resolve) => {
    const params: any = cloneDeep(queryParams)
    delete params.dataType
    params.settleStatus = 'y'
    let res
    switch (queryParams.dataType) {
      case 'company': {
        res = await tms_web.customerWaybill_queryCustomerWaybill_post({ data: params })
        break
      }
      case 'driver': {
        res = await tms_web.customerWaybill_queryDriverOrders_post({ data: params })
        break
      }
      case 'staff': {
        res = await tms_web.staffCashSettlement_query_post({ data: params })
        break
      }
    }

    gridOptions.data = res.data?.list || []
    resolve({
      page: {
        total: res.data?.total
      },
      result: res.data?.list
    })
  })
}

const gridOptions = reactive<
  VxeGridProps<GetApiResByListItem<typeof tms_web.customerWaybill_queryCustomerWaybill_post>>
>({
  stripe: true,
  border: true,
  keepSource: true,
  height: '100%',
  columnConfig: {
    resizable: true
  },
  pagerConfig: {
    enabled: true,
    pageSize: 10
  },
  editConfig: {
    trigger: 'click',
    mode: 'row',
    showStatus: true
  },
  toolbarConfig: {
    zoom: true,
    custom: true,
    slots: {
      buttons: 'toolbar_buttons',
      tools: 'tools'
    }
  },
  proxyConfig: {
    props: {
      result: 'result',
      total: 'page.total'
    },
    ajax: {
      // 接收 Promise
      query: ({ page }) => {
        queryParams.pageNo = page.currentPage
        queryParams.pageSize = page.pageSize
        return fetchApi()
      }
    }
  },
  columns: [],
  data: []
})

const gridEvents: VxeGridListeners = {}

const leftColumns: any[] = [
  { type: 'seq', width: 70 },
  {
    field: 'transportNumber',
    title: '运单单号',
    minWidth: '230px'
  },
  { field: 'customerName', title: '客户名称', minWidth: '130px' }
]

const rightColumns: any[] = [
  {
    field: 'settlementMethod',
    title: '结算方式',
    minWidth: '160px',
    formatter: () => {
      return '到付'
    }
  },
  { field: 'totalMoney', title: '结算金额', minWidth: '130px' },
  {
    field: 'settleStatus',
    title: '结算状态',
    minWidth: '100px',
    formatter: ({ cellValue }) => {
      return cellValue === 'y' ? '已结算' : '未结算'
    }
  },
  { field: 'payTime', title: '结算时间', minWidth: '160px', formatter: dateFormatterByOptions },
  { title: '操作', width: '220px', slots: { default: 'operate' }, fixed: 'right' }
]

const executionCapacityColumns = {
  field: 'executionCapacity',
  title: '执行运力',
  minWidth: '250px',
  formatter: ({ row }) => {
    const capacityTypeDisName = getDictLabel(DICT_TYPE.ORDER_TRANSPORT_TYPE, row.capacityType)
    return `${row.transportDriverName || ''}(${row.transportCarNumber || ''})
        ${row.capacityType != undefined ? capacityTypeDisName + (row.carrierName ? '(' + row.carrierName + ')' : '') : ''}`
  }
}

watch(
  () => queryParams.dataType,
  () => {
    const resColumns = [...leftColumns]
    switch (queryParams.dataType) {
      case 'company': {
        resColumns.push({
          field: 'identityType',
          title: '结算对象类型',
          minWidth: '110px',
          slots: {
            default: 'identityType'
          }
        })
        resColumns.push(executionCapacityColumns)
        break
      }
      case 'driver': {
        resColumns.push(executionCapacityColumns)
        break
      }
      case 'staff': {
        break
      }
    }
    resColumns.push(...rightColumns)
    gridOptions.columns = resColumns
  },
  {
    immediate: true
  }
)

const handelSearch = () => {
  searchRef.value?.open()
}
const router = useRouter()
const toInfoPage = (rowData?: Required<typeof gridOptions>['data'][number]) => {
  let path = ''
  if (queryParams.dataType === 'company') {
    path = 'customer/info'
  }
  if (queryParams.dataType === 'driver') {
    path = 'driver/info'
  }
  if (queryParams.dataType === 'staff') {
    path = 'staff/info'
  }
  router.push({
    path,
    query: {
      id: rowData?.id,
      type: 'view'
    }
  })
}
</script>
