<template>
  <div class="flex flex-col gap-5">
    <div class="flex items-center gap-[10px] rounded-[10px] bg-white p-5 leading-none">
      <div class="text-[18px] font-bold text-black">{{ '结算单' }}详情</div>
      <div class="flex cursor-pointer items-center gap-[6px] text-[13px] text-[#A8ABB2]">
        <Icon icon="tdesign:rollback" />
        <span @click="router.back()">返回</span>
      </div>
    </div>
    <div class="flex flex-col gap-[30px] rounded-[10px] bg-white p-10">
      <InfoBlock name="客户信息">
        <div class="grid grid-cols-3">
          <InfoField name="客户名称" :text="info.customerName" />
          <InfoField
            name="下单时间"
            :text="dateFormatterByOptions({ cellValue: info.orderTime })"
          />
          <InfoField name="运单号" :text="info.transportNumber" />
        </div>
        <div class="grid grid-cols-3">
          <InfoField name="联系人" :text="info.sendName" />
          <InfoField name="联系电话" :text="info.sendPhone" />
        </div>
        <div class="grid grid-cols-3">
          <InfoField name="车辆类型" :text="info.carModelName" />
          <InfoField name="车辆规格"> {{ info.carLength || '' }}</InfoField>
        </div>
        <div class="grid grid-cols-3">
          <InfoField
            name="预约时间"
            :text="dateFormatterByOptions({ cellValue: info.loadingTime })"
          />
        </div>
        <div class="grid grid-cols-3">
          <InfoField name="装货地址">
            <span>
              {{
                `${info.sendProvince || ''}${info.sendCity || ''}${info.sendCounty || ''}${info.sendAddress || ''}`
              }}
            </span>
          </InfoField>
        </div>
      </InfoBlock>
      <InfoBlock name="配送信息">
        <custom-table
          :columns="deliveryColumns"
          :data="info.addressList || []"
          hide-paging
          :padding="0"
          show-index
        />
      </InfoBlock>
      <InfoBlock name="接单信息">
        <custom-table
          :columns="orderColumns"
          :data="orderList"
          hide-paging
          :padding="0"
          show-index
        />
      </InfoBlock>

      <InfoBlock name="结算信息">
        <div class="grid">
          <InfoField name="运单运费"> {{ info.totalMoney }}元</InfoField>
        </div>
        <div class="grid">
          <InfoField name="其它费用"> 0元</InfoField>
        </div>
        <div class="grid">
          <InfoField name="运单总额"> {{ info.totalMoney }}元</InfoField>
        </div>
        <div class="grid">
          <InfoField name="未结算金额"> {{ info.totalMoney }}元</InfoField>
        </div>
      </InfoBlock>

      <div class="flex justify-center">
        <el-button @click="router.back()">返回</el-button>
      </div>
    </div>
    <GoodsInfoOpen ref="goodsInfoRef" />
  </div>
</template>
<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue'
import { tms_web } from '@/api/ccms'
import InfoBlock from '@/components/infoBlock/InfoBlock.vue'
import InfoField from '@/components/infoBlock/InfoField.vue'
import GoodsInfoOpen from '@/views/ccms/delivery/order/GoodsInfoOpen.vue'
import CustomTable from '@/components/customTable/Index.vue'
import { removeCommonField } from '@/utils/businessData'
import { getDictLabel } from '@/utils/dict'
import { dateFormatterByOptions } from '@/utils/formatTime'

const route = useRoute()
const router = useRouter()

const info = ref<GetApiRes<typeof tms_web.transportOrder_get_post>>({} as any)

const goodsInfoRef = ref<typeof GoodsInfoOpen.prototype>()

const deliveryColumns = [
  {
    title: '卸货地址',
    key: 'address',
    minWidth: 200,
    render(h: any, params: any) {
      return h(
        'span',
        `${params.row.province || ''}${params.row.city || ''}${params.row.county || ''}${params.row.address || ''}`
      )
    }
  },
  {
    title: '收货人',
    key: 'receiverName',
    minWidth: 130
  },
  {
    title: '收货人联系电话',
    key: 'receiverPhone',
    minWidth: 150
  },
  {
    title: '操作',
    render: (h: any, params: any) => {
      console.log(params.row)
      let btn: any[] = []
      btn.push(
        h('span', {
          domProps: {
            innerHTML: '商品信息'
          },
          class: 'cyan',
          on: {
            click: () => {
              goodsInfoRef.value?.open(params.row.goodsList)
            }
          }
        })
      )
      return h(
        'div',
        {
          class: 'handleBtn'
        },
        btn
      )
    }
  }
]

const orderColumns = [
  {
    title: '时间',
    key: 'acceptTime',
    minWidth: 150,
    render(h: any, params: any): any {
      return h('span', dateFormatterByOptions({ cellValue: params.row.acceptTime }))
    }
  },
  {
    title: '接单状态',
    key: 'orderStatus',
    minWidth: 100,
    render(h: any, params: any) {
      return h('span', getDictLabel(DICT_TYPE.TRANSPORT_ORDER_STATUS, params.row.orderStatus))
    }
  },
  {
    title: '车牌号',
    key: 'transportCarNumber',
    minWidth: 150
  },
  {
    title: '车辆类型',
    key: 'transportCarType',
    minWidth: 150,
    render(h: any, params: any) {
      return h('span', getDictLabel(DICT_TYPE.CAR_TYPE, params.row.transportCarType))
    }
  },
  {
    title: '车辆规格',
    key: 'transportCarLength',
    minWidth: 150,
    render(h: any, params: any): any {
      return h('span', params.row.transportCarLength ? params.row.transportCarLength : '-')
    }
  },
  {
    title: '驾驶员',
    key: 'transportDriverName',
    minWidth: 150
  },
  {
    title: '联系电话',
    key: 'transportDriverPhone',
    minWidth: 150
  }
]

const orderList = computed(() => {
  return [info.value]
})

function getInfo() {
  tms_web
    .transportOrder_get_post({
      data: {
        id: route.query.id as unknown as number
      }
    })
    .then((data) => {
      info.value = removeCommonField(data.data || {})
    })
}

onMounted(() => {
  getInfo()
})
</script>

<style scoped lang="scss"></style>
