<template>
  <div>
    <el-calendar>
      <template #date-cell="{ data }">
        <div :class="data.isSelected ? 'is-selected' : ''" class="center">
          {{ data.day.split('-').slice(2).join('-') }}
        </div>
        <div class="sign-center selected" v-if="checkSign(data.day)">
          {{ '已签到' }}
        </div>
        <div class="sign-center" v-if="!checkSign(data.day)">
          {{ '未签到' }}
        </div>
      </template>
      <!-- <template
        slot="dateCell"
        slot-scope="{date, data}">

      </template> -->
    </el-calendar>
    <!--    <el-calendar v-model="currentDate" :picker-options="pickerOptions">-->
    <!--      &lt;!&ndash; 自定义日期单元格内的文本 &ndash;&gt;-->
    <!--      <template #day="{ date, day }">-->
    <!--        <div class="custom-day-content">-->
    <!--          <span class="day-number">{{ day.day }}</span>-->
    <!--          <span class="custom-text">{{ getCustomText(date) }}</span>-->
    <!--        </div>-->
    <!--      </template>-->
    <!--    </el-calendar>-->
    <div class="flex justify-center">
      <el-button @click="router.back()">返回</el-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue'
import { tms_web } from '@/api/ccms'
import { dateFormatter2ByOptions } from '@/utils/formatTime'
defineOptions({ name: 'DriverSignInfoPage' })

const route = useRoute()
const router = useRouter()

const info = ref([])
const data: any = {}
const date: any = {}

onMounted(() => {
  getInfo()
})

//查询签到记录列表
function getInfo() {
  tms_web
    .accidentFaultDamageReport_queryDriverSign_post({
      data: {
        driverName: route.query.driverName,
        carNumber: route.query.carNumber
      }
    })
    .then((data) => {
      info.value = data.data?.list || []
    })
}

// 获取签到过的时间列表
const signTime = computed(() => {
  return info.value.map((item: any) => {
    return item.signTime != undefined ? dateFormatter2ByOptions({ cellValue: item.signTime }) : ''
  })
})

// 校验是不是签到过的时间
function checkSign(date: any) {
  var time = dateFormatter2ByOptions({ cellValue: date })
  const exists = signTime.value.some((value:any) => value === time)
  return exists
}
</script>

<style scoped lang="scss">
.is-selected {
  color: #1989fa;
}

.center {
  height: 40%;
  left: 30%;
  transform: translate(45%, 50%);
}

.sign-center {
  height: 40%;
  left: 40%;
  transform: translate(39%, 50%);
}

.selected {
  color: #00b386;
}
</style>
