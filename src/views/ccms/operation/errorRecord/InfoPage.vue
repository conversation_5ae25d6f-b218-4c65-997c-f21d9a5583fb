<template>
  <div class="flex flex-col gap-5">
    <div class="flex items-center gap-[10px] rounded-[10px] bg-white p-5 leading-none">
      <div class="text-[18px] font-bold text-black">
        {{ '异常记录详情' }}
      </div>
      <div class="flex cursor-pointer items-center gap-[6px] text-[13px] text-[#A8ABB2]">
        <Icon icon="tdesign:rollback" />
        <span @click="router.back()">返回</span>
      </div>
    </div>
    <div class="flex flex-col gap-[30px] rounded-[10px] bg-white p-10">
      <InfoBlock name="异常详情">
        <div class="grid grid-cols-3">
          <InfoField name="车牌号码" :text="info.carNumber" />
          <InfoField name="资产ID" :text="info.assetsId" />
        </div>
        <div class="grid grid-cols-3">
          <InfoField name="客户名称" :text="info.customerName" />
          <InfoField name="驾驶员姓名" :text="info.transportDriverName" />
        </div>
        <div class="grid grid-cols-3">
          <InfoField name="联系电话" :text="info.transportDriverPhone" />
          <InfoField name="记录时间" :text="info.recordStartTime" />
        </div>
        <div class="grid grid-cols-3">
          <InfoField name="持续时间(h.m.s)" :text="info.duration" />
          <InfoField name="异常类型" :text="getDictLabel('error_type',info.errorType)" />
        </div>
      </InfoBlock>

      <div class="flex justify-center">
        <el-button @click="router.back()">返回</el-button>
      </div> 
    </div>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import { tms_web } from '@/api/ccms'
//   import { overwriteEnumDeep } from "@/util";
import InfoBlock from '@/components/infoBlock/InfoBlock.vue'
import InfoField from '@/components/infoBlock/InfoField.vue'
//   import { useEnums } from "@/hooks/useEnums";
import { getDictLabel } from '@/utils/dict'

const route = useRoute()
const router = useRouter()

const info: any = ref({})

function getInfo() {
  tms_web
    .transportOrderErrorRecord_get_post({
      data: {
        id: route.query.id
      }
    })
    .then((data) => {
      info.value = data.data || {}
      //计算时分秒
      let res = 0
      let endTime: any = 0
      let startTime: any = 0
      if (info.value.recordEndTime) {
        startTime = new Date(info.value.recordStartTime)
        endTime = new Date(info.value.recordEndTime)
      } else {
        endTime = new Date()
      }
      res = endTime - startTime
      const seconds = Math.floor(res / 1000)
      const minutes = Math.floor(seconds / 60)
      const hours = Math.floor(minutes / 60)
      const tmp = `${(hours / 24) * 24 + (hours % 24)}h${minutes % 60}m${seconds % 60}s`
      info.value.duration = tmp
    })
}

onMounted(() => {
  getInfo()
})

//   const $notify = useNotify();
</script>

<style scoped lang="scss">
.not-require {
  left: 1000px;
}
</style>
