<template>
    <!-- 列表 -->
    <ContentWrap class="full-height">
      <vxe-grid ref="gridRef" v-bind="gridOptions" v-on="gridEvents">
        <template #type="{ row }">
          <DictTag :type="DICT_TYPE.ERROR_TYPE" :value="row.errorType" />
        </template>
        <template #status="{ row }">
          <DictTag :type="DICT_TYPE.INSURE_STATUS" :value="row.status" />
        </template>
        <template #operate="{ row }">
          <el-button
            type="primary"
            size="small"
            @click="goPage('view', row.id)"
            v-hasPermi="['ccms:transportOrderErrorRecord:get']"
            plain
            >详情</el-button
          >
          <!-- <el-button type="danger" size="small" @click="handleDelete(row.id)" v-hasPermi="['wms:contract-message:delete']"
            plain>删除</el-button> -->
        </template>
      </vxe-grid>
    </ContentWrap>
  
    <!-- 表单弹窗：添加/修改 -->
    <ContractMessageForm ref="formRef" @success="getList" />
    <!-- 搜索抽屉 -->
    <SearchDrawer ref="searchRef" v-model="queryParams" @search="getList" />
  </template>
  
  <script setup lang="ts">
  import { dateFormatterByOptions } from '@/utils/formatTime'
  // import download from '@/utils/download'
//   import ContractMessageForm from './ContractMessageForm.vue'
  import DictTag from '@/components/DictTag/src/DictTag.vue'
  import { DICT_TYPE } from '@/utils/dict'
  import { tms_web } from '@/api/ccms'
  import type { VxeGridProps, VxeGridListeners, VxeGridInstance } from 'vxe-table'
  import SearchDrawer from './SearchDrawer.vue'
import router from '@/router'
  // import { rowContextKey } from 'element-plus';
  // import { values } from 'lodash-es';
  
  defineOptions({ name: 'ErrorRecord' })
  
  const message = useMessage() // 消息弹窗
  const { t } = useI18n() // 国际化
  
  const queryParams = reactive({
    pageNo: 1,
    pageSize: 10
  })
  
  const searchRef = ref()
  // 表格实例
  const gridRef = ref<VxeGridInstance>()
  
  // 调用vxe grid query方法重载表格
  const getList = (item:any) => {
    Object.assign(queryParams,{...queryParams,...item})
    if (gridRef.value) {
      gridRef.value.commitProxy('query') // 触发表格重新加载数据
    }
  }
  // 获取表格数据
  const fetchApi = () => {
    return new Promise(async (resolve) => {
      // ContractMessageApi.getContractMessagePage(queryParams)
      // const res = await ContractMessageApi.getContractMessagePage(queryParams)
      const res = await tms_web.transportOrderErrorRecord_query_post({
        data: {
          ...queryParams
        }
      })
      console.log(res, '异常List')
      res.data?.list.forEach((item) => {
      let ret = 0
      let endTime: any = 0
      let startTime: any = 0
      if (item.recordEndTime) {
        startTime = new Date(item.recordStartTime)
        endTime = new Date(item.recordEndTime)
      } else {
        endTime = new Date()
      }
      ret = endTime - startTime
      const seconds = Math.floor(ret / 1000)
      const minutes = Math.floor(seconds / 60)
      const hours = Math.floor(minutes / 60)
      item.duration = `${(hours / 24) * 24 + (hours % 24)}h${minutes % 60}m${seconds % 60}s`
      return item
    })
      // gridOptions.data = res.data.list
  
      resolve({
        page: {
          total: res.data?.total
        },
        result: res.data?.list
      })
    })
  }
  function goPage(view: string, id?: string) {
  let path = 'errorRecord/InfoPage'
  let query: any = {}
  query.view = view;
  query.id = id;
  router.push({
    path: path,
    query: {
      ...query
    }
  })
}
  
  const gridOptions = reactive({
    stripe: true,
    border: true,
    keepSource: true,
    height: '100%',
    columnConfig: {
      resizable: true
    },
    pagerConfig: {
      enabled: true,
      pageSize: 10
    },
    editConfig: {
      trigger: 'click',
      mode: 'row',
      showStatus: true
    },
    toolbarConfig: {
      zoom: true,
      custom: true,
      // slots: {
      //   buttons: 'toolbar_buttons'
      // },
      tools: [
        { name: '', code: 'search', icon: 'vxe-icon-search', status: 'info' },
        // { name: '', code: 'customExport', icon: 'vxe-table-icon-download', status: 'info' }
      ]
    },
    proxyConfig: {
      props: {
        result: 'result',
        total: 'page.total'
      },
      ajax: {
        // 接收 Promise
        query: ({ page }) => {
          queryParams.pageNo = page.currentPage
          queryParams.pageSize = page.pageSize
          return fetchApi()
        },
        // body 对象： { removeRecords }
      }
    },
    columns: [
      { type: 'seq', width: 70 },
      { field: 'carNumber', title: '车牌号码', minWidth: '100px' },
      { field: 'assetsId', title: '资产ID', minWidth: '100px' },
      { field: 'customerName', title: '客户名称', minWidth: '100px' },
      { field: 'transportDriverName', title: '驾驶员姓名', minWidth: '100px' },
      { field: 'transportDriverPhone', title: '联系电话', minWidth: '100px' },
    //   { field: 'carMasterName', title: '记录时间', minWidth: '110px' },
    //   { field: 'phone', title: '手机号码', minWidth: '110px' },
      // { field: 'contractPeriod', title: '合同期限', minWidth: '100px' },
      {
        field: 'recordStartTime',
        title: '记录时间',
        minWidth: '120px',
        formatter: dateFormatterByOptions
      },
      {
        field: 'duration',
        title: '持续时间(h.m.s)',
        minWidth: '100px',
      },
      { field: 'errorType', title: '异常类型', minWidth: '100px', slots: { default: 'type' } },
      // { field: 'hctdName', title: '招商专员', minWidth: '120px' },
      //   { field: 'effectiveDate', title: '合同生效时间', minWidth: '200px',formatter:dateFormatter2 },
      //   { field: 'expirationDay', title: '合同失效时间', minWidth: '200px',formatter:dateFormatter2 },
      //   { field: 'renewalTime', title: '合同续签时间', minWidth: '200px', formatter: dateFormatter2 },
      // { field: 'originalContractNo', title: '原合同编号', minWidth: '200px' },
      // { field: 'status', title: '合同状态', minWidth: '100px' },
      { title: '操作', width: 200, slots: { default: 'operate' } }
    ],
    data: []
  })
  
  const gridEvents: VxeGridListeners = {
    toolbarButtonClick(params) {
      console.log(params.code)
    },
  
    toolbarToolClick({ code }) {
      // console.log(123, code, tool)
      switch (code) {
        case 'search':
          handelSearch()
          break
        case 'add':
          openForm()
          break
        case 'del':
          if (gridRef.value) {
            gridRef.value.commitProxy('delete')
          }
          break
        case 'customExport':
          console.log('自定义导出')
          break
      }
    }
  }
  
  const handelSearch = () => {
    searchRef.value?.open()
  }
  
  /** 添加/修改操作 */
  const formRef = ref()
  const openForm = (id?: number) => {
    // console.log(formRef.value)
  
    formRef.value.open(id)
  }
  
  </script>
  