<template>
  <el-drawer v-model="drawerVisible" title="搜索" direction="rtl" size="350px">
    <template #header>
      <span class="text-16px font-700">搜索</span>
    </template>
    <div class="search-drawer" style="height: 100%">
      <el-form
        ref="queryFormRef"
        :inline="true"
        :model="queryParams"
        class="-mb-15px"
        label-width="68px"
      >
        <el-form-item label="车牌号码" prop="carNumber">
          <el-input
            v-model="queryParams.carNumber"
            placeholder="请输入车牌号码"
            clearable
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="资产ID" prop="assetsId">
          <el-input
            v-model="queryParams.assetsId"
            placeholder="请输入资产ID"
            clearable
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="所属客户" prop="customerId">
          <el-select
            v-model="queryParams.customerId"
            placeholder="请选择"
            clearable
            class="!w-240px"
          >
            <el-option
              v-for="(item, index) in customerList"
              :label="item.name"
              :value="item.id"
              :key="index"
            />
          </el-select>
        </el-form-item>
       
        <!-- <el-form-item label="申请时间" prop="signDate">
          <el-date-picker
            v-model="dataTimeList"
            @change="dataTimeFn"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-240px"
          />
        </el-form-item> -->
      </el-form>
    </div>
    <template #footer>
      <div style="flex: auto">
        <el-button @click="reset()">重置</el-button>
        <el-button type="primary" @click="search">搜索</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { tms_web } from '@/api/ccms'

// 定义 props 和 emits
const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  }
})
onMounted(() => {
  queryCustomer()
})
const customerList: any = ref([])

const loading = ref(false)
function queryCustomer() {
  loading.value = true
  tms_web
    .customer_query_post({
      data: {}
    })
    .then((res) => {
      customerList.value = res.data?.list || []
    })
    .finally(() => {
      loading.value = false
    })
}
// 时间段数组
const dataTimeList = ref([])
// 处理时间函数
const emit = defineEmits(['update:modelValue', 'search', 'reset'])

// 控制抽屉显示
const drawerVisible = ref(false)
interface queryParams {
  carNumber: any
  assetsId: any
  customerId: any
  status: any
}
// 使用 computed 实现双向绑定
const queryParams = ref({
  carNumber: undefined,
  assetsId: undefined,
  customerId: undefined,
  status: undefined,
})

// 打开抽屉
function open() {
  drawerVisible.value = true
}

// 关闭抽屉
function close() {
  drawerVisible.value = false
}

// 搜索
function search() {
  close()
  // console.log(queryParams.value);
  emit('search', queryParams.value)
}

const queryFormRef = ref()
// 重置
function reset() {
  dataTimeList.value.length = 0
  queryFormRef.value?.resetFields()
  emit('reset', queryParams.value)
}

// 暴露 open 方法
defineExpose({
  open
})
</script>

<style lang="scss" scoped>
.search-drawer {
  ::v-deep {
    .el-form {
      height: 98%;
    }
  }
}
</style>
