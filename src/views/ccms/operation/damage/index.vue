<template>
    <!-- 列表 -->
    <ContentWrap class="full-height">
      <vxe-grid ref="gridRef" v-bind="gridOptions" v-on="gridEvents">
        <template #toolbar_buttons>
          <VxeRadioGroup v-model="queryParams.status" @change="getList">
            <VxeRadioButton label="all" content="全部" />
            <VxeRadioButton label="not" content="未处理" />
            <VxeRadioButton label="next" content="续报" />
            <VxeRadioButton label="finish" content="完结" />
          </VxeRadioGroup>
          <!-- <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
                <el-tab-pane label="全部" name="all" />
                <el-tab-pane label="未处理" name="not" />
                <el-tab-pane label="已处理" name="yes" />
              </el-tabs> -->
        </template>
        <template #status="{ row }">
          <DictTag :type="DICT_TYPE.DEAL_REPORT_STATUS" :value="row.status" />
        </template>
        <template #level="{ row }">
          <DictTag :type="DICT_TYPE.DAMAGE_LEVEL" :value="row.damageLevel" />
        </template>
        <template #operate="{ row }">
          <el-button
            type="primary"
            size="small"
            @click="goPage('view', row.id)"
            v-hasPermi="['ccms:accidentFaultDamageReport:get']"
            plain
            >详情</el-button
          >
          <el-button
            type="danger"
            size="small"
            v-if="row.status !== 'finish'"
            @click="goPage('deal', row.id)"
            v-hasPermi="['ccms:accidentFaultDamageReport:exceptionHandling']"
            plain
            >处理</el-button
          >
        </template>
      </vxe-grid>
    </ContentWrap>
    <!-- <DealForm ref="dealRef" @success="getList" /> -->
  
    <!-- 表单弹窗：添加/修改 -->
    <!-- <ContractMessageForm ref="imageRef" @success="getList" /> -->
    <!-- 搜索抽屉 -->
    <SearchDrawer ref="searchRef" v-model="queryParams" @search="getList" />
  </template>
  
  <script setup lang="ts">
  import { dateFormatterByOptions } from '@/utils/formatTime'
  // import download from '@/utils/download'
  // import ContractMessageForm from './ContractMessageForm.vue'
  import DictTag from '@/components/DictTag/src/DictTag.vue'
  import { DICT_TYPE } from '@/utils/dict'
  import { tms_web } from '@/api/ccms'
  import type { VxeGridProps, VxeGridListeners, VxeGridInstance } from 'vxe-table'
  import SearchDrawer from './SearchDrawer.vue'
  // import DealForm from './DealForm.vue'
  // import { rowContextKey } from 'element-plus';
  // import { values } from 'lodash-es';
  import type { TabsPaneContext } from 'element-plus'
  import router from '@/router'
  defineOptions({ name: 'DamageVue' })
  const message = useMessage() // 消息弹窗
  const { t } = useI18n() // 国际化
  interface queryParams {
    status?: string
    pageNo: number // 可选属性
    pageSize: number
  }
  const queryParams = reactive<queryParams>({
    pageNo: 1,
    status: 'all',
    pageSize: 10
  })
  onActivated(()=>{
    getList()
  }) 
  const searchRef = ref()
  // 表格实例
  const gridRef = ref<VxeGridInstance>()
  
  // 调用vxe grid query方法重载表格
  const getList = (item: any={}) => {
    Object.assign(queryParams, { ...queryParams, ...item })
    if (gridRef.value) {
      gridRef.value.commitProxy('query') // 触发表格重新加载数据
    }
  }
  // 获取表格数据
  const fetchApi = () => {
    return new Promise(async (resolve) => {
      const params = { ...queryParams }
      if (params.status === 'all') {
        delete params.status
      }
      const res = await tms_web.accidentFaultDamageReport_queryDamageDeclaration_post({
        data: {
          ...params
        }
      })
      // console.log(res, '货损List')
      resolve({
        page: {
          total: res.data?.total
        },
        result: res.data?.list
      })
    })
  }
  
  const gridOptions = reactive({
    stripe: true,
    border: true,
    keepSource: true,
    height: '100%',
    columnConfig: {
      resizable: true
    },
    pagerConfig: {
      enabled: true,
      pageSize: 10
    },
    editConfig: {
      trigger: 'click',
      mode: 'row',
      showStatus: true
    },
    toolbarConfig: {
      zoom: true,
      custom: true,
      slots: {
        buttons: 'toolbar_buttons'
      },
      tools: [
        { name: '', code: 'search', icon: 'vxe-icon-search', status: 'info' }
        // { name: '', code: 'customExport', icon: 'vxe-table-icon-download', status: 'info' }
      ]
    },
    proxyConfig: {
      props: {
        result: 'result',
        total: 'page.total'
      },
      ajax: {
        // 接收 Promise
        query: ({ page }) => {
          queryParams.pageNo = page.currentPage
          queryParams.pageSize = page.pageSize
          return fetchApi()
        }
        // body 对象： { removeRecords }
      }
    },
    columns: [
      { type: 'seq', width: 70 },
      { field: 'transportNumber', title: '运单号', minWidth: '180px' },
      {
        field: 'declareTime',
        title: '申报时间',
        minWidth: '120px',
        formatter: dateFormatterByOptions
      },
      //   { field: 'accidentAddress', title: '事故地点', minWidth: '100px' },
      { field: 'damageLevel', title: '货损等级', minWidth: '100px', slots: { default: 'level' } },
      { field: 'accidentCarNumber', title: '事故车辆', minWidth: '100px' },
      { field: 'driverName', title: '驾驶员姓名', minWidth: '100px' },
      { field: 'driverPhone', title: '联系电话', minWidth: '110px' },
      { field: 'status', title: '状态', minWidth: '100px', slots: { default: 'status' } },
      // { field: 'contractPeriod', title: '合同期限', minWidth: '100px' },
      // { field: 'dealRemark', title: '处理意见', minWidth: '100px' },
      // { field: 'hctdName', title: '招商专员', minWidth: '120px' },
      // { field: 'originalContractNo', title: '原合同编号', minWidth: '200px' },
      // { field: 'status', title: '合同状态', minWidth: '100px' },
      { title: '操作', width: 200, slots: { default: 'operate' } }
    ],
    data: []
  })
  
  const gridEvents: VxeGridListeners = {
    toolbarButtonClick(params) {
      console.log(params.code)
    },
  
    toolbarToolClick({ code }) {
      // console.log(123, code, tool)
      switch (code) {
        case 'search':
          handelSearch()
          break
        case 'add':
          openForm()
          break
        case 'del':
          if (gridRef.value) {
            gridRef.value.commitProxy('delete')
          }
          break
        case 'customExport':
          console.log('自定义导出')
          break
      }
    }
  }
  
  const handelSearch = () => {
    searchRef.value?.open()
  }
  
  /** 添加/修改操作 */
  const imageRef = ref()
  const openForm = (id?: number) => {
    // console.log(imageRef.value)
    imageRef.value.open(id)
  }
  function goPage(type: string, id?: string) {
    let path = ''
    let query: any = {}
    if (type === 'view') {
      path = 'damage/InfoPage'
      query.id = id
    }
    if (type === 'deal') {
      path = 'damage/InfoPage'
      query.id = id
    }
    router.push({
      path: path,
      query: {
        type: type,
        ...query
      }
    })
  }
  
  /** 导出按钮操作 */
  // const handleExport = async () => {
  //   try {
  //     // 导出的二次确认
  //     await message.exportConfirm()
  //     // 发起导出
  //     const data = await ContractMessageApi.exportContractMessage(queryParams)
  //     download.excel(data, '合同信息.xls')
  //   } catch {
  //   } finally {
  //   }
  // }
  </script>
  <style scoped lang="scss">
  :deep(.el-tabs__header) {
    margin-bottom: 0px;
  }
  :deep(.el-tabs__nav-wrap:after) {
    background-color: rgba($color: #000000, $alpha: 0);
  }
  </style>
  