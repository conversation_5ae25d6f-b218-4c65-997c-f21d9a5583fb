<template>
  <el-drawer v-model="drawerVisible" title="搜索" direction="rtl" size="350px">
    <template #header>
      <span class="text-16px font-700">搜索</span>
    </template>
    <div class="search-drawer" style="height: 100%">
      <el-form
        ref="queryFormRef"
        :inline="true"
        :model="queryParams"
        class="-mb-15px"
        label-width="68px"
      >
        <el-form-item label="运单号" prop="transportNumber">
          <el-input
            v-model="queryParams.transportNumber"
            placeholder="请输入"
            clearable
            class="!w-240px"
          />
        </el-form-item>

        <el-form-item label="事故等级" prop="accidentLevel">
          <el-select
            v-model="queryParams.accidentLevel"
            placeholder="请选择"
            clearable
            class="!w-240px"
          >
            <el-option
              v-for="item in levelList"
              :label="item.label"
              :value="item.value"
              :key="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="事故地点" prop="accidentAddress">
          <el-input
            v-model="queryParams.accidentAddress"
            placeholder="请输入"
            clearable
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="事故车辆" prop="accidentCarNumber">
          <el-input
            v-model="queryParams.accidentCarNumber"
            placeholder="请输入"
            clearable
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="上报时间" prop="dataTimeList">
          <el-date-picker
            v-model="dataTimeList"
            @change="dataTimeFn"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-240px"
          />
        </el-form-item>
        <!-- <el-form-item label="联系人电话" prop="phone">
          <el-input v-model="queryParams.phone" placeholder="请输入联系人电话" clearable class="!w-240px" />
        </el-form-item> -->
      </el-form>
    </div>
    <template #footer>
      <div style="flex: auto">
        <el-button @click="reset()">重置</el-button>
        <el-button type="primary" @click="search">搜索</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { getStrDictOptions } from '@/utils/dict'

const levelList = ref(getStrDictOptions('accident_level'))

// 定义 props 和 emits
const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update:modelValue', 'search', 'reset'])
// 时间段数组
const dataTimeList = ref([])
// 处理时间函数
const dataTimeFn = (val: any) => {
  queryParams.value.startTime = val ? val[0] : ''
  queryParams.value.endTime = val ? val[1] : ''
}
// 控制抽屉显示
const drawerVisible = ref(false)
interface queryParams {
  transportNumber: any
  accidentLevel: any
  accidentAddress: any
  startTime: any
  endTime: any
  accidentCarNumber: any
}
// 使用 computed 实现双向绑定
const queryParams = ref({
  transportNumber: undefined,
  accidentLevel: undefined,
  accidentAddress: undefined,
  startTime: '',
  endTime: '',
  accidentCarNumber: undefined
})

// 打开抽屉
function open() {
  drawerVisible.value = true
}

// 关闭抽屉
function close() {
  drawerVisible.value = false
}

// 搜索
function search() {
  close()
  // console.log(queryParams.value);
  emit('search', queryParams.value)
}

const queryFormRef = ref()
// 重置
function reset() {
  queryFormRef.value?.resetFields()
  dataTimeList.value.length = 0
  queryParams.value.startTime = ''
  queryParams.value.endTime = ''
  emit('reset', queryParams.value)
}

// 暴露 open 方法
defineExpose({
  open
})
</script>

<style lang="scss" scoped>
.search-drawer {
  ::v-deep {
    .el-form {
      height: 98%;
    }
  }
}
</style>
