<template>
  <div class="flex flex-col gap-5">
    <div class="flex items-center gap-[10px] rounded-[10px] bg-white p-5 leading-none">
      <div class="text-[18px] font-bold text-black">
        {{ isView ? '' : '' }}{{ isDeal ? '' : '' }}
      </div>
      <div class="flex cursor-pointer items-center gap-[6px] text-[13px] text-[#A8ABB2]">
        <Icon icon="tdesign:rollback" />
        <span @click="router.back()">返回</span>
      </div>
    </div>
    <div class="flex flex-col gap-[30px] rounded-[10px] bg-white p-10">
      <InfoBlock name="事故描述" v-if="isDeal">
        <el-form :model="formData" :rules="rules" ref="formValidateRef">
          <el-form-item label="异常类型：" prop="reportType" class="is-required">
            <el-input
              v-model="reportTypeName"
              style="width: 250px"
              size="large"
              disabled
            />
          </el-form-item>
          <el-form-item
            label="处理结果："
            prop="status"
            :rules="[
              {
                required: true,
                message: '请选择审核结果',
                trigger: 'change'
              }
            ]"
          >
            <el-radio-group v-model="formData.status">
              <el-radio label="finish">完结</el-radio>
              <el-radio label="next">续报</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </InfoBlock>
      <InfoBlock name="故障详情">
        <div class="grid grid-cols-3">
          <InfoField name="事故等级" :text="info.accidentLevel">
            <template #default>
              <DictTag :type="DICT_TYPE.ACCIDENT_LEVEL" :value="info.accidentLevel" />
            </template>
          </InfoField>

          <InfoField name="事故形态" :text="info.accidentForm" />
        </div>
        <div class="grid grid-cols-3">
          <InfoField name="事故发生时间" :text="dateFormatterByOptions({cellValue:info.accidentTime})" />
          <InfoField name="事故发生地点" :text="info.accidentAddress" />
        </div>
        <div class="grid grid-cols-3">
          <InfoField name="天气情况" :text="info.weatherCondition" />
          <InfoField name="事故路段公路技术等级" :text="info.accidentHighwayItLevel" >
            <template #default>
              <DictTag :type="DICT_TYPE.ACCIDENT_HIGHWAY_IT_LEAVE" :value="info.accidentHighwayItLevel" />
            </template>
          </InfoField>
        </div>
        <div class="grid grid-cols-3">
          <InfoField name="事故路段公路行政等级" :text="info.accidentHighwayGovLevel">
            <template #default>
              <DictTag :type="DICT_TYPE.ACCIDENT_HIGHWAY_GOV_LEVEL" :value="info.accidentHighwayGovLevel" />
            </template>
          </InfoField>
          <InfoField name="事故路段线性状况" :text="info.accidentHighwaySection">
            <template #default>
              <DictTag :type="DICT_TYPE.ACCIDENT_HIGHWAY_SECTION" :value="info.accidentHighwaySection" />
            </template>
          </InfoField>
        </div>
        <div class="grid grid-cols-3">
          <InfoField name="事故直接原因" :text="info.accidentReason" />
          <InfoField name="车牌号" :text="info.accidentCarNumber" />
        </div>
        <div class="grid grid-cols-3">
          <InfoField name="车辆类型" :text="info.accidentCarType" />
          <InfoField name="车辆规格" :text="info.accidentCarModel" />
        </div>
        <div class="grid grid-cols-3">
          <InfoField name="主要货物" :text="info.mainGoods" />
          <InfoField name="驾驶员姓名" :text="info.driverName" />
        </div>
        <div class="grid grid-cols-3">
          <InfoField name="驾驶证号码" :text="info.driverNumber" />
          <InfoField name="事故初步原因分析" :text="info.accidentReasonAnalysis" />
        </div>
      </InfoBlock>
      <InfoBlock name="现场照片">
        <div
          class="grid grid-cols-[repeat(auto-fill,_132px)] justify-between gap-5 overflow-hidden"
        >
          <div
            v-for="(file, index) in fileInfo"
            :key="file.id"
            class="flex flex-col items-center gap-4 overflow-hidden"
          >
            <el-image
              class="size-[132px] rounded"
              :src="imgFilePath[index]"
              fit="cover"
              :preview-src-list="imgFilePath"
              :initial-index="index"
            />
            <div class="w-[122px] truncate text-center text-[12px] text-[#33333]">
              {{ file.name }}
            </div>
          </div>
        </div>
      </InfoBlock>
      <InfoBlock name="处理详情" v-if="isView">
        <div class="grid grid-cols-3">
          <InfoField name="处理时间" :text="dateFormatterByOptions({cellValue:info.dealTime})" />
          <InfoField name="处理账号" :text="info.processorsName" />
        </div>
        <div class="grid grid-cols-1">
          <InfoField name="处理内容" :text="info.content" />
        </div>
      </InfoBlock>
      <InfoBlock name="事故描述" v-if="isDeal">
        <el-form :model="formData" :rules="rules" ref="formValidateRef">
          <el-form-item label="" prop="faultRemark">
            <el-input
              type="textarea"
              v-model="info.faultRemark"
              size="large"
              disabled
            />
          </el-form-item>
          <el-form-item label="异常处理：" class="is-required">
            <el-select
              class="dealType"
              style="width: 250px"
              v-model="formData.dealType"
              placeholder="请选择"
              clearable
              filterable
            >
              <el-option
                v-for="(item, index) in dealTypeEnum"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="运费金额：" prop="freightMoney" class="is-required">
            <el-input
              v-model="formData.freightMoney"
              style="width: 250px"
              size="large"
              placeholder="请输入"
              disabled
            />
            元
          </el-form-item>
          <el-form-item label="减免金额：" prop="reductionMoney">
            <el-input
              v-model="formData.reductionMoney"
              @change="changeFreightMoney"
              style="width: 250px"
              size="large"
              placeholder="请输入减免金额"
            />
            元
          </el-form-item>
          <el-form-item label="最终金额：" prop="endMoney" class="is-required">
            <el-input
              v-model="formData.endMoney"
              style="width: 250px"
              size="large"
              disabled
            />
            元
          </el-form-item>
        </el-form>
      </InfoBlock>
      <div class="flex justify-center">
        <el-button type="primary" @click="save" v-hasPermi="['ccms:accidentFaultDamageReport:exceptionHandling']" v-if="isDeal">保存</el-button>
        <el-button @click="router.back()">返回</el-button>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue'
import { tms_web } from '@/api/ccms'
//   import { useEnums as useEnumsStore, useNotify, useRoute, useRouter } from "@/util/composition-helpers";
//   import { overwriteEnumDeep } from "@/util";
import InfoBlock from '@/components/infoBlock/InfoBlock.vue'
import InfoField from '@/components/infoBlock/InfoField.vue'
//   import { useEnums } from "@/hooks/useEnums";
//   import { getDownloadPath } from "@/util/apiUtil";
import DictTag from '@/components/DictTag/src/DictTag.vue'
import { DICT_TYPE,getDictLabel,getStrDictOptions } from '@/utils/dict'
import { dateFormatterByOptions } from '@/utils/formatTime'

//   const { getDisName: getAccidentLevel } = useEnums("AccidentLevel");
//   const { getDisName: getAccidentHighwayItLevel } = useEnums("AccidentHighwayItLevel");
//   const { getDisName: getAccidentHighwayGovLevel } = useEnums("AccidentHighwayGovLevel");
//   const { getDisName: getAccidentHighwaySection } = useEnums("AccidentHighwaySection");
//   const { getDisName: getReportType } = useEnums("ReportType");
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
defineOptions({
  name: 'InfoPage', // 直接定义 name
});
const emits = defineEmits<{
  (e: 'saveEvent'): void
}>()
const route = useRoute()
const router = useRouter()

const isView = computed(() => {
  return route.query.type === 'view'
})

const isDeal = computed(() => {
  return route.query.type === 'deal'
})

const info = ref<Required<GetApiRes<typeof tms_web.accidentFaultDamageReport_get_post>>>({} as any)
const fileInfo = ref<Required<GetApiRes<typeof tms_web.accidentFaultDamageReport_queryImage_post>>>(
  {} as any
)

const imgFilePath = computed(() => {
  return fileInfo.value.map((item) => {
    return item.address || ''
  })
})

const formData = ref<{
  id: any
  dealType: string
  freightMoney: number
  reductionMoney: number
  endMoney: number
  status: 'next' | 'finish'
}>({
  id: route.query.id,
  dealType: 'reduction',
  freightMoney: 0,
  reductionMoney: 0,
  endMoney: 0,
  status: 'finish'
})

const rules = {
  reductionMoney: [{ required: true, message: '请输入减免金额', trigger: 'blur' }]
}

const allEnums: any = ref({})
const dealTypeEnum: any = ref({})
const reportTypeName: any = ref({})

function getInfo() {
  tms_web
    .accidentFaultDamageReport_get_post({
      data: {
        id: route.query.id
      }
    })
    .then((data) => {
      info.value = data.data || {}
    //   info.value.accidentTime=dateFormatter2ByOptions(info.value.accidentTime)
    
      reportTypeName.value = getDictLabel('report_type',info.value.reportType)  + '上报'
    //   reportTypeName.value = info.value.reportType + '上报'
      formData.value.freightMoney = info.value.totalMoney
      formData.value.reductionMoney = info.value.reductionMoney
      formData.value.endMoney = info.value.totalMoney - info.value.reductionMoney
      if (info.value.status === 'not') {
        formData.value.status = 'finish'
      } else {
        formData.value.status = info.value.status
      }
    })

  tms_web
    .accidentFaultDamageReport_queryImage_post({
      data: {
        businessId: route.query.id
      }
    })
    .then((data) => {
      fileInfo.value = data.data || {}
    })
}

onMounted(() => {
  getInfo()
  //   allEnums.value = useEnumsStore()
  dealTypeEnum.value = getStrDictOptions('deal_type')
  console.log(getStrDictOptions('deal_type'));
  
})

// const $notify = useNotify()

function save() {
  saveFun()
}

async function saveFun() {
  await tms_web.accidentFaultDamageReport_exceptionHandling_post({
    data: {
      ...formData.value
    }
  })
  //   $notify({
  //     title: '提示',
  //     message: '操作成功！',
  //     type: 'success'
  //   })
  message.success('保存成功')
  emits('saveEvent')
  router.back()
}

function changeFreightMoney() {
  var aa = formData.value.freightMoney
  var bb = formData.value.reductionMoney
  formData.value.endMoney = aa - bb
}
</script>

<style scoped lang="scss">
.not-require {
  left: 1000px;
}
</style>
