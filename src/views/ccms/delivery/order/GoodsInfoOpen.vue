<template>
  <OpenComponents ref="openRef" title="商品信息" :width="500" hide-foot append-to-body>
    <custom-table :columns="columns" :data="list" hide-paging :padding="0" show-index />
  </OpenComponents>
</template>

<script lang="ts" setup>
import OpenComponents from '@/components/Open.vue'
import { ref } from 'vue'
import CustomTable from '@/components/customTable/Index.vue'

const openRef = ref<InstanceType<typeof OpenComponents>>()

const columns = [
  {
    title: '商品名称',
    key: 'spuName',
    minWidth: 150
  },
  {
    title: '包装',
    key: 'packingName',
    minWidth: 90
  },
  {
    title: '重量/体积',
    key: 'weight',
    minWidth: 130,
    render(h: any, params: any) {
      return h('span', `${params.row.weight || ''}吨/${params.row.volume || ''}方`)
    }
  }
]

const list = ref<any[]>([])

// 弹窗打开
function open(goodsList?: any) {
  openRef.value?.open()
  list.value = goodsList || []
}

defineExpose({
  open
})
</script>

<style lang="scss" scoped></style>
