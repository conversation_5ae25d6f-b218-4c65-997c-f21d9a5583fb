<template>
  <OpenComponents append-to-body ref="openRef" @on-save="save" title="变更装货地址" :width="500">
    <el-form :model="formData" :rules="rules" ref="formValidateRef" label-width="140px">
      <el-form-item label="装货地址：" prop="province">
        <SelectProvincialAndCities v-model:modelValue="provincialAndCities" />
      </el-form-item>
      <el-form-item label="详细地址：" prop="sendAddress">
        <el-input v-model="formData.sendAddress" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="联系人：" prop="sendName">
        <el-input v-model="formData.sendName" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="联系电话：" prop="sendPhone">
        <el-input v-model="formData.sendPhone" placeholder="请输入" />
      </el-form-item>
    </el-form>
  </OpenComponents>
</template>

<script lang="ts" setup>
import OpenComponents from '@/components/Open.vue'
import { ElForm } from 'element-plus'
import { computed, ref } from 'vue'
import SelectProvincialAndCities from '@/components/selectProvincialAndCities/SelectProvincialAndCities.vue'
import { tms_web } from '@/api/ccms'

const openRef = ref<InstanceType<typeof OpenComponents>>()
const formValidateRef = ref<InstanceType<typeof ElForm>>()

const emits = defineEmits<{
  (e: 'on-save', saveData: any): void
}>()

const rules = {
  province: [{ required: true, message: '请选择配送地址', trigger: 'change' }],
  address: [{ required: true, message: '请输入详细地址', trigger: 'blur' }],
  sendName: [{ required: true, message: '请输入联系人', trigger: 'blur' }],
  sendPhone: [{ required: true, message: '请输入联系电话', trigger: 'blur' }]
}

const formData = ref<any>({})

const provincialAndCities = computed({
  get() {
    const { province, city, county } = formData.value
    return {
      province: province || '',
      city: city || '',
      county: county || ''
    }
  },
  set(data) {
    formData.value = { ...formData.value, ...data }
  }
})

// 提交表单数据
function save() {
  formValidateRef.value?.validate((valid: any) => {
    if (valid) {
      saveFun()
    }
  })
}

const message = useMessage()

async function saveFun() {
  const params = {
    orderId: formData.value.id,
    sendProvince: formData.value.province,
    sendCity: formData.value.city,
    sendCounty: formData.value.county,
    sendAddress: formData.value.sendAddress,
    sendName: formData.value.sendName,
    sendPhone: formData.value.sendPhone
  }

  await tms_web.transportOrder_changeSendAddress_post({
    data: params
  })
  emits('on-save', params)
  message.success('操作成功')
  openRef.value?.close()
}

// 弹窗打开
function open(rowData: any) {
  openRef.value?.open()
  formData.value = JSON.parse(JSON.stringify(rowData))
  formData.value.province = formData.value.sendProvince
  formData.value.city = formData.value.sendCity
  formData.value.county = formData.value.sendCounty
  delete formData.value.sendProvince
  delete formData.value.sendCity
  delete formData.value.sendCounty
}

defineExpose({
  open
})
</script>

<style lang="scss" scoped></style>
