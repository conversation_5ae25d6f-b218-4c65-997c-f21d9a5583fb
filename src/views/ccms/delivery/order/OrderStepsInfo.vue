<script setup lang="ts">
import { cloneDeep } from 'lodash-es'
import dayjs from 'dayjs'

const props = defineProps<{
  orderInfo: any
}>()

const stepList = [
  {
    stepName: '客户下单',
    time: ''
  },
  {
    stepName: '订单审核',
    time: ''
  },
  {
    stepName: '司机接单',
    time: ''
  },
  {
    stepName: '装车确认',
    time: ''
  },
  {
    stepName: '配送中',
    time: ''
  },
  {
    stepName: '卸货确认',
    time: ''
  },
  {
    stepName: '已支付',
    time: ''
  },
  {
    stepName: '已完成',
    time: ''
  }
]

function formatTime(str: string | number) {
  if (str) {
    return dayjs(str).format('YYYY-MM-DD HH:mm:ss')
  } else {
    return ''
  }
}

const resStepList = computed(() => {
  const tempList: any[] = cloneDeep(stepList)
  tempList[0].time = formatTime(props.orderInfo.orderTime)
  tempList[1].time = formatTime(props.orderInfo.orderTime)
  tempList[2].time = formatTime(props.orderInfo.transportTime)
  if (props.orderInfo.driverLoadStatus === 'y') {
    tempList[3].time = formatTime(props.orderInfo.driverLoadTime)
  }

  if (props.orderInfo.customerLoadStatus === 'y') {
    tempList[4].time = formatTime(props.orderInfo.customerLoadTime)
  }

  if (props.orderInfo.customerUnloadStatus === 'y') {
    tempList[5].time = formatTime(props.orderInfo.customerUnloadTime)
  }

  if (props.orderInfo.payStatus === 'yes') {
    tempList[6].time = formatTime(props.orderInfo.payTime)
  }

  if (props.orderInfo.transportStatus === 'finish') {
    tempList[7].time = formatTime(props.orderInfo.finishTime)
  }

  return tempList
})

const active = computed(() => {
  let i = 0
  for (const [index, item] of resStepList.value.entries()) {
    if (!item.time) {
      i = index
      break
    } else if (index === resStepList.value.length - 1) {
      return index
    }
  }
  return i
})
</script>

<template>
  <el-steps :active="active">
    <el-step
      v-for="item in resStepList"
      :key="item.stepName"
      :title="item.stepName"
      :description="item.time || '-'"
    />
  </el-steps>
</template>

<style scoped lang="scss"></style>
