<template>
  <div class="flex flex-col gap-5">
    <div class="flex items-center gap-[10px] rounded-[10px] bg-white p-5 leading-none">
      <div class="text-[18px] font-bold text-black"> 合单调度 </div>
      <!-- <OrderStatus :name="info.orderStatus || ''" /> -->
      <div class="flex cursor-pointer items-center gap-[6px] text-[13px] text-[#A8ABB2]">
        <Icon icon="tdesign:rollback" />
        <span @click="router.back()">返回</span>
      </div>
    </div>
    <div class="flex flex-col gap-[30px] rounded-[10px] bg-white p-10">
      <InfoBlock name="客户信息">
        <el-form
          ref="ruleFormRef"
          style="max-width: 600px"
          :model="ruleForm"
          :rules="rules"
          label-width="100px"
        >
          <el-form-item label="合单策略" required prop="mergeStrategy">
            <el-radio-group v-model="ruleForm.mergeStrategy">
              <el-radio :value="item.value" v-for="(item, index) in mergeOrderList" :key="index">{{
                item.label
              }}</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="客户名称" v-if="ruleForm.mergeStrategy === 'customer'">
            <el-select
              v-model="mergeKHfrom.customerId"
              filterable
              placeholder="请选择客户（模糊搜索）"
            >
              <el-option
                v-for="item in customerList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <div v-else>
            <el-form-item label="定点路线">
              <el-select v-model="mergeKHfrom.routeId" filterable placeholder="请选择定点路线">
                <el-option
                  v-for="item in routeList"
                  :key="item.id"
                  :label="item.routeName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item required label="范围">
              <el-select v-model="mergeKHfrom.luxianId" filterable placeholder="请选择路线范围">
                <el-option
                  v-for="item in getDictOptions('route_range')"
                  :key="item.id"
                  :label="item.label+'km'"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </div>
        </el-form>
      </InfoBlock>
      <InfoBlock name="可合并订单">
        <custom-table
          @on-selection-change="selectionChangeFn"
          :columns="orderColumns"
          :data="info"
          :selectable="selectable"
          :showCheckbox="true"
          hide-paging
          :padding="0"
          show-index
        />
      </InfoBlock>
      <InfoBlock name="车辆调度">
        <el-form
          ref="ruleFormRef"
          style="max-width: 600px"
          :model="ruleForm"
          :rules="rules"
          label-width="100px"
        >
          <el-form-item label="车辆类型" prop="carModelId" required>
            <el-select
              @change="carMidelFn()"
              class="form_input"
              v-model="ruleForm.carModelId"
              placeholder="请选择"
            >
              <el-option
                :label="item.name"
                :value="item.id"
                v-for="(item, index) in modelsQueryList"
                :key="index"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="车辆规格" prop="carLengthId" required>
            <el-select
              @change="carlengthlFn()"
              class="form_input"
              v-model="ruleForm.carLengthId"
              placeholder="请选择"
            >
              <el-option
                :label="item.carLength + '米'"
                :value="item.id"
                v-for="(item, index) in lengthQueryList"
                :key="index"
              />
            </el-select>
          </el-form-item>
          <el-form-item style="position: relative" label="车辆调度" prop="carNumber">
            <el-select disabled v-model="ruleForm.carNumber" placeholder="请选择" />
            <el-button @click="dilogFn" style="position: absolute; right: -25%" type="primary" plain
              >选择车辆调度</el-button
            >
          </el-form-item>
          <el-form-item v-if="deiverFlag" label="驾驶员" prop="driverId">
            <el-select v-model="ruleForm.driverId" placeholder="请选择">
              <el-option
                :label="item.carrierName"
                :value="item.id"
                v-for="(item, index) in driverNameList"
                :key="index"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </InfoBlock>
      <InfoBlock name="路线信息">
        <div id="container" style="width: 100%; height: 500px"></div>
      </InfoBlock>

      <InfoBlock name="异常处理记录" v-if="isWaybillInfoPage">
        <custom-table
          :columns="errorColumns"
          :data="errorList"
          hide-paging
          :showCheckbox="true"
          :padding="0"
          show-index
        />
      </InfoBlock>
      <InfoBlock name="货检信息" v-if="isWaybillInfoPage">
        <custom-table
          :columns="checkColumns"
          :data="checkList"
          hide-paging
          :padding="0"
          show-index
        />
      </InfoBlock>
      <div class="flex justify-center">
        <el-button
          type="primary"
          @click="mergeOrder()"
          v-hasPermi="['ccms:transportOrder:mergeOrder']"
          >确认合单
        </el-button>
        <el-button @click="router.back()">返回</el-button>
      </div>
    </div>
    <SelectCarOpen ref="SelectCarOpenRef" @on-save="carObjFn" />
    <GoodsInfoOpen ref="goodsInfoRef" />
    <edit-address-open ref="editAddressOpenRef" @on-save="saveEditAddress" />
    <edit-send-address-open ref="editSendAddressOpenRef" @on-save="saveSendEditAddress" />
    <img-list-view ref="imgListViewRef" />
  </div>
</template>
<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue'
import { tms_web } from '@/api/ccms'
import InfoBlock from '@/components/infoBlock/InfoBlock.vue'
import InfoField from '@/components/infoBlock/InfoField.vue'
import OrderStatus from './OrderStatus.vue'
import GoodsInfoOpen from './GoodsInfoOpen.vue'
import EditAddressOpen from './EditAddressOpen.vue'
import SelectCarOpen from './SelectCarOpen.vue'
import EditSendAddressOpen from './EditSendAddressOpen.vue'
import ImgListView from '@/components/ImgListView.vue'
import { getStrDictOptions, DICT_TYPE, getDictObj, getDictOptions } from '@/utils/dict'
import CustomTable from '@/components/customTable/Index.vue'
import { hasPermission } from '@/directives/permission/hasPermi'
import { dateFormatterByOptions } from '@/utils/formatTime'
import { sysType } from '@/utils/sysType'
import type { FormInstance, FormRules } from 'element-plus'
import { optionsToEnums } from '@/utils/businessData'

// 表单内容
interface RuleForm {
  mergeStrategy: string
  orderIdList: []
  carModelId?: number
  carModelName: string
  carLengthId?: number
  carLength: string
  carNumber: string
  driverId: string
}

const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive<RuleForm>({
  mergeStrategy: 'customer',
  orderIdList: [],
  carModelId: undefined,
  carModelName: '',
  carLengthId: undefined,
  carLength: '',
  carNumber: '',
  driverId: ''
})
// 合单操作
function mergeOrder() {
  message.confirm('确认合单调度?', '合单调度提示').then(() => {
    const queryParams = { ...ruleForm }
    tms_web
      .transportOrder_mergeOrder_post({
        data: {
          ...queryParams
        }
      })
      .then(() => {
        message.success('操作成功')
        router.back()
      })
  })
}
const SelectCarOpenRef = ref()
// 选择车辆完成回显
const deiverFlag = ref(false)
const carObjFn = (item) => {
  // console.log(item)
  ruleForm.carNumber = item.id
  if (!item.driverId) {
    deiverFlag.value = true
  } else {
    {
      deiverFlag.value = false
      ruleForm.driverId = ''
    }
  }
}
// 打开车辆选择弹窗
const dilogFn = () => {
  {
    SelectCarOpenRef.value.open()
  }
}
const locationOptions = ['Home', 'Company', 'School']

const rules = reactive<FormRules<RuleForm>>({
  mergeStrategy: [
    {
      required: true,
      message: '请选择合单策略',
      trigger: 'change'
    }
  ],
  carModelId: [
    {
      required: true,
      message: '请选择车辆类型',
      trigger: 'change'
    }
  ],
  carLengthId: [
    {
      required: true,
      message: '请选择车辆规格',
      trigger: 'change'
    }
  ],
  carNumber: [
    {
      required: false,
      message: '请选择调度车辆',
      trigger: 'change'
    }
  ],
  driverId: [
    {
      required: true,
      message: '请选择驾驶员',
      trigger: 'change'
    }
  ]
})


// 表单内容
const message = useMessage() // 消息弹窗
const editAddressOpenRef = ref<InstanceType<typeof EditAddressOpen>>()
const editSendAddressOpenRef = ref<InstanceType<typeof EditSendAddressOpen>>()
const imgListViewRef = ref<typeof ImgListView>()
const VITE_SYS_TYPE = import.meta.env.VITE_SYS_TYPE

const route = useRoute()
const router = useRouter()
const isAudit = computed(() => {
  return route.query.audit === 'audit'
})

const info = ref([] as any)

const isHistoryOrderPage = computed(() => {
  return (
    route.path.includes('/delivery/historyOrder/info') ||
    (info.value.mergeOrder && info.value.mergeOrder === 'y') ||
    (info.value.splitOrder && info.value.splitOrder === 'y')
  )
})

const isWaybillInfoPage = computed(() => {
  return route.path.includes('/delivery/waybill/info') || isHistoryOrderPage.value
})

const goodsInfoRef = ref<typeof GoodsInfoOpen.prototype>()

const deliveryColumns = [
  {
    title: '卸货地址',
    key: 'address',
    minWidth: 200,
    render(h: any, params: any) {
      return h(
        'span',
        `${params.row.province || ''}${params.row.city || ''}${params.row.county || ''}${params.row.address || ''}`
      )
    }
  },
  {
    title: '收货人',
    key: 'receiverName',
    minWidth: 130
  },
  {
    title: '收货人联系电话',
    key: 'receiverPhone',
    minWidth: 150
  },
  {
    title: '操作',
    render: (h: any, params: any) => {
      let btn: any[] = []
      btn.push(
        h('span', {
          domProps: {
            innerHTML: '商品信息'
          },
          class: 'cyan',
          on: {
            click: () => {
              goodsInfoRef.value?.open(params.row.goodsList)
            }
          }
        })
      )
      if (!isAudit.value && hasPermission(['ccms:transportOrder:update'])) {
        btn.push(
          h('span', {
            domProps: {
              innerHTML: '修改'
            },
            on: {
              click: () => {
                editDeliveryItemOpen(params.row, params.index)
              }
            }
          })
        )
      }
      return h(
        'div',
        {
          class: 'handleBtn'
        },
        btn
      )
    }
  }
]

function editDeliveryItemOpen(rowData: any, index: number) {
  editAddressOpenRef.value?.open(rowData, index)
}

function saveEditAddress(saveData: any, index: number) {
  const tempData = JSON.parse(JSON.stringify(info.value.addressList || []))
  tempData[index] = saveData
  info.value.addressList = tempData
}

function saveSendEditAddress(saveData: any) {
  let tempData = JSON.parse(JSON.stringify(info.value))
  tempData = { ...tempData, ...saveData }
  info.value = tempData
}
// 选择项id数组
const selectIdList = ref([])
// 选择项变化
const selectionChangeFn = (data) => {
  ruleForm.orderIdList = data.map((item) => item.id)
}
// 设置不可选择项
const selectable = (row) => true
const orderColumns = [
  {
    title: '订单编号',
    key: 'orderNumber',
    minWidth: 150
  },
  {
    title: '装货地址',
    key: 'sendAddress',
    minWidth: 250,
    render(h: any, params: any) {
      return (
        params.row.sendProvince +
        params.row.sendCity +
        params.row.sendCounty +
        params.row.sendAddress
      )
    }
  },
  {
    title: '件重(kg)',
    key: 'weight',
    minWidth: 100
  },
  {
    title: '件数',
    key: 'number',
    minWidth: 100
  },
  {
    title: '总重(kg)',
    key: 'transportCarNumber',
    minWidth: 100,
    render(h: any, params: any) {
      return params.row.number * params.row.weight
    }
  },
  {
    title: '订单金额',
    key: 'totalMoney',
    minWidth: 100
  },
  {
    title: '操作',
    key: 'urls',
    render(h: any, params: any): any {
      return h(
        'span',
        {
          class: 'text-[#E04F48] cursor-pointer',
          on: {
            click: () => {
              //查询文件列表
              tms_web.accidentFaultDamageReport_queryImage_post({}).then((data) => {})
              //
            }
          }
        },
        '移除'
      )
    }
  }
  // {
  //   title: '车辆类型',
  //   key: 'transportCarType',
  //   minWidth: 150,
  //   render(h: any, params: any) {
  //     return h('span', getDictObj(DICT_TYPE.CAR_TYPE, params.row.transportCarType))
  //   }
  // },
  // {
  //   title: '车辆规格',
  //   key: 'transportCarLength',
  //   minWidth: 150,
  //   render(h: any, params: any): any {
  //     return h('span', params.row.transportCarLength ? params.row.transportCarLength + '米' : '-')
  //   }
  // },
]
// 联选变化函数
const carMidelFn = () => {
  ruleForm.carModelName = modelsQueryList.value.filter(
    (item) => item.id == ruleForm.carModelId
  )[0].name
}
const carlengthlFn = () => {
  ruleForm.carLength = lengthQueryList.value.filter(
    (item) => item.id == ruleForm.carLengthId
  )[0].carLength
}
// 获取车辆类型和长度
const modelsQueryList = ref<GetApiResByList<typeof tms_web.vehicleSpecifications_queryModels_post>>(
  []
)
async function queryCarModelsQuery() {
  const data = await tms_web.vehicleSpecifications_queryModels_post({
    load: { fullLoading: true }
  })
  modelsQueryList.value = data?.data?.list || []
}

const lengthQueryList = ref<GetApiResByList<typeof tms_web.vehicleSpecifications_queryLength_post>>(
  []
)
async function queryCarLengthQuery() {
  const data = await tms_web.vehicleSpecifications_queryLength_post({
    load: { fullLoading: true }
  })
  lengthQueryList.value = data?.data?.list || []
}
function getInfo() {
  const query = {
    routeId: mergeKHfrom.routeId,
    customerId: mergeKHfrom.customerId
  }
  if (ruleForm.mergeStrategy === 'customer') {
    delete query.routeId
  } else {
    delete query.customerId
  }
  tms_web
    .transportOrder_queryMergeVOList_post({
      data: {
        ...query
      }
    })
    .then((data) => {

      info.value = data.data || []
      if (isHistoryOrderPage.value) {
        //移除操作列
        deliveryColumns.pop()
      }
    })
}
const driverNameList = ref()
const driverList = () => {
  tms_web
    .driver_query_post({
      data: {}
    })
    .then((data) => {
      driverNameList.value = data.data?.list
    })
}
const errorColumns = [
  {
    title: '上报时间',
    key: 'declareTime',
    minWidth: 150,
    render(h: any, params: any): any {
      return h('span', dateFormatterByOptions({ cellValue: params.row.declareTime }))
    }
  },
  {
    title: '异常类型',
    key: 'reportType',
    minWidth: 100,
    render(h: any, params: any) {
      return h('span', getDictObj(DICT_TYPE.REPORT_TYPE, params.row.reportType))
    }
  },
  {
    title: '异常描述',
    key: 'faultRemark',
    minWidth: 180
  },
  {
    title: '处理状态',
    key: 'status',
    minWidth: 150,
    render(h: any, params: any) {
      return h('span', getDictObj(DICT_TYPE.DEAL_REPORT_STATUS, params.row.status))
    }
  },
  {
    title: '处理时间',
    key: 'dealTime',
    minWidth: 150,
    render(h: any, params: any): any {
      return h('span', dateFormatterByOptions({ cellValue: params.row.dealTime }))
    }
  },
  {
    title: '处理人',
    key: 'dealUserName',
    minWidth: 150
  },
  {
    title: '现场图片',
    key: 'urls',
    render(h: any, params: any): any {
      return h(
        'span',
        {
          class: 'text-[#2D57CC] cursor-pointer',
          on: {
            click: () => {
              //查询文件列表
              tms_web
                .accidentFaultDamageReport_queryImage_post({
                  data: {
                    businessId: params.row.id
                  },
                  load: {
                    fullLoading: true
                  }
                })
                .then((data) => {
                  if (data.data && data.data.length) {
                    imgListViewRef.value?.open(data.data)
                  } else {
                    message.warning('暂无图片')
                  }
                })
              //
            }
          }
        },
        '查看图片'
      )
    }
  }
]

const errorList = ref<
  GetApiResByList<typeof tms_web.accidentFaultDamageReport_queryAccidentDeclaration_post>
>([])

function queryErrorList() {
  tms_web
    .accidentFaultDamageReport_queryAccidentDeclaration_post({
      data: {
        orderId: route.query.id as unknown as number
      }
    })
    .then((data) => {
      errorList.value = data?.data?.list || []
    })
}

//货检信息
const checkColumns = [
  {
    title: '检查阶段',
    key: 'name',
    minWidth: 150
  },
  {
    title: '描述',
    key: 'remark',
    minWidth: 100
  },
  {
    title: '操作',
    key: 'urls',
    render(h: any, params: any): any {
      return h(
        'span',
        {
          class: 'text-[#2D57CC] cursor-pointer',
          on: {
            click: () => {
              //文件列表
              if (params.row && params.row.fileList && params.row.fileList.length) {
                imgListViewRef.value?.open(params.row.fileList)
              } else {
                message.warning('暂无图片')
              }
            }
          }
        },
        '查看图片'
      )
    }
  }
]

const checkList = ref<any>([])

function queryCheckList() {
  tms_web
    .transportOrder_queryCheckList_post({
      data: {
        orderId: route.query.id as unknown as number
      }
    })
    .then((data) => {
      checkList.value = data.data || []
    })
}
// 客户与策略表单
const mergeKHfrom = reactive({
  customerId: '',
  routeId: '',
  luxianId:''
})
// 监听器
watch(
  () => [mergeKHfrom.customerId, mergeKHfrom.routeId],
  ([newcustomerId, newrouteId], [oldcustomerId, oldrouteId]) => {
    console.log(`customerId: ${oldcustomerId} -> ${newcustomerId}`)
    console.log(`routeId: ${oldrouteId} -> ${newrouteId}`)
    if (mergeKHfrom.customerId || mergeKHfrom.routeId) {
      getInfo()
    }
  }
)
// 接口节流开关
const loading = ref(false)
// 定点路线列表
const routeList: any = ref([])
const queryRouteList = () => {
  loading.value = true
  tms_web
    .routes_queryRoute_post({
      data: {}
    })
    .then((res) => {
      // console.log(res, '定点路线')

      routeList.value = res.data?.list || []
    })
    .finally(() => {
      loading.value = false
    })
}
const customerList: any = ref([])
const queryCustomer = () => {
  loading.value = true
  tms_web
    .customer_query_post({
      data: {}
    })
    .then((res) => {
      customerList.value = res.data?.list || []
    })
    .finally(() => {
      loading.value = false
    })
}
const mergeOrderList = ref()
// 地图初始化函数
const GDmapInit = () => {
  var map = new AMap.Map('container', {
    zoom: 10, //设置地图显示的缩放级别
    center: [116.397428, 39.90923], //设置地图中心点坐标
    viewMode: '2D', //设置地图模式
    lang: 'zh_cn' //设置地图语言类型
  })
}
const carTypeList = ref()
const getcarType = () => {
  carTypeList.value = getDictOptions(DICT_TYPE.CAR_TYPE)
}
onMounted(() => {
  // 获取车辆规格与类型
  queryCarModelsQuery()
  queryCarLengthQuery()
  // 获取驾驶员列表
  driverList()
  getcarType()
  // 初始化高德地图
  GDmapInit()
  // 获取定点路线数组
  queryRouteList()
  // 获取客户数组
  queryCustomer()
  mergeOrderList.value = getStrDictOptions('merge_strategy')
  // console.log(mergeOrderList.value, 'mergeOrderList.value')

  if (isWaybillInfoPage.value) {
    queryCheckList()
    queryErrorList()
  }
})
</script>

<style scoped lang="scss"></style>
