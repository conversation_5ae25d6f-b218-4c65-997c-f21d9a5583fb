<script setup lang="ts">
import { computed, ref } from 'vue'
import { tms_web } from '@/api/ccms'
import GoodsInfoOpen from './GoodsInfoOpen.vue'
import EditAddressOpen from './EditAddressOpen.vue'
import SelectCarOpen from './SelectCarOpen.vue'
import SelectDriverOpen from './SelectDriverOpen.vue'
import { cloneDeep } from 'lodash-es'
import CustomTable from '@/components/customTable/Index.vue'
import { getDictLabel,DICT_TYPE } from '@/utils/dict'

const emits = defineEmits(['on-save'])

const isDrawerShow = ref(false)

const orderId = ref('')

const goodsInfoRef = ref<typeof GoodsInfoOpen.prototype>()
const editAddressOpenRef = ref<InstanceType<typeof EditAddressOpen>>()
const selectCarOpenRef = ref<InstanceType<typeof SelectCarOpen>>()
const selectDriverOpenRef = ref<InstanceType<typeof SelectDriverOpen>>()

const info = ref<any>({})

const deliveryColumns = [
  {
    title: '卸货地址',
    key: 'address',
    minWidth: 250,
    render(h: any, params: any) {
      return h(
        'span',
        `${params.row.province || ''}${params.row.city || ''}${params.row.county || ''}${params.row.address || ''}`
      )
    }
  },
  {
    title: '收货人',
    key: 'receiverName',
    minWidth: 100
  },
  {
    title: '收货人联系电话',
    key: 'receiverPhone',
    minWidth: 150
  },
  {
    title: '已选车辆',
    key: 'carList',
    minWidth: 200,
    render: (h: any, params: any) => {
      return h(
        'span',
        params.row.carList?.map((item: any) => item.carNumber).join(',') || '请选择车辆'
      )
    }
  },
  {
    title: '操作',
    fixed: 'right',
    width: 200,
    render: (h: any, params: any) => {
      let btn: any[] = []
      btn.push(
        h('span', {
          domProps: {
            innerHTML: '选择车辆'
          },
          on: {
            click: () => {
              selectCarOpenRef.value?.open(params.index)
            }
          }
        }),
        h('span', {
          domProps: {
            innerHTML: '商品信息'
          },
          class: 'cyan',
          on: {
            click: () => {
              goodsInfoRef.value?.open(params.row.goodsList)
            }
          }
        }),
        h('span', {
          domProps: {
            innerHTML: '修改'
          },
          on: {
            click: () => {
              editAddressOpenRef.value?.open(params.row, params.index)
            }
          }
        })
      )
      return h(
        'div',
        {
          class: 'handleBtn'
        },
        btn
      )
    }
  }
]

const deliveryData = computed(() => {
  let res: any[] = []
  for (const item of info.value.addressList || []) {
    if (item.carList && item.carList.length) {
      res.push(...item.carList)
    }
  }
  //根据车牌号码去重
  const resMap = new Map()
  for (const item of res) {
    if (!resMap.has(item.carNumber)) {
      resMap.set(item.carNumber, item)
    }
  }
  res = Array.from(resMap.values())
  return res
})
const driverColumns = [
  {
    title: '已选车辆',
    key: 'carNumber',
    minWidth: 130
  },
  {
    title: '车辆类型',
    key: 'carType',
    minWidth: 130,
    render(h: any, params: any) {
      return h('span', getDictLabel(DICT_TYPE.CAR_TYPE, params.row.carType))
    }

  },
  {
    title: '车辆规格',
    key: 'carModelName',
    minWidth: 150,
    render: (h: any, params: any) => {
      return h('span', {
        domProps: {
          innerHTML: `${params.row.carModelName || ''} / ${params.row.carLength || ''}米`
        }
      })
    }
  },
  {
    title: '所属承运商',
    key: 'carrierName',
    minWidth: 130
  },
  {
    title: '驾驶员姓名',
    key: 'driverName',
    minWidth: 130
  },
  {
    title: '操作',
    width: 150,
    fixed: 'right',
    render: (h: any, params: any) => {
      let btn: any[] = []
      btn.push(
        h('span', {
          domProps: {
            innerHTML: '选择驾驶员'
          },
          on: {
            click: () => {
              selectDriverOpenRef.value?.open(params.row)
            }
          }
        })
      )
      btn.push(
        h('span', {
          domProps: {
            innerHTML: '删除'
          },
          class: 'red',
          on: {
            click: () => {
              console.log(params)
              removeCar(params.row.carNumber || '')
            }
          }
        })
      )
      return h(
        'div',
        {
          class: 'handleBtn'
        },
        btn
      )
    }
  }
]

function saveEditAddress(saveData: any, index: number) {
  const tempData = JSON.parse(JSON.stringify(info.value.addressList || []))
  tempData[index] = saveData
  info.value.addressList = tempData
}

function removeCar(carNumber: string) {
  const addressList = cloneDeep(info.value.addressList)
  for (const address of addressList) {
    let delIndex = null
    for (const [index, car] of (address.carList || []).entries()) {
      if (carNumber === car.carNumber) {
        delIndex = index
        break
      }
    }
    if (delIndex !== null) {
      address.carList.splice(delIndex, 1)
      break
    }
  }
  info.value.addressList = addressList
}

function saveSelectedCars(carList: any, index: number) {
  //直接修改info.value.addressList
  const addressList = cloneDeep(info.value.addressList)
  const temp = addressList[index]
  if (!temp.carList) {
    temp.carList = []
  }
  //车牌号已经存在时排除
  const carNumberList = temp.carList.map((item: any) => item.carNumber)
  carList = carList.filter((item: any) => !carNumberList.includes(item.carNumber))
  temp.carList.push(...carList)
  info.value.addressList = addressList
}

function saveSelectedDriver(driver: any, carNumber: string) {
  const addressList = cloneDeep(info.value.addressList)
  for (const address of addressList) {
    for (const car of address.carList || []) {
      if (carNumber === car.carNumber) {
        car.driverName = driver.name
        car.driverId = driver.id
      }
    }
  }
  info.value.addressList = addressList
}

function getInfo() {
  tms_web
    .transportOrder_get_post({
      data: {
        id: orderId.value as unknown as number
      }
    })
    .then((data) => {
      info.value = data.data || {}
    })
}

const message = useMessage()

function save() {
  const params: any = {
    orderId: orderId.value,
    detailList: [],
    carDriverList: []
  }

  for (const item of info.value.addressList) {
    if (!item.carList || !item.carList.length) {
      message.warning('请选择车辆！')
      return
    }
    params.detailList.push({
      id: item.id,
      province: item.province,
      city: item.city,
      county: item.county,
      address: item.address,
      receiverName: item.receiverName,
      receiverPhone: item.receiverPhone,
      carNumberList: item.carList.map((item: any) => item.carNumber)
    })
  }
  for (const car of deliveryData.value) {
    if (!car.driverId) {
      message.warning('请选择驾驶员！')
      return
    }
    params.carDriverList.push({
      carNumber: car.carNumber,
      driverId: car.driverId
    })
  }
  tms_web
    .transportOrder_spiltOrder_post({
      data: params
    })
    .then(() => {
      message.success('操作成功！')
      emits('on-save')
      close()
    })
}

function open(id: string) {
  isDrawerShow.value = true
  orderId.value = id
  getInfo()
}

function close() {
  drawerRef.value.handleClose()
}

const drawerRef = ref<any>()

defineExpose({
  open,
  close
})
</script>

<template>
  <el-drawer
    ref="drawerRef"
    size="1000px"
    v-model="isDrawerShow"
    :withHeader="false"
    :destroy-on-close="true"
    :close-on-press-escape="false"
    :wrapperClosable="false"
  >
    <GoodsInfoOpen ref="goodsInfoRef" />
    <SelectCarOpen ref="selectCarOpenRef" @on-save="saveSelectedCars" />
    <SelectDriverOpen ref="selectDriverOpenRef" @on-save="saveSelectedDriver" />
    <edit-address-open ref="editAddressOpenRef" is-local @on-save="saveEditAddress" />
    <div class="flex h-full flex-col">
      <div
        class="flex h-[50px] items-center justify-between border-b border-solid border-[#EBEBEB] px-5"
      >
        <div class="text-[16px] font-bold text-black">车辆调度</div>
        <i
          @click="close"
          class="el-icon el-icon-close cursor-pointer text-[13px] text-[#999999]"
        ></i>
      </div>
      <div class="scrollBar flex flex-1 flex-col gap-5 overflow-y-auto p-5">
        <custom-table
          table-title="卸货地址"
          :columns="deliveryColumns"
          :data="info.addressList || []"
          show-index
          :padding="0"
          hide-paging
        />
        <custom-table
          table-title="驾驶员信息"
          :columns="driverColumns"
          :data="deliveryData || []"
          show-index
          :padding="0"
          hide-paging
        />
      </div>
      <div class="flex h-[56px] items-center justify-center bg-[#F5F7FA]">
        <el-button type="primary" @click="save">提交</el-button>
        <el-button @click="close">取消</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<style scoped lang="scss"></style>
