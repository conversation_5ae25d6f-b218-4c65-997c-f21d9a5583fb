<template>
  <div class="flex flex-col gap-5">
    <div class="flex items-center gap-[10px] rounded-[10px] bg-white p-5 leading-none">
      <div class="text-[18px] font-bold text-black"> 拆单 </div>
      <OrderStatus :name="OrderData.orderStatus || ''" />
      <div class="flex cursor-pointer items-center gap-[6px] text-[13px] text-[#A8ABB2]">
        <Icon icon="tdesign:rollback" />
        <span @click="router.back()">返回</span>
      </div>
    </div>
    <div class="flex flex-col gap-[30px] rounded-[10px] bg-white p-10">
      <InfoBlock name="卸货地址">
        <custom-table
          :columns="orderColumns"
          :data="orderList"
          hide-paging
          :padding="0"
          show-index
        />
      </InfoBlock>
    </div>
    <div class="flex flex-col gap-[30px] rounded-[10px] bg-white p-10">
      <InfoBlock name="拆单信息" style="position: relative">
        <el-checkbox class="car_du" label="开启车辆调度" v-model="carDUflag" />

        <div class="spilt_box" v-for="(item, index) in tableDataList" :key="index">
          <div class="sunorder_number">
            <span> {{ OrderData.orderNumber + '-拆' + (index + 1) }} </span>
            <div class="del_sunorder" @click="delTableData(index)">
              <el-icon><Delete /></el-icon>
              删除子订单
            </div>
          </div>
          <el-form
            style="display: flex"
            :inline="true"
            :ref="'formRules' + index"
            :model="item"
            class="demo-form-inline"
          >
            <el-form-item v-if="!carDUflag" label="车辆类型" required>
              <el-select
                @change="carMidelFn(item)"
                class="form_input"
                v-model="item.carModelId"
                placeholder="请选择"
              >
                <el-option
                  :label="item1.name"
                  :value="item1.id"
                  v-for="(item1, index1) in modelsQueryList"
                  :key="index1"
                />
              </el-select>
            </el-form-item>
            <el-form-item v-if="!carDUflag" label="车辆规格" required>
              <el-select
                @change="carlengthlFn(item)"
                class="form_input"
                v-model="item.carLengthId"
                placeholder="请选择"
              >
                <el-option
                  :label="item2.carLength + '米'"
                  :value="item2.id"
                  v-for="(item2, index2) in lengthQueryList"
                  :key="index2"
                />
              </el-select>
            </el-form-item>
            <el-form-item v-if="carDUflag">
              <el-button type="primary" plain @click="dilogFn(index)">选择车辆调度</el-button>
              <span style="margin: 0 10px">|</span>
              <span style="width: 100px">{{ item.carNumber }}</span>
            </el-form-item>
            <el-form-item v-if="carDUflag && driverIdList[index]" label="驾驶员" required>
              <el-select class="form_input" v-model="item.driverId" placeholder="请选择" clearable>
                <el-option
                  :label="item1.carrierName"
                  :value="item1.id"
                  v-for="(item1, index1) in driverNameList"
                  :key="index1"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="子订单运费" required>
              <el-input
                class="form_input"
                v-model="item.totalMoney"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-form>

          <el-table border :data="item.addressList" style="width: 100%">
            <el-table-column type="index" label="序号" min-width="80" />
            <el-table-column label="卸货地址" min-width="300">
              <template #default="{ row }">
                {{ row.province + row.city + row.county + row.address }}
              </template>
            </el-table-column>
            <el-table-column label="货物名称" width="200">
              <template #default="{ row }">
                {{ row.goodsList[0].spuName }}
              </template>
            </el-table-column>
            <el-table-column label="件重" min-width="200">
              <template #default="{ row }">
                {{ row.goodsList[0].weight + 'kg' }}
              </template>
            </el-table-column>
            <el-table-column label="件数" min-width="200">
              <template #default="{ row }">
                <el-input
                  class="table_input"
                  v-model="row.goodsList[0].number"
                  placeholder="请输入"
                />
                <!-- <el-input-number v-model="row.goodsList[0].number" :disabled="true" /> -->
              </template>
            </el-table-column>
            <el-table-column label="总重" min-width="200">
              <template #default="{ row }">
                {{ row.goodsList[0].number * row.goodsList[0].weight + 'kg' }}
              </template>
            </el-table-column>
            <el-table-column prop="address" min-width="120" label="操作">
              <template #default="{ $index }">
                <el-button type="danger" plain @click="delRowFn($index, index)">移除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <el-button type="primary" plain class="add_Table" @click="addTableData"
          >新建子订单</el-button
        >
      </InfoBlock>
      <div class="flex justify-center">
        <el-button
          type="primary"
          @click="spiltOrder()"
          v-hasPermi="['ccms:transportOrder:spiltOrder']"
          >确认拆单
        </el-button>
        <el-button @click="router.back()">返回</el-button>
      </div>
    </div>
    <SelectCarOpen ref="SelectCarOpenRef" @on-save="carObjFn" />
    <GoodsInfoOpen ref="goodsInfoRef" />
    <edit-address-open ref="editAddressOpenRef" @on-save="saveEditAddress" />
    <edit-send-address-open ref="editSendAddressOpenRef" @on-save="saveSendEditAddress" />
    <img-list-view ref="imgListViewRef" />
  </div>
</template>
<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue'
import { tms_web } from '@/api/ccms'
import InfoBlock from '@/components/infoBlock/InfoBlock.vue'
import OrderStatus from './OrderStatus.vue'
import GoodsInfoOpen from './GoodsInfoOpen.vue'
import EditAddressOpen from './EditAddressOpen.vue'
import EditSendAddressOpen from './EditSendAddressOpen.vue'
import ImgListView from '@/components/ImgListView.vue'
import { getDictLabel, DICT_TYPE, getDictOptions } from '@/utils/dict'
import CustomTable from '@/components/customTable/Index.vue'
import { hasPermission } from '@/directives/permission/hasPermi'
import { dateFormatterByOptions } from '@/utils/formatTime'
import { sysType } from '@/utils/sysType'
import SelectCarOpen from './SelectCarOpen.vue'
import { cloneDeep } from 'lodash-es'
import { Delete } from '@element-plus/icons-vue'
const selectCarOpenRef = ref()
const message = useMessage() // 消息弹窗
const editAddressOpenRef = ref<InstanceType<typeof EditAddressOpen>>()
const editSendAddressOpenRef = ref<InstanceType<typeof EditSendAddressOpen>>()
const imgListViewRef = ref<typeof ImgListView>()
const VITE_SYS_TYPE = import.meta.env.VITE_SYS_TYPE

const route = useRoute()
const router = useRouter()
const isAudit = computed(() => {
  return route.query.audit === 'audit'
})
// 联选变化函数
const carMidelFn = (row) => {
  row.carModelName = modelsQueryList.value.filter((item) => item.id == row.carModelId)[0].name
}
const carlengthlFn = (row) => {
  row.carLength = lengthQueryList.value.filter((item) => item.id == row.carLengthId)[0].carLength
}
const SelectCarOpenRef = ref()
// 选择车辆完成回显
const carObjFn = (item, index) => {
  tableDataList[index].carNumber = item.id

  // ruleForm.carNumber=item.id
  if (!item.driverId) {
    driverIdList[index] = true
  } else {
    tableDataList[index].driverId = ''
  }
}
// 驾驶员列表
const driverNameList = ref()
const driverList = () => {
  tms_web
    .driver_query_post({
      data: {}
    })
    .then((data) => {
      // console.log(data.data, '驾驶员列表')
      driverNameList.value = data.data?.list
    })
}
// 打开车辆选择弹窗
const dilogFn = (index) => {
  SelectCarOpenRef.value.open(index)
}
const driverIdList = [false, false]
const addTableData = () => {
  driverIdList.push('')
  tableDataList.push({
    totalMoney: '',
    carNumber: '',
    driverId: '',
    carModelId: '',
    carModelName: '',
    carLengthId: '',
    carLength: '',
    addressList: OrderData.value.addressList
  })
}
const delRowFn = (index, i) => {
  tableDataList[i].data.splice(index, 1)
}
const delTableData = (index) => {
  tableDataList.splice(index, 1)
}

const carTypeList = ref()
const getcarType = () => {
  carTypeList.value = getDictOptions(DICT_TYPE.CAR_TYPE)
}
const tableDataList = reactive([
  {
    totalMoney: '',
    carNumber: '',
    driverId: '',
    carModelId: '',
    carModelName: '',
    carLengthId: '',
    carLength: '',
    addressList: []
  },
  {
    totalMoney: '',
    carNumber: '',
    driverId: '',
    carModelId: '',
    carModelName: '',
    carLengthId: '',
    carLength: '',
    addressList: []
  }
])
// 子订单表格
const tableData = [
  {
    date: '2016-05-03',
    name: 'Tom',
    address: 'No. 189, Grove St, Los Angeles'
  },
  {
    date: '2016-05-02',
    name: 'Tom',
    address: 'No. 189, Grove St, Los Angeles'
  },
  {
    date: '2016-05-04',
    name: 'Tom',
    address: 'No. 189, Grove St, Los Angeles'
  },
  {
    date: '2016-05-01',
    name: 'Tom',
    address: 'No. 189, Grove St, Los Angeles'
  }
]
function saveSelectedCars(carList: any, index: number) {
  //直接修改info.value.addressList
  const addressList = cloneDeep(info.value.addressList)
  const temp = addressList[index]
  if (!temp.carList) {
    temp.carList = []
  }
  //车牌号已经存在时排除
  const carNumberList = temp.carList.map((item: any) => item.carNumber)
  carList = carList.filter((item: any) => !carNumberList.includes(item.carNumber))
  temp.carList.push(...carList)
  info.value.addressList = addressList
}
const info = ref([])

const isHistoryOrderPage = computed(() => {
  return (
    route.path.includes('/delivery/historyOrder/info') ||
    (info.value.mergeOrder && info.value.mergeOrder === 'y') ||
    (info.value.splitOrder && info.value.splitOrder === 'y')
  )
})

const isWaybillInfoPage = computed(() => {
  return route.path.includes('/delivery/waybill/info') || isHistoryOrderPage.value
})

const goodsInfoRef = ref<typeof GoodsInfoOpen.prototype>()

const deliveryColumns = [
  {
    title: '卸货地址',
    key: 'address',
    minWidth: 200,
    render(h: any, params: any) {
      return h(
        'span',
        `${params.row.province || ''}${params.row.city || ''}${params.row.county || ''}${params.row.address || ''}`
      )
    }
  },
  {
    title: '收货人',
    key: 'receiverName',
    minWidth: 130
  },
  {
    title: '收货人联系电话',
    key: 'receiverPhone',
    minWidth: 150
  },
  {
    title: '操作',
    render: (h: any, params: any) => {
      let btn: any[] = []
      btn.push(
        h('span', {
          domProps: {
            innerHTML: '商品信息'
          },
          class: 'cyan',
          on: {
            click: () => {
              goodsInfoRef.value?.open(params.row.goodsList)
            }
          }
        })
      )
      if (!isAudit.value && hasPermission(['ccms:transportOrder:update'])) {
        btn.push(
          h('span', {
            domProps: {
              innerHTML: '修改'
            },
            on: {
              click: () => {
                editDeliveryItemOpen(params.row, params.index)
              }
            }
          })
        )
      }
      return h(
        'div',
        {
          class: 'handleBtn'
        },
        btn
      )
    }
  }
]

function editDeliveryItemOpen(rowData: any, index: number) {
  editAddressOpenRef.value?.open(rowData, index)
}

function saveEditAddress(saveData: any, index: number) {
  const tempData = JSON.parse(JSON.stringify(info.value.addressList || []))
  tempData[index] = saveData
  info.value.addressList = tempData
}

function saveSendEditAddress(saveData: any) {
  let tempData = JSON.parse(JSON.stringify(info.value))
  tempData = { ...tempData, ...saveData }
  info.value = tempData
}

const orderColumns = [
  {
    title: '卸货地址',
    key: 'transportCarNumber',
    minWidth: 150,
    render(h: any, params: any) {
      return params.row.province + params.row.city + params.row.county + params.row.address
    }
  },
  {
    title: '货物名称',
    key: 'transportCarNumber',
    minWidth: 150,
    render(h: any, params: any) {
      let str = ''
      params.row.goodsList.forEach((item) => {
        str = str + item.spuName
      })
      return str || '-'
    }
  },
  {
    title: '件重',
    key: 'weight',
    minWidth: 150,
    render(h: any, params: any) {
      return (params.row.goodsList[0].weight || '-') + 'kg'
    }
  },
  {
    title: '件数',
    key: 'number',
    minWidth: 150,
    render(h: any, params: any) {
      return params.row.goodsList[0].number || '-'
    }
  },
  {
    title: '总重',
    minWidth: 150,
    render(h: any, params: any) {
      return +params.row.goodsList[0].number * +params.row.goodsList[0].weight + 'kg'
    }
  }
  //   {
  //     title: '车辆类型',
  //     key: 'transportCarType',
  //     minWidth: 150,
  //     render(h: any, params: any) {
  //       return h('span', getDictLabel(DICT_TYPE.CAR_TYPE, params.row.transportCarType))
  //     }
  //   },
  //   {
  //     title: '车辆规格',
  //     key: 'transportCarLength',
  //     minWidth: 150,
  //     render(h: any, params: any): any {
  //       return h('span', params.row.transportCarLength ? params.row.transportCarLength + '米' : '-')
  //     }
  //   },
]

const orderList = computed(() => {
  return info.value
})
// 登记总数
const numMax = ref(0)
function sumArray(arr) {
  return arr.reduce((acc, curr) => acc + +curr.number, 0)
}
const OrderData = ref({})
function getInfo() {
  tms_web
    .transportOrder_get_post({
      data: {
        id: route.query.id as unknown as number
      }
    })
    .then((data) => {
      // console.log(data, '拆单详情')
      info.value = data.data.addressList || []
      tableDataList.forEach((item) => {
        item.addressList = JSON.parse(JSON.stringify(data.data.addressList))
      })
      OrderData.value = JSON.parse(JSON.stringify(data.data))
      data.data.addressList?.forEach((item) => {
        numMax.value += sumArray(item.goodsList)
      })
      if (isHistoryOrderPage.value) {
        //移除操作列
        deliveryColumns.pop()
      }
    })
}
let moneyNum = ref(0)

function spiltOrder() {
  tableDataList.forEach((item) => {
    item.addressList.forEach((item1) => {
      moneyNum.value += sumArray(item1.goodsList)
    })
  })
  if (moneyNum.value > numMax.value) {
    // console.log(moneyNum.value, numMax.value, 'moneyNum')
    moneyNum.value=0
    return message.warning('子订单件数之和大于原订单，请重新输入')
  }

  message.confirm('确认拆单?', '拆单提示').then(() => {
    tms_web
      .transportOrder_spiltOrder_post({
        data: {
          orderId: +route.query.id,
          dtoList: tableDataList
        }
      })
      .then((res) => {
        // console.log(res, 'res')
        message.success('操作成功')
        router.back()
        // gridRef.value?.clearCheckboxRow()
        // getList()
      })
  })
}
const errorColumns = [
  {
    title: '上报时间',
    key: 'declareTime',
    minWidth: 150,
    render(h: any, params: any): any {
      return h('span', dateFormatterByOptions({ cellValue: params.row.declareTime }))
    }
  },
  {
    title: '异常类型',
    key: 'reportType',
    minWidth: 100,
    render(h: any, params: any) {
      return h('span', getDictLabel(DICT_TYPE.REPORT_TYPE, params.row.reportType))
    }
  },
  {
    title: '异常描述',
    key: 'faultRemark',
    minWidth: 180
  },
  {
    title: '处理状态',
    key: 'status',
    minWidth: 150,
    render(h: any, params: any) {
      return h('span', getDictLabel(DICT_TYPE.DEAL_REPORT_STATUS, params.row.status))
    }
  },
  {
    title: '处理时间',
    key: 'dealTime',
    minWidth: 150,
    render(h: any, params: any): any {
      return h('span', dateFormatterByOptions({ cellValue: params.row.dealTime }))
    }
  },
  {
    title: '处理人',
    key: 'dealUserName',
    minWidth: 150
  },
  {
    title: '现场图片',
    key: 'urls',
    render(h: any, params: any): any {
      return h(
        'span',
        {
          class: 'text-[#2D57CC] cursor-pointer',
          on: {
            click: () => {
              //查询文件列表
              tms_web
                .accidentFaultDamageReport_queryImage_post({
                  data: {
                    businessId: params.row.id
                  },
                  load: {
                    fullLoading: true
                  }
                })
                .then((data) => {
                  if (data.data && data.data.length) {
                    imgListViewRef.value?.open(data.data)
                  } else {
                    message.warning('暂无图片')
                  }
                })
              //
            }
          }
        },
        '查看图片'
      )
    }
  }
]

const errorList = ref<
  GetApiResByList<typeof tms_web.accidentFaultDamageReport_queryAccidentDeclaration_post>
>([])

function queryErrorList() {
  tms_web
    .accidentFaultDamageReport_queryAccidentDeclaration_post({
      data: {
        orderId: route.query.id as unknown as number
      }
    })
    .then((data) => {
      errorList.value = data?.data?.list || []
    })
}

//货检信息
const checkColumns = [
  {
    title: '检查阶段',
    key: 'name',
    minWidth: 150
  },
  {
    title: '描述',
    key: 'remark',
    minWidth: 100
  },
  {
    title: '操作',
    key: 'urls',
    render(h: any, params: any): any {
      return h(
        'span',
        {
          class: 'text-[#2D57CC] cursor-pointer',
          on: {
            click: () => {
              //文件列表
              if (params.row && params.row.fileList && params.row.fileList.length) {
                imgListViewRef.value?.open(params.row.fileList)
              } else {
                message.warning('暂无图片')
              }
            }
          }
        },
        '查看图片'
      )
    }
  }
]

const checkList = ref<any>([])

const modelsQueryList = ref<GetApiResByList<typeof tms_web.vehicleSpecifications_queryModels_post>>(
  []
)
async function queryCarModelsQuery() {
  const data = await tms_web.vehicleSpecifications_queryModels_post({
    load: { fullLoading: true }
  })
  modelsQueryList.value = data?.data?.list || []
}

const lengthQueryList = ref<GetApiResByList<typeof tms_web.vehicleSpecifications_queryLength_post>>(
  []
)
async function queryCarLengthQuery() {
  const data = await tms_web.vehicleSpecifications_queryLength_post({
    load: { fullLoading: true }
  })
  lengthQueryList.value = data?.data?.list || []
}
function queryCheckList() {
  tms_web
    .transportOrder_queryCheckList_post({
      data: {
        orderId: route.query.id as unknown as number
      }
    })
    .then((data) => {
      // console.log(data, '拆单详情')
      checkList.value = data.data || []
    })
}
// 车辆规格联选
const options = computed(() => {
  return modelsQueryList.value.map((item) => {
    return {
      value: item.id,
      label: item.name,
      children: lengthQueryList.value.map((item2) => {
        return {
          value: item2.id,
          label: item2.carLength + '米'
        }
      })
    }
  })
})
// 选择车辆调度开关
const carDUflag = ref(false)
onMounted(() => {
  queryCarModelsQuery()
  queryCarLengthQuery()
  getcarType()
  getInfo()
  driverList()
  if (isWaybillInfoPage.value) {
    queryCheckList()
    queryErrorList()
  }
})
</script>

<style scoped lang="scss">
.car_du {
  position: absolute;
  right: 5%;
  top: 0px;
}
.form_input {
  width: 200px;
  height: 36px;
  background: #ffffff;
  border-radius: 4px 4px 4px 4px;
}
.add_Table {
  width: 119px;
  height: 36px;
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #2d57cc;
}
.table_input {
  width: 140px;
  height: 36px;
  background: #ffffff;
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #dadee5;
}
.sunorder_number {
  padding: 10px;
  width: 100%;
  height: 38px;
  background: #e8ecf2;
  border-radius: 0px 0px 0px 0px;
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
  span {
    font-family:
      Source Han Sans SC,
      Source Han Sans SC;
    font-weight: bold;
    font-size: 13px;
    color: #333333;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .del_sunorder {
    font-family:
      Source Han Sans SC,
      Source Han Sans SC;
    font-weight: 400;
    font-size: 13px;
    color: #e04f48;
    text-align: left;
    font-style: normal;
    text-transform: none;
    cursor: pointer;
  }
}
:deep(.el-table) {
  color: #505050;
}

:deep(.el-table .cell) {
  line-height: 40px !important;
  height: 40px !important;
}

:deep(.el-table__header-wrapper, .el-table__footer-wrapper) {
  border-radius: 5px;
}

:deep(.el-table__row--striped) {
  background: rgba(246, 247, 251, 1) !important;

  td {
    background: unset !important;
  }
}
:deep(.el-table__row > td) {
  /* 去除表格线 */
  border-left: none;
  border-right: none;
}

:deep(.has-gutter tr th .cell) {
  background-color: #fff;
}
:deep(.el-pagination) {
  .btn-prev,
  .btn-next {
    background: transparent;
  }

  .number.active {
    background: var(--el-color-primary);
    color: #ffffff;
    border-radius: 5px;
  }

  .el-pagination__rightwrapper {
    height: 60px;
    line-height: 60px;
    display: flex;
    align-items: center;

    .el-pager {
      display: flex;
      align-items: center;

      li {
        height: 24px;
        min-width: 24px;
        min-height: 24px;
      }
    }
  }
}
:deep(.el-table__body) {
  .el-table__row {
    > td {
      padding: 0;
      font-size: 13px;
      height: 40px;
      line-height: 40px;

      > .cell {
        font-weight: 400;
        color: #333333;
        //padding: 0;
        height: 40px;
        line-height: 40px;
      }
    }

    //td:first-child{
    //    padding: 0 14px;
    //}
  }
}
:deep(.el-table__header-wrapper),
:deep(.el-table__fixed-header-wrapper) {
  //覆盖表头样式,自定义的表头填充满整个空间方便定制
  thead th {
    height: 40px;
    //flex-grow: 1;
    padding: 0;
    font-size: 13px;
    font-weight: bold;
    color: #000000;
    border-right-color: #dbdbdb;
    background: #e8ecf2;

    > .cell {
      //padding: 0;
      display: flex;
      align-items: center;
      background: #f0f1f5;

      > div {
        height: 40px;
        line-height: 40px;
      }
    }
  }
}
</style>
