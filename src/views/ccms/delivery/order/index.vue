<template>
  <!-- 列表 -->
  <ContentWrap class="full-height">
    <vxe-grid ref="gridRef" v-bind="gridOptions" v-on="gridEvents">
      <template #tools>
        <!-- <el-tooltip
          class="item"
          effect="dark"
          :disabled="canMerge"
          content="同一个客户、地址并且运输要求相同时，待接单并且未约单的订单可以选择合单操作，请勾选两条以上列表数据后继续操作"
          placement="top"
        >
      </el-tooltip> -->
        <!-- :disabled="!canMerge" -->
        <vxe-button
          v-hasPermi="['ccms:transportOrder:mergeOrder']"
          status="primary"
          @click="toMergePage"
          icon="vxe-icon-merge-cells"
          >合单调度
        </vxe-button>
        <vxe-button @click="handelSearch" status="info" icon="vxe-icon-search" />
      </template>
      <template #toolbar_buttons>
        <vxe-radio-group v-model="queryParams.orderStatus" @change="getList">
          <vxe-radio-button label="all">所有订单</vxe-radio-button>
          <vxe-radio-button label="not">待接单</vxe-radio-button>
          <vxe-radio-button label="yes">已接单</vxe-radio-button>
          <vxe-radio-button label="cancel">已取消</vxe-radio-button>
        </vxe-radio-group>
      </template>
      <template #status="{ row }">
        <dict-tag :type="DICT_TYPE.ASSETS_STATUS" :value="row.status" />
      </template>
      <template #orderNumber="{ row }">
        {{ row.orderNumber }}
        <span v-if="row.mergeOrder === 'y'" class="ml-1 font-bold text-[#2D57CC]">合并</span>
        <span v-else-if="row.splitOrder === 'y'" class="ml-1 font-bold text-[#36B336]">拆分</span>
      </template>
      <template #transportRequire="{ row }">
        <CustomStatus
          :title="getDictLabel(DICT_TYPE.TRANSPORT_REQUIRE, row.transportRequire)"
          :color="colorConfig[row.transportRequire]"
        />
      </template>
      <template #orderStatus="{ row }">
        <CustomStatus
          :title="getDictLabel(DICT_TYPE.TRANSPORT_ORDER_STATUS, row.orderStatus)"
          :color="OrdercolorConfig[row.orderStatus]"
        />
      </template>
      <template #operate="{ row }">
        <el-button type="primary" size="small" @click="toInfoPage('info', row)" plain
          >详情</el-button
        >
        <el-button
          v-if="isMergeAndSplit(row)"
          type="warning"
          size="small"
          @click="SelectCarOpenRef?.open(row.id)"
          v-hasPermi="['ccms:transportOrder:carDispatch']"
          plain
          >车辆调度
        </el-button>
        <el-button
          v-if="isMergeAndSplit(row)"
          type="warning"
          size="small"
          @click="toInfoPage('spilt', row)"
          v-hasPermi="['ccms:transportOrder:spiltOrder']"
          plain
          >拆单
        </el-button>
        <el-button
          v-if="row.orderNumber.slice(-1) === '合'"
          type="danger"
          size="small"
          @click="toInfoPage('mergeInfo', row)"
          v-hasPermi="['ccms:transportOrder:cancelMergeOrder']"
          plain
          >撤销合单
        </el-button>
        <el-button
          v-if="row.splitOrder === 'y'"
          type="danger"
          size="small"
          @click="toInfoPage('spiltInfo', row)"
          v-hasPermi="['ccms:transportOrder:spiltOrder']"
          plain
          >撤销拆单
        </el-button>
      </template>
    </vxe-grid>
  </ContentWrap>

  <!-- 搜索抽屉 -->
  <SearchDrawer ref="searchRef" v-model="queryParams" @search="getList" />
  <SelectCarOpen ref="SelectCarOpenRef" @on-save="carsubmit" />
</template>

<script setup lang="ts">
import type { VxeGridInstance, VxeGridListeners, VxeGridProps } from 'vxe-table'
import SearchDrawer from './SearchDrawer.vue'
import { tms_web } from '@/api/ccms'
import { DICT_TYPE, getDictLabel } from '@/utils/dict'
import CustomStatus from '@/components/CustomStatus.vue'
import { cloneDeep } from 'lodash-es'
import SelectCarOpen from './SelectCarOpen.vue'
import { dateFormatterByOptions } from '@/utils/formatTime'

defineOptions({ name: 'DeliveryOrder' })
onActivated(() => {
  getList()
})
const SelectCarOpenRef = ref<InstanceType<typeof SelectCarOpen>>()

const message = useMessage() // 消息弹窗

const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  orderStatus: 'all',
  customerName: undefined,
  sendPhone: undefined,
  orderStartTime: undefined,
  orderEndTime: undefined
})

const searchRef = ref()
// 表格实例
const gridRef = ref<VxeGridInstance>()

// 调用vxe grid query方法重载表格
const getList = () => {
  if (gridRef.value) {
    gridRef.value.commitProxy('query') // 触发表格重新加载数据
  }
}
// 获取表格数据
const fetchApi = () => {
  return new Promise(async (resolve) => {
    const params: any = cloneDeep(queryParams)
    if (params.orderStatus === 'all') {
      delete params.orderStatus
    }
    const res = await tms_web.transportOrder_query_post({ data: params })

    gridOptions.data = res.data?.list || []
    resolve({
      page: {
        total: res.data?.total
      },
      result: res.data?.list
    })
  })
}

const colorConfig: any = {
  cold: '#2D57CC',
  normal: '#36B336'
}
const OrdercolorConfig: any = {
  not: '#D68711',
  yes: '#36B336',
  cancel: '#A8ABB2'
}

const selectedList = ref<any[]>([])

const canMerge = computed(() => {
  return selectedList.value.length > 1
})

const gridOptions = reactive<
  VxeGridProps<GetApiResByListItem<typeof tms_web.transportOrder_query_post>>
>({
  stripe: true,
  border: true,
  keepSource: true,
  height: '100%',
  columnConfig: {
    resizable: true
  },
  checkboxConfig: {
    checkMethod: ({ row }) => {
      if (!isMergeAndSplit(row)) {
        return false
      }
      //选择一项后只能选择和这一项客户名称,装货地址,运输要求完全相同的订单
      if (selectedList.value.length >= 1) {
        const tempItem = selectedList.value[0]
        return (
          row.customerName === tempItem.customerName &&
          row.sendProvince === tempItem.sendProvince &&
          row.sendCity === tempItem.sendCity &&
          row.sendCounty === tempItem.sendCounty &&
          row.sendAddress === tempItem.sendAddress &&
          row.transportRequire === tempItem.transportRequire
        )
      }
      return true
    }
  },
  pagerConfig: {
    enabled: true,
    pageSize: 10
  },
  editConfig: {
    trigger: 'click',
    mode: 'row',
    showStatus: true
  },
  toolbarConfig: {
    zoom: true,
    custom: true,
    slots: {
      buttons: 'toolbar_buttons',
      tools: 'tools'
    }
  },
  proxyConfig: {
    props: {
      result: 'result',
      total: 'page.total'
    },
    ajax: {
      // 接收 Promise
      query: ({ page }) => {
        queryParams.pageNo = page.currentPage
        queryParams.pageSize = page.pageSize
        return fetchApi()
      }
    }
  },
  columns: [
    { type: 'seq', width: 70 },
    {
      field: 'orderNumber',
      title: '订单号',
      minWidth: '230px',
      slots: {
        default: 'orderNumber'
      }
    },
    { field: 'orderTime', title: '下单时间', minWidth: '160px', formatter: dateFormatterByOptions },
    { field: 'customerName', title: '客户名称', minWidth: '130px' },
    { field: 'sendName', title: '联系人', minWidth: '100px' },
    { field: 'sendPhone', title: '联系电话', minWidth: '120px' },
    {
      field: 'transportRequire',
      title: '运输要求',
      minWidth: '100px',
      slots: {
        default: 'transportRequire'
      }
    },
    {
      field: 'orderStatus',
      title: '状态',
      minWidth: '100px',
      slots: {
        default: 'orderStatus'
      }
    },
    {
      field: 'transportType',
      title: '运输类型',
      minWidth: '100px',
      formatter: ({ cellValue }) => getDictLabel(DICT_TYPE.CAR_ROUTE_TYPE, cellValue)
    },
    {
      field: 'sendAddress',
      title: '装货地址',
      minWidth: '250px',
      formatter: ({ row }) => {
        return `${row.sendProvince || ''}${row.sendCity || ''}${row.sendCounty || ''}${row.sendAddress || ''}`
      }
    },
    { field: 'tempCalibration', title: '温度校准值', minWidth: '120px' },
    { field: 'humCalibration', title: '湿度校准值', minWidth: '120px' },
    // {
    //   field: 'periodStatus',
    //   title: '在线状态',
    //   minWidth: '120px'
    // },
    { title: '操作', width: 300, slots: { default: 'operate' }, fixed: 'right' }
  ],
  data: []
})

//是否可以合并拆分
function isMergeAndSplit(row: any) {
  return (
    row.orderStatus === 'not' &&
    row.mergeOrder !== 'y' &&
    row.splitOrder !== 'y' &&
    !row.transportCarDriverId
  )
}
// 车辆调度触发函数
const carsubmit = (item,id) => {
  for (const key in carqueryPrmas) {
    if (Object.prototype.hasOwnProperty.call(carqueryPrmas, key)) {
      if (key !== 'orderId') {
        carqueryPrmas[key] = item[key]
      } 
    }
  }
  carqueryPrmas.orderId=id
  carDispatch()
}
// 车辆调度参数对象
const carqueryPrmas = reactive({
  orderId: 0,
  carNumber: '',
  driverId: 0
})
function carDispatch() {
  message.confirm(`确认调度车辆${carqueryPrmas.carNumber}`, '车辆调度提示').then(() => {
    const query = { ...carqueryPrmas }
    tms_web
      .transportOrder_carDispatch_post({
        data: {
          ...query
        }
      })
      .then(() => {
        message.success('操作成功')
        // gridRef.value?.clearCheckboxRow()
        getList()
      })
  })
}

const gridEvents: VxeGridListeners = {
  checkboxChange() {
    selectedList.value = gridRef.value?.getCheckboxRecords() || []
  },
  checkboxAll() {
    selectedList.value = gridRef.value?.getCheckboxRecords() || []
  }
}

const handelSearch = () => {
  searchRef.value?.open()
}
const router = useRouter()
const toMergePage = () => {
  router.push({
    path: 'order/mergeOrder'
    // query: {
    //   type: 'view',
    //   id: rowData?.id
    // }
  })
}
const toInfoPage = (type: any, rowData?: Required<typeof gridOptions>['data'][number]) => {
  switch (type) {
    case 'info':
      router.push({
        path: 'order/info',
        query: {
          type: 'view',
          id: rowData?.id
        }
      })
      break
    case 'mergeInfo':
      router.push({
        path: 'order/mergeInfo',
        query: {
          type: 'view',
          id: rowData?.id
        }
      })
      break
    case 'spiltInfo':
      router.push({
        path: 'order/spiltInfo',
        query: {
          type: 'view',
          id: rowData?.id
        }
      })
      break
    case 'spilt':
      router.push({
        path: 'order/spiltOrder',
        query: {
          type: 'view',
          id: rowData?.id
        }
      })

      break
    default:
      break
  }
}
</script>
