<script lang="ts" setup>
import CustomTagStatus from '@/components/CustomTagStatus.vue'
import { getDictLabel,DICT_TYPE } from '@/utils/dict'

const colorConfig = {
  not: '#D68711',
  yes: '#36B336',
  cancel: '#E04F48'
}

const props = defineProps<{
  name: string
}>()
</script>

<template>
  <custom-tag-status
    :color="colorConfig[props.name] || '#2D57CC'"
    :name="getDictLabel(DICT_TYPE.TRANSPORT_ORDER_STATUS, props.name)"
  />
</template>

<style scoped lang="scss"></style>
