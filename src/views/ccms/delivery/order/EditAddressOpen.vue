<template>
  <OpenComponents ref="openRef" @on-save="save" title="修改卸货地址" :width="500" append-to-body>
    <el-form :model="formData" :rules="rules" ref="formValidateRef" label-width="140px">
      <el-form-item class="type" label="配送地址：" prop="province">
        <SelectProvincialAndCities v-model:modelValue="provincialAndCities" />
      </el-form-item>
      <el-form-item label="详细地址：" prop="address">
        <el-input v-model="formData.address" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="收货人：" prop="receiverName">
        <el-input v-model="formData.receiverName" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="收货人联系电话：" prop="receiverPhone">
        <el-input v-model="formData.receiverPhone" placeholder="请输入" />
      </el-form-item>
    </el-form>
  </OpenComponents>
</template>

<script lang="ts" setup>
import OpenComponents from '@/components/Open.vue'
import { ElForm } from 'element-plus'
import { computed, ref } from 'vue'
import SelectProvincialAndCities from '@/components/selectProvincialAndCities/SelectProvincialAndCities.vue'
import { tms_web } from '@/api/ccms'

const props = defineProps<{
  isLocal?: boolean
}>()
const openRef = ref<InstanceType<typeof OpenComponents>>()
const formValidateRef = ref<InstanceType<typeof ElForm>>()

const emits = defineEmits<{
  (e: 'on-save', saveData: any, index: number): void
}>()

const formData = ref<any>({})

const provincialAndCities = computed({
  get() {
    const { province, city, county } = formData.value
    return {
      province: province || '',
      city: city || '',
      county: county || ''
    }
  },
  set(data) {
    formData.value = { ...formData.value, ...data }
  }
})

const index = ref(0)

const rules = {
  province: [{ required: true, message: '请选择配送地址', trigger: 'change' }],
  address: [{ required: true, message: '请输入详细地址', trigger: 'blur' }],
  receiverName: [{ required: true, message: '请输入收货人', trigger: 'blur' }],
  receiverPhone: [{ required: true, message: '请输入收货人联系电话', trigger: 'blur' }]
}

// 提交表单数据
function save() {
  formValidateRef.value?.validate((valid: any) => {
    if (valid) {
      saveFun()
    }
  })
}

const message = useMessage()

async function saveFun() {
  if (!props.isLocal) {
    await tms_web.transportOrder_changeReceiverAddress_post({
      data: {
        ...formData.value
      }
    })
    message.success('操作成功')
  }
  emits('on-save', formData.value, index.value)
  openRef.value?.close()
}

// 弹窗打开
function open(rowData: any, rowIndex: number) {
  openRef.value?.open()
  formData.value = JSON.parse(JSON.stringify(rowData))
  index.value = rowIndex
}

defineExpose({
  open
})
</script>

<style lang="scss" scoped></style>
