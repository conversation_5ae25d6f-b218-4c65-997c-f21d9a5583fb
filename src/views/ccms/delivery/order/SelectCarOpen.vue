<template>
  <OpenComponents
    className="open-padding-0"
    ref="openRef"
    @on-save="saveFun"
    title="选择车辆"
    :width="1100"
    append-to-body
  >
    <template #title>
      <el-tabs class="open-tabs w-[300px]" stretch v-model="carType" @tabChange="queryList()">
        <el-tab-pane label="所有车辆" name="all" />
        <el-tab-pane label="自营车辆" name="person" />
        <!-- <el-tab-pane label="承运商车辆" name="carrier" /> -->
      </el-tabs>
    </template>
    <div class="car_boxMax">
      <div class="item_list">
        <el-input
          class="top_search"
          @change="SearchcancegFn"
          v-model="queryParams.carNumber"
          size="large"
          placeholder="请输入车牌号码"
        >
       <template #append>
        <el-button @click="SearchSubmitFn" :icon="Search" />
      </template>
      </el-input>
        <div class="but_list">
          <div
            class="item_box"
            @click="selectActiveFn(item)"
            :class="item.id === active ? 'active' : ''"
            v-for="(item, index1) in carrierList"
            :key="index1"
          >
            <div class="car_number_div">
              <div class="car_index">{{ index1 + 1 }}</div>
              <div class="car_number">{{ item.carNumber }}</div>
              <div class="car_type">{{
                getDictLabel('order_transport_type', item.type) || '-'
              }}</div>
            </div>
            <div class="card_div">
              <span class="card_box">{{
                getDictLabel(DICT_TYPE.VEHICLE_KIND, item.carCategory)
              }}</span>
              <span class="card_box">{{ item.carLength }}米</span>
              <span class="card_box">{{ getDictLabel('energy_type', item.energyType) }}</span>
            </div>
            <div class="info_div">
              <div class="info_box">
                <div class="info_title">核定载重</div>
                <div class="info_text">{{ item.totalWeight || '-' }}</div>
              </div>
              <div class="info_box">
                <div class="info_title">体积</div>
                <div class="info_text">{{
                  (item.vehicleHeight * item.vehicleLen * item.vehicleWidth || '-') + 'm³'
                }}</div>
              </div>
              <div class="info_box">
                <div class="info_title">当前状态</div>
                <div class="info_text">-</div>
              </div>
            </div>
            <div class="name__and_address">
              <div class="name_and_number">
                {{ item.driverName + '(' + item.driverPhone + ')' }}
              </div>
              <div class="address_number"> 当前距离：{{ '-' }}Km </div>
            </div>
          </div>
        </div>
      </div>
      <div id="containerdilog" class="gd_map"></div>
    </div>
  </OpenComponents>
</template>

<script lang="ts" setup>
import OpenComponents from '@/components/Open.vue'
import { ElForm } from 'element-plus'
import { computed, ref } from 'vue'
import { tms_web } from '@/api/ccms'
import { cloneDeep } from 'lodash-es'
import { getDictOptions, DICT_TYPE, getDictLabel } from '@/utils/dict'
import { Search } from '@element-plus/icons-vue'
const input1 = ref('')
const openRef = ref<InstanceType<typeof OpenComponents>>()
const formValidateRef = ref<InstanceType<typeof ElForm>>()

const emits = defineEmits<{
  (e: 'on-save', saveData: {},index:any ): void
}>()
// 地图初始化函数
const GDmapInit = () => {
  var map = new AMap.Map('containerdilog', {
    zoom: 10, //设置地图显示的缩放级别
    center: [116.397428, 39.90923], //设置地图中心点坐标
    viewMode: '2D', //设置地图模式
    lang: 'zh_cn' //设置地图语言类型
  })
}
// 搜索
const SearchSubmitFn=()=>{
  queryCarrierList()
}
const SearchcancegFn=(str)=>{
  if(str==''){
  queryCarrierList()
  }
}
// 点击选择卡片函数
const active = ref('')
const activeObj = ref({})
const selectActiveFn = (item) => {
  active.value = item.id
  activeObj.value = item
}
const selectedIndexList = ref<number[]>([])

function selectedItem(index: number) {
  if (selectedIndexList.value.includes(index)) {
    selectedIndexList.value = selectedIndexList.value.filter((item) => item !== index)
  } else {
    selectedIndexList.value.push(index)
  }
}

const carType = ref('all')

const index = ref(0)
const queryParams=ref({
  carNumber:'',
})
//承运商
const carrierList = ref<GetApiResByList<typeof tms_web.carrier_query_post>>([])
function queryCarrierList() {
  const query={...queryParams.value}
  tms_web
    .car_query_post({
      data: {
        ...query,
        pageNo: 1,
        pageSize: 99
      }
    })
    .then((res) => {
      // console.log(res, '车辆列表')
      carrierList.value = res.data?.list || []
    })
}

const carModelAndLength = ref<any[]>([])
const modelsQueryList = ref<GetApiResByList<typeof tms_web.vehicleSpecifications_queryModels_post>>(
  []
)

async function queryCarModelsQuery() {
  const data = await tms_web.vehicleSpecifications_queryModels_post({
    load: { fullLoading: true }
  })
  modelsQueryList.value = data?.data?.list || []
}

const lengthQueryList = ref<GetApiResByList<typeof tms_web.vehicleSpecifications_queryLength_post>>(
  []
)

async function queryCarLengthQuery() {
  const data = await tms_web.vehicleSpecifications_queryLength_post({
    load: { fullLoading: true }
  })
  lengthQueryList.value = data?.data?.list || []
}

const options = computed(() => {
  return modelsQueryList.value.map((item) => {
    return {
      value: item.id,
      label: item.name,
      children: lengthQueryList.value.map((item2) => {
        return {
          value: item2.id,
          label: item2.carLength + '米'
        }
      })
    }
  })
})
const message = useMessage()

async function saveFun() {
  if (!activeObj.value.id) {
    message.warning('请选择车辆！')
    return
  }
  // const params: any[] = []
  // selectedIndexList.value.forEach((item) => {
  //   params.push(list.value[item])
  // })
  emits('on-save', activeObj.value,index.value)
  openRef.value?.close()
}

const query = ref<any>({
  pageNo: 1,
  pageSize: 9
})

const total = ref(0)
const list = ref<any[]>([])

function queryList(pageNo = 1) {
  query.value.pageNo = pageNo
  const params: any = cloneDeep(query.value)
  if (carType.value === 'all') {
    params.notType = 'person'
  } else {
    params.type = carType.value
  }
  if (carModelAndLength.value.length) {
    params.carModelId = carModelAndLength.value[0]
    params.carLengthId = carModelAndLength.value[1]
  }
  tms_web
    .car_query_post({
      data: params
    })
    .then((res) => {
      list.value = res?.data?.list || []
      total.value = res?.data?.total || 0
    })
}

function reset() {
  query.value = {
    pageNo: 1,
    pageSize: 9
  }
  carType.value = 'all'
  selectedIndexList.value = []
}
onMounted(() => {})
// 弹窗打开
function open(rowIndex: number) {
  setTimeout(() => {
    // 初始化高德地图
    GDmapInit()
  }, 100)
  openRef.value?.open()
  reset()
  queryCarrierList()
  queryCarModelsQuery()
  queryCarLengthQuery()
  queryList()
  index.value = rowIndex
}

defineExpose({
  open
})
</script>

<style lang="scss" scoped>
.car_boxMax {
  width: 100%;
  height: 100%;
  padding: 5px;
  box-sizing: border-box;
  // background-color: pink;
  display: flex;
  .item_list {
    height: 100%;
    overflow-x: hidden;
    .top_search {
      width: 300px;
      height: 36px;
      background: #ffffff;
      border-radius: 4px 4px 4px 4px;
      border: 1px solid #dadee5;
    }
    .but_list {
      width: 104%;
      height: 490px;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      overflow-y: scroll;
      .item_box {
        margin-top: 10px;
        width: 300px;
        height: 150px;
        background: #ffffff;
        border: 1px solid #dadee5;
        border-radius: 3px 3px 3px 3px;
        padding: 10px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        // background-color: skyblue;.
        .name__and_address {
          display: flex;
          justify-content: space-between;
          .name_and_number {
            font-family:
              Source Han Sans SC,
              Source Han Sans SC;
            font-weight: 400;
            font-size: 12px;
            color: #333333;
            text-align: left;
            font-style: normal;
            text-transform: none;
          }
          .address_number {
            font-family:
              Source Han Sans SC,
              Source Han Sans SC;
            font-weight: 400;
            font-size: 12px;
            color: #2d57cc;
            text-align: left;
            font-style: normal;
            text-transform: none;
          }
        }
        .info_div {
          width: 100%;
          height: 52px;
          background: #f5f7fa;
          display: flex;
          justify-content: space-around;
          padding: 8px;
          .info_box {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
          }
          .info_title {
            font-family:
              Source Han Sans SC,
              Source Han Sans SC;
            font-weight: 400;
            font-size: 12px;
            color: #999999;
            line-height: 10px;
            text-align: center;
            font-style: normal;
            text-transform: none;
          }
          .info_text {
            font-family:
              Source Han Sans SC,
              Source Han Sans SC;
            font-weight: bold;
            font-size: 13px;
            color: #333333;
            line-height: 12px;
            text-align: left;
            font-style: normal;
            text-transform: none;
          }
        }
        .card_div {
          display: flex;
          .card_box {
            text-align: center;
            min-width: 30px;
            padding: 6px;
            font-size: 12px;
            background: #ffffff;
            border-radius: 4px 4px 4px 4px;
            border: 1px solid #ebebeb;
            margin-right: 4px;
          }
        }
        .car_number_div {
          display: flex;
          align-items: center;
          .car_number {
            font-family:
              Source Han Sans SC,
              Source Han Sans SC;
            font-weight: bold;
            font-size: 13px;
            color: #333333;
            line-height: 12px;
            text-align: left;
            font-style: normal;
            text-transform: none;
            padding: 0 8px;
          }
          .car_type {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 28px;
            height: 16px;
            background: rgba(25, 190, 107, 0.1);
            border-radius: 3px 3px 3px 3px;
            font-family:
              Source Han Sans SC,
              Source Han Sans SC;
            font-weight: 400;
            font-size: 9px;
            color: #36b336;
            text-align: left;
            font-style: normal;
            text-transform: none;
          }
          .car_index {
            text-align: center;
            width: 20px;
            height: 20px;
            background: #c0c4cc;
            border-radius: 999px 999px 999px 999px;
          }
        }
      }
      .active {
        border: 1px solid #2d57cc;
      }
    }
  }
  .gd_map {
    margin-left: 20px;
    width: 740px;
    height: 519px;
  }
}
.select-car {
  :deep(.el-form-item__label) {
    padding: 0;
  }

  :deep(.el-form-item) {
    margin-bottom: 0 !important;
    margin-right: 10px;
    width: 200px;
  }

  .search-opt-button {
    :deep(.el-form-item__label) {
      opacity: 0;
    }
  }
}
</style>
