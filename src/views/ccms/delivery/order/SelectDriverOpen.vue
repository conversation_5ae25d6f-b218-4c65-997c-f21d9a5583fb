<template>
  <OpenComponents ref="openRef" @on-save="save" title="选择驾驶员" :width="500" append-to-body>
    <el-form :model="formData" :rules="rules" ref="formValidateRef" label-width="140px">
      <el-form-item v-if="isCarrier" label="所属承运商：" prop="carrierName">
        <el-input disabled v-model="formData.carrierName" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="选择驾驶员：" prop="selectedDriverId">
        <el-select
          class="w-full"
          v-model="formData.selectedDriverId"
          placeholder="请选择"
          filterable
          clearable
        >
          <el-option
            v-for="item in driverList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
    </el-form>
  </OpenComponents>
</template>

<script lang="ts" setup>
import OpenComponents from '@/components/Open.vue'
import { ElForm } from 'element-plus'
import { computed, ref } from 'vue'
import { tms_web } from '@/api/ccms'

const message = useMessage()

const openRef = ref<InstanceType<typeof OpenComponents>>()
const formValidateRef = ref<InstanceType<typeof ElForm>>()

const emits = defineEmits<{
  (e: 'on-save', saveData: any, carNumber: string): void
}>()

const formData = ref<any>({})

const rules = {}

// 提交表单数据
function save() {
  formValidateRef.value?.validate((valid: any) => {
    if (valid) {
      saveFun()
    }
  })
}

const isCarrier = computed(() => {
  return formData.value.type && formData.value.type.name === 'carrier'
})

const driverList = ref<any>([])

function queryDriverList() {
  const params: any = {}
  if (isCarrier.value) {
    params.carrierId = formData.value.carrierId
  } else {
    params.type = 'self'
  }
  tms_web
    .driver_query_post({
      data: params
    })
    .then((data) => {
      driverList.value = data.data || []
    })
}

async function saveFun() {
  if (!formData.value.selectedDriverId) {
    message.warning('请选择驾驶员！')
    return
  }
  const params = driverList.value.find((item: any) => {
    return item.id === formData.value.selectedDriverId
  })
  emits('on-save', params, formData.value.carNumber)
  openRef.value?.close()
}

// 弹窗打开
function open(rowData: any) {
  openRef.value?.open()
  formData.value = JSON.parse(JSON.stringify(rowData))
  queryDriverList()
}

defineExpose({
  open
})
</script>

<style lang="scss" scoped></style>
