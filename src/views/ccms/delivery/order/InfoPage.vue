<template>
  <div class="flex flex-col gap-5">
    <div class="flex items-center gap-[10px] rounded-[10px] bg-white p-5 leading-none">
      <div class="text-[18px] font-bold text-black">
        {{ isHistoryOrderPage ? '历史' : '' }}{{ isWaybillInfoPage ? '运单' : '订单' }}详情
      </div>
      <OrderStatus :name="info.orderStatus || ''" />
      <div class="flex cursor-pointer items-center gap-[6px] text-[13px] text-[#A8ABB2]">
        <Icon icon="tdesign:rollback" />
        <span @click="router.back()">返回</span>
      </div>
    </div>
    <div class="flex flex-col gap-[30px] rounded-[10px] bg-white p-10">
      <OrderStepsInfo :order-info="info"/>
      <InfoBlock name="客户信息">
        <div class="grid grid-cols-3">
          <InfoField name="客户名称" :text="info.customerName" />
          <InfoField
            name="下单时间"
            :text="dateFormatterByOptions({ cellValue: info.orderTime })"
          />
          <InfoField name="订单号" :text="info.orderNumber" />
        </div>
        <div class="grid grid-cols-3">
          <InfoField name="联系人" :text="info.sendName" />
          <InfoField name="联系电话" :text="info.sendPhone" />
        </div>
        <div class="grid grid-cols-3">
          <InfoField name="车辆类型" :text="info.carModelName" />
          <InfoField name="车辆规格"> {{ info.carLength || '' }}</InfoField>
        </div>
        <div class="grid grid-cols-3" v-if="sysType === 'ccms'">
          <InfoField name="运输要求">
            {{ getDictLabel(DICT_TYPE.TRANSPORT_REQUIRE, info.transportRequire) || '' }}
          </InfoField>
          <InfoField name="温度要求">
            {{ info.transportTemp ? info.transportTemp + '℃' : '' }}
          </InfoField>
          <InfoField name="湿度要求">
            {{ info.transportHum ? info.transportHum + '%RH' : '' }}
          </InfoField>
        </div>
        <div class="grid grid-cols-3">
          <InfoField
            name="预约时间"
            :text="dateFormatterByOptions({ cellValue: info.loadingTime })"
          />
        </div>
        <div class="grid grid-cols-3">
          <InfoField name="装货地址">
            <span>
              {{
                `${info.sendProvince || ''}${info.sendCity || ''}${info.sendCounty || ''}${info.sendAddress || ''}`
              }}
            </span>
            <span
              v-if="!isHistoryOrderPage"
              v-hasPermi="['ccms:transportOrder:update']"
              @click="editSendAddressOpenRef?.open(info)"
              class="ml-1 cursor-pointer text-[#2D57CC] underline underline-offset-1"
              >变更</span
            >
          </InfoField>
        </div>
      </InfoBlock>
      <InfoBlock name="配送信息" v-if="info.addressList">
        <custom-table
          :columns="deliveryColumns"
          :data="info.addressList || []"
          hide-paging
          :padding="0"
          show-index
        />
      </InfoBlock>
      <InfoBlock name="接单信息">
        <custom-table
          :columns="orderColumns"
          :data="orderList"
          hide-paging
          :padding="0"
          show-index
        />
      </InfoBlock>
      <InfoBlock name="异常处理记录" v-if="isWaybillInfoPage">
        <custom-table
          :columns="errorColumns"
          :data="errorList"
          hide-paging
          :padding="0"
          show-index
        />
      </InfoBlock>
      <InfoBlock name="货检信息" v-if="isWaybillInfoPage">
        <custom-table
          :columns="checkColumns"
          :data="checkList"
          hide-paging
          :padding="0"
          show-index
        />
      </InfoBlock>
      <div class="flex justify-center">
        <el-button
          type="warning"
          @click="selectCarOpenRef?.open(route.query.id)"
          v-hasPermi="['ccms:transportOrder:spiltOrder']"
          plain
          >车辆调度
        </el-button>
        <el-button @click="router.back()">返回</el-button>
      </div>
    </div>
    <SelectCarOpen ref="selectCarOpenRef" @on-save="saveSelectedCars" />
    <GoodsInfoOpen ref="goodsInfoRef" />
    <edit-address-open ref="editAddressOpenRef" @on-save="saveEditAddress" />
    <edit-send-address-open ref="editSendAddressOpenRef" @on-save="saveSendEditAddress" />
    <img-list-view ref="imgListViewRef" />
  </div>
</template>
<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue'
import { tms_web } from '@/api/ccms'
import InfoBlock from '@/components/infoBlock/InfoBlock.vue'
import InfoField from '@/components/infoBlock/InfoField.vue'
import OrderStepsInfo from "./OrderStepsInfo.vue"
import OrderStatus from './OrderStatus.vue'
import GoodsInfoOpen from './GoodsInfoOpen.vue'
import EditAddressOpen from './EditAddressOpen.vue'
import EditSendAddressOpen from './EditSendAddressOpen.vue'
import ImgListView from '@/components/ImgListView.vue'
import { getDictLabel, DICT_TYPE } from '@/utils/dict'
import CustomTable from '@/components/customTable/Index.vue'
import { hasPermission } from '@/directives/permission/hasPermi'
import { dateFormatterByOptions } from '@/utils/formatTime'
import { sysType } from '@/utils/sysType'
import SelectCarOpen from './SelectCarOpen.vue'
import { cloneDeep } from 'lodash-es'

const selectCarOpenRef= ref()
const message = useMessage() // 消息弹窗
const editAddressOpenRef = ref<InstanceType<typeof EditAddressOpen>>()
const editSendAddressOpenRef = ref<InstanceType<typeof EditSendAddressOpen>>()
const imgListViewRef = ref<typeof ImgListView>()
const VITE_SYS_TYPE = import.meta.env.VITE_SYS_TYPE

const route = useRoute()
const router = useRouter()
const isAudit = computed(() => {
  return route.query.audit === 'audit'
})
function saveSelectedCars(carList: any, index: number) {
  //直接修改info.value.addressList
  const addressList = cloneDeep(info.value.addressList)
  const temp = addressList[index]
  if (!temp.carList) {
    temp.carList = []
  }
  //车牌号已经存在时排除
  const carNumberList = temp.carList.map((item: any) => item.carNumber)
  carList = carList.filter((item: any) => !carNumberList.includes(item.carNumber))
  temp.carList.push(...carList)
  info.value.addressList = addressList
}
const info = ref<GetApiRes<typeof tms_web.transportOrder_get_post>>({} as any)

const isHistoryOrderPage = computed(() => {
  return (
    route.path.includes('/delivery/historyOrder/info') ||
    (info.value.mergeOrder && info.value.mergeOrder === 'y') ||
    (info.value.splitOrder && info.value.splitOrder === 'y')
  )
})

const isWaybillInfoPage = computed(() => {
  return route.path.includes('/delivery/waybill/info') || isHistoryOrderPage.value
})

const goodsInfoRef = ref<typeof GoodsInfoOpen.prototype>()

const deliveryColumns = [
  {
    title: '卸货地址',
    key: 'address',
    minWidth: 200,
    render(h: any, params: any) {
      return h(
        'span',
        `${params.row.province || ''}${params.row.city || ''}${params.row.county || ''}${params.row.address || ''}`
      )
    }
  },
  {
    title: '收货人',
    key: 'receiverName',
    minWidth: 130
  },
  {
    title: '收货人联系电话',
    key: 'receiverPhone',
    minWidth: 150
  },
  {
    title: '操作',
    render: (h: any, params: any) => {
      let btn: any[] = []
      btn.push(
        h('span', {
          domProps: {
            innerHTML: '商品信息'
          },
          class: 'cyan',
          on: {
            click: () => {
              goodsInfoRef.value?.open(params.row.goodsList)
            }
          }
        })
      )
      if (!isAudit.value && hasPermission(['ccms:transportOrder:update'])) {
        btn.push(
          h('span', {
            domProps: {
              innerHTML: '修改'
            },
            on: {
              click: () => {
                editDeliveryItemOpen(params.row, params.index)
              }
            }
          })
        )
      }
      return h(
        'div',
        {
          class: 'handleBtn'
        },
        btn
      )
    }
  }
]

function editDeliveryItemOpen(rowData: any, index: number) {
  editAddressOpenRef.value?.open(rowData, index)
}

function saveEditAddress(saveData: any, index: number) {
  const tempData = JSON.parse(JSON.stringify(info.value.addressList || []))
  tempData[index] = saveData
  info.value.addressList = tempData
}

function saveSendEditAddress(saveData: any) {
  let tempData = JSON.parse(JSON.stringify(info.value))
  tempData = { ...tempData, ...saveData }
  info.value = tempData
}

const orderColumns = [
  {
    title: '时间',
    key: 'acceptTime',
    minWidth: 150,
    render(h: any, params: any): any {
      return h('span', dateFormatterByOptions({ cellValue: params.row.acceptTime }))
    }
  },
  {
    title: '接单状态',
    key: 'orderStatus',
    minWidth: 100,
    render(h: any, params: any) {
      return h('span', getDictLabel(DICT_TYPE.TRANSPORT_ORDER_STATUS, params.row.orderStatus))
    }
  },
  {
    title: '车牌号',
    key: 'transportCarNumber',
    minWidth: 150
  },
  {
    title: '车辆类型',
    key: 'transportCarType',
    minWidth: 150,
    render(h: any, params: any) {
      return h('span', getDictLabel(DICT_TYPE.CAR_TYPE, params.row.transportCarType))
    }
  },
  {
    title: '车辆规格',
    key: 'transportCarLength',
    minWidth: 150,
    render(h: any, params: any): any {
      return h('span', params.row.transportCarLength ? params.row.transportCarLength + '米' : '-')
    }
  },
  {
    title: '驾驶员',
    key: 'transportDriverName',
    minWidth: 150
  },
  {
    title: '联系电话',
    key: 'transportDriverPhone',
    minWidth: 150
  }
]

const orderList = computed(() => {
  return [info.value]
})

function getInfo() {
  tms_web
    .transportOrder_get_post({
      data: {
        id: route.query.id as unknown as number
      }
    })
    .then((data) => {
      info.value = data.data || {}
      if (isHistoryOrderPage.value) {
        //移除操作列
        deliveryColumns.pop()
      }
    })
}

const errorColumns = [
  {
    title: '上报时间',
    key: 'declareTime',
    minWidth: 150,
    render(h: any, params: any): any {
      return h('span', dateFormatterByOptions({ cellValue: params.row.declareTime }))
    }
  },
  {
    title: '异常类型',
    key: 'reportType',
    minWidth: 100,
    render(h: any, params: any) {
      return h('span', getDictLabel(DICT_TYPE.REPORT_TYPE, params.row.reportType))
    }
  },
  {
    title: '异常描述',
    key: 'faultRemark',
    minWidth: 180
  },
  {
    title: '处理状态',
    key: 'status',
    minWidth: 150,
    render(h: any, params: any) {
      return h('span', getDictLabel(DICT_TYPE.DEAL_REPORT_STATUS, params.row.status))
    }
  },
  {
    title: '处理时间',
    key: 'dealTime',
    minWidth: 150,
    render(h: any, params: any): any {
      return h('span', dateFormatterByOptions({ cellValue: params.row.dealTime }))
    }
  },
  {
    title: '处理人',
    key: 'dealUserName',
    minWidth: 150
  },
  {
    title: '现场图片',
    key: 'urls',
    render(h: any, params: any): any {
      return h(
        'span',
        {
          class: 'text-[#2D57CC] cursor-pointer',
          on: {
            click: () => {
              //查询文件列表
              tms_web
                .accidentFaultDamageReport_queryImage_post({
                  data: {
                    businessId: params.row.id
                  },
                  load: {
                    fullLoading: true
                  }
                })
                .then((data) => {
                  if (data.data && data.data.length) {
                    imgListViewRef.value?.open(data.data)
                  } else {
                    message.warning('暂无图片')
                  }
                })
              //
            }
          }
        },
        '查看图片'
      )
    }
  }
]

const errorList = ref<
  GetApiResByList<typeof tms_web.accidentFaultDamageReport_queryAccidentDeclaration_post>
>([])

function queryErrorList() {
  tms_web
    .accidentFaultDamageReport_queryAccidentDeclaration_post({
      data: {
        orderId: route.query.id as unknown as number
      }
    })
    .then((data) => {
      errorList.value = data?.data?.list || []
    })
}

//货检信息
const checkColumns = [
  {
    title: '检查阶段',
    key: 'name',
    minWidth: 150
  },
  {
    title: '描述',
    key: 'remark',
    minWidth: 100
  },
  {
    title: '操作',
    key: 'urls',
    render(h: any, params: any): any {
      return h(
        'span',
        {
          class: 'text-[#2D57CC] cursor-pointer',
          on: {
            click: () => {
              //文件列表
              if (params.row && params.row.fileList && params.row.fileList.length) {
                imgListViewRef.value?.open(params.row.fileList)
              } else {
                message.warning('暂无图片')
              }
            }
          }
        },
        '查看图片'
      )
    }
  }
]

const checkList = ref<any>([])

function queryCheckList() {
  tms_web
    .transportOrder_queryCheckList_post({
      data: {
        orderId: route.query.id as unknown as number
      }
    })
    .then((data) => {
      checkList.value = data.data || []
    })
}

onMounted(() => {
  getInfo()
  if (isWaybillInfoPage.value) {
    queryCheckList()
    queryErrorList()
  }
})
</script>

<style scoped lang="scss"></style>
