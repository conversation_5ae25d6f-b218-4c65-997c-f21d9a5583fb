<template>
  <OpenComponents
    ref="openRef"
    title="查看详情"
    :width="800"
    append-to-body
    hide-save
    close-text="关闭"
  >
    <el-timeline class="w-full">
      <el-timeline-item
        v-for="(item, index) in dataList"
        :key="index"
        color="#36B336"
        center
        :timestamp="item.time"
        placement="top"
      >
        <div class="flex flex-col gap-5">
          <div class="text-[13px] text-[#333333]">
            {{ item.content }}
          </div>
          <div class="grid flex-1 grid-cols-7 gap-y-[20px]">
            <el-image
              v-for="(imgInfo, imgIndex) in item.imgList"
              :key="imgIndex"
              class="size-[100px] rounded-[10px]"
              :src="imgInfo"
              fit="cover"
              :preview-src-list="item.imgList"
              :initial-index="imgIndex"
            />
            <el-empty
              class="col-span-full"
              v-if="!item.imgList.length"
            />
          </div>
        </div>
      </el-timeline-item>
    </el-timeline>
    <el-empty v-if="!dataList.length"/>
  </OpenComponents>
</template>

<script lang="ts" setup>
import OpenComponents from "@/components/Open.vue";
import { ref } from "vue";
import { getDownloadPath } from "@/utils/apiUtil";
import { tms_web } from "@/api/ccms";

const openRef = ref<InstanceType<typeof OpenComponents>>();

const dataList = ref<
  {
    time: string;
    content: string;
    imgList: string[];
  }[]
>([]);

// 弹窗打开
function open(orderInfo: any, type: string) {
  openRef.value?.open();
  reset();
  console.log(orderInfo, type);
  let imgList: string[] = [];

  if (type === "已装货") {
    if (orderInfo.fileList) {
      imgList = orderInfo.fileList
        .filter((item: any) => item.type && item.type.name === "load_picture")
        .map((item: any) => {
          return getDownloadPath(item.address || "");
        });
    }
    dataList.value = [
      {
        time: orderInfo.driverLoadTime || "-",
        content: orderInfo.loadRemark || "-",
        imgList: imgList,
      },
    ];
  } else if (type === "已卸货") {
    if (orderInfo.fileList) {
      imgList = orderInfo.fileList
        .filter((item: any) => item.type && item.type.name === "unload_picture")
        .map((item: any) => {
          return getDownloadPath(item.address || "");
        });
    }
    dataList.value = [
      {
        time: orderInfo.driverUnloadTime || "-",
        content: orderInfo.unloadRemark || "-",
        imgList: imgList,
      },
    ];
  } else if (type === "配送中") {
    tms_web
      .transportOrder_queryCheck_post({
        data: {
          orderId: orderInfo.id,
        },
      })
      .then((data) => {
        console.log(data);
        dataList.value = (data.data || []).map((item) => {
          return {
            time: item.createTime || "-",
            content: item.remark || "-",
            imgList: (item.fileList || []).map((imgItem: any) => {
              return getDownloadPath(imgItem.address);
            }),
          };
        });
      });
  }
}

function reset() {
  dataList.value = [];
}

defineExpose({
  open,
});
</script>

<style lang="scss" scoped></style>
