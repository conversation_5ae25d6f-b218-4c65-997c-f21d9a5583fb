<script setup lang="tsx">
const props = defineProps<{
  stepName: string;
  stepStatus?: string;
  labelTime: string;
  time?: string;
  labelOther?: string;
  other?: string;
  isView?: boolean;
  isLast?: boolean;
}>();

const emits = defineEmits<{
  (e: "view-info", stepName: string): void;
}>();
</script>

<template>
  <div
    class="flex w-[230px] flex-shrink-0 flex-col gap-[30px] text-nowrap leading-none"
  >
    <div class="text-[14px] font-bold text-[#333333]">{{ props.stepName }}</div>
    <div class="flex items-center">
      <div
        :class="props.stepStatus === 'y' ? 'bg-[#36B336]' : 'bg-[#999999]'"
        class="size-[15px] rounded-full bg-[#36B336]"
      ></div>
      <div v-if="!props.isLast" class="h-[1px] flex-1 bg-[#DADEE5]"></div>
    </div>
    <div class="flex items-center text-[13px] text-[#333333]">
      <div class="text-[#999999]">{{ props.labelTime }}：</div>
      <div>{{ props.time || "-" }}</div>
    </div>
    <div
      class="flex items-center text-[13px] text-[#333333]"
      :style="{
        opacity: props.labelOther ? 1 : 0,
        pointerEvents: props.labelOther ? 'auto' : 'none',
      }"
    >
      <div class="text-[#999999]">{{ props.labelOther }}：</div>
      <div>
        <div
          v-if="props.isView && props.stepStatus === 'y'"
          class="cursor-pointer text-[13px] text-[#2D57CC] underline underline-offset-2"
          @click="emits('view-info', props.stepName)"
        >
          查看详情
        </div>
        <template v-else>{{ props.other || "-" }}</template>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
