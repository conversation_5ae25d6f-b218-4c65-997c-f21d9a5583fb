<script setup lang="tsx">
import { computed, onMounted, onUnmounted, ref, watch } from 'vue'
import { tms_web } from '@/api/ccms'
import BatteryDisplay from '@/components/BatteryDisplay.vue'
import * as echarts from 'echarts'
import dayjs from 'dayjs'
// import echarts from "echarts";
import { useOrderCarMap } from '@/hooks/trackQuery/useOrderCarMap'
import StepDom from './StepDom.vue'
import { cloneDeep } from 'lodash'
import ViewInfoOpen from './ViewInfoOpen.vue'
import { sysType } from '@/utils/sysType'
import { Avatar } from '@element-plus/icons-vue'
const props = defineProps<{
  orderId: string
  orderType: string
}>()

const orderInfo = ref<any>({})

const isMapViewInit = ref(false)

async function getOrderInfo() {
  clearInterval(timer)
  const data = await tms_web.transportOrder_get_post({
    data: {
      id: props.orderId
    }
  })
  console.log(data, '起点终点位置')

  orderInfo.value = data.data || {}

  //获取起点终点的坐标位置
  const start = `${orderInfo.value.sendProvince || ''}${orderInfo.value.sendCity || ''}${orderInfo.value.sendCounty || ''}${orderInfo.value.sendAddress}`
  const endList: string[] = []
  for (const item of orderInfo.value.addressList || []) {
    endList.push(`${item.province}${item.city}${item.county}${item.address}`)
  }
  setStartEndPointByAddress(start, endList)

  function loop() {
    queryMapData()
    queryTempHumRecords()
    queryBatteryList()
    queryCount()
  }

  //配送中的订单定时查询
  if (orderInfo.value.transportStatus && orderInfo.value.transportStatus.name === 'delivery') {
    timer = setInterval(loop, 5000)
  }
  loop()
}

function toInfoPage() {
  if (props.orderType === '实时轨迹') {
    window.open(
      window.location.origin + `/${sysType}/delivery/waybill/info?type=view&id=${props.orderId}`
    )
  } else {
    window.open(
      window.location.origin +
        `/${sysType}/delivery/historyOrder/info?type=view&id=${props.orderId}`
    )
  }
}

const chartType = ref('温度')

const mapContainerRef = ref<HTMLDivElement>()
const { setMapPoints, setStartEndPointByAddress } = useOrderCarMap(mapContainerRef)
console.log(mapContainerRef, 'mapContainerRef')

function queryMapData() {
  tms_web
    .assetsRecord_queryLatLngRecord_post({
      data: {
        orderId: props.orderId
      }
    })
    .then((data) => {
      console.log(data, '坐标数据')
      setMapPoints(data.data || [], isMapViewInit.value)
      isMapViewInit.value = true
    })
}

const tempHumRecords = ref<any>([])

const currentTempHum = computed(() => {
  return tempHumRecords.value[tempHumRecords.value.length - 1] || {}
})

function queryTempHumRecords() {
  tms_web
    .assetsRecord_queryTempHumRecords_post({
      data: {
        orderId: Number(props.orderId)
      }
    })
    .then((data) => {
      tempHumRecords.value = data.data || []
      updateChart()
      console.log(data, 'echarts图表数据')
    })
}

const countData = ref<any>({})

function queryCount() {
  tms_web
    .assetsRecord_count_post({
      data: {
        orderId: props.orderId
      }
    })
    .then((data) => {
      countData.value = data.data || {}
      console.log(data, '温度湿度最大值最小值平均值')
    })
}

let timer: any = null

watch(chartType, () => {
  updateChart()
})

const chartRef = ref<HTMLDivElement>()

let chartInstance: any

function updateChart() {
  //处理数据,按照24小时保存数据
  const tempHumData: any[] = []
  const xAxisData: string[] = []
  for (const item of tempHumRecords.value) {
    xAxisData.push(dayjs(item.collectTime as string).format('HH:mm'))
    tempHumData.push(chartType.value === '温度' ? item.tempValue : item.humValue)
  }

  const color = chartType.value === '温度' ? '#FF6600' : '#1678FF'
  chartInstance.setOption({
    color: [color],
    grid: {
      top: 50,
      left: 20,
      right: 20,
      bottom: 20
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: xAxisData,
      axisTick: {
        show: false
      },
      axisLine: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLine: {
        show: false
      }
    },
    series: [
      {
        name: chartType.value,
        type: 'line',
        symbol: 'none',
        connectNulls: true,
        smooth: true,
        data: tempHumData,
        markPoint: {
          symbolSize: 50,
          itemStyle: {
            color: color
          },
          data: [
            { type: 'max', name: 'Max' },
            { type: 'min', name: 'Min' }
          ]
        },
        lineStyle: {
          color: color,
          width: 3
        }
        //设定最高点和最低点
        /*markLine: {
          symbol: "none",
          symbolSize: 0,
          lineStyle: {
            color: "#E04F48",
          },
          data: [
            {
              x: 30,
              yAxis: 15,
            },
            {
              x: 30,
              yAxis: 5,
            },
          ],
        },*/
      }
    ]
  })
}

const batteryList = ref<any[]>([])

function queryBatteryList() {
  tms_web
    .transportOrder_queryOrderAssetList_post({
      data: {
        orderId: props.orderId
      }
    })
    .then((data) => {
      batteryList.value = data.data || []
      console.log(data, '设备状态列表')
    })
}

const VITE_SYS_TYPE = import.meta.env.VITE_SYS_TYPE

onUnmounted(() => {
  clearInterval(timer)
})

onMounted(() => {
  if (chartRef.value) {
    chartInstance = echarts.init(chartRef.value)
  }

  watch(
    () => props.orderId,
    () => {
      if (props.orderId) {
        isMapViewInit.value = false
        getOrderInfo()
      }
    },
    {
      immediate: true
    }
  )
})

const stepList = [
  {
    stepName: '已下单',
    stepStatus: 'y',
    labelTime: '订单时间',
    time: '',
    labelOther: '订单编号',
    other: ''
  },
  {
    stepName: '已接单',
    stepStatus: 'y',
    labelTime: '接单时间',
    time: '',
    labelOther: '车牌号码',
    other: ''
  },
  {
    stepName: '已装货',
    stepStatus: 'n',
    labelTime: '装货时间',
    time: '',
    labelOther: '货物检查',
    isView: true
  },
  {
    stepName: '配送中',
    stepStatus: 'n',
    labelTime: '配送时间',
    time: '',
    labelOther: '货物检查',
    isView: true
  },
  {
    stepName: '已卸货',
    stepStatus: 'n',
    labelTime: '卸货时间',
    time: '',
    labelOther: '货物检查',
    isView: true
  },
  {
    stepName: '确认卸货',
    stepStatus: 'n',
    labelTime: '确认时间',
    time: ''
  },
  {
    stepName: '已支付',
    stepStatus: 'n',
    labelTime: '支付时间',
    time: '2024-11-18 14:18:18'
  },
  {
    stepName: '已完成',
    stepStatus: 'n',
    labelTime: '完成时间',
    time: '2024-11-18 14:18:18'
  }
]

const resStepList = computed(() => {
  const tempList = cloneDeep(stepList)
  tempList[0].time = orderInfo.value.orderTime
  tempList[0].other = orderInfo.value.orderNumber
  tempList[1].time = orderInfo.value.transportTime
  tempList[1].other = orderInfo.value.transportNumber
  if (orderInfo.value.driverLoadStatus && orderInfo.value.driverLoadStatus.name === 'y') {
    tempList[2].time = orderInfo.value.driverLoadTime
    tempList[2].stepStatus = 'y'
  }

  if (orderInfo.value.customerLoadStatus && orderInfo.value.customerLoadStatus.name === 'y') {
    tempList[3].time = orderInfo.value.customerLoadTime
    tempList[3].stepStatus = 'y'
  }

  if (orderInfo.value.driverUnloadStatus && orderInfo.value.driverUnloadStatus.name === 'y') {
    tempList[4].time = orderInfo.value.driverUnloadTime
    tempList[4].stepStatus = 'y'
  }

  if (orderInfo.value.customerUnloadStatus && orderInfo.value.customerUnloadStatus.name === 'y') {
    tempList[5].time = orderInfo.value.customerUnloadTime
    tempList[5].stepStatus = 'y'
  }

  if (orderInfo.value.payStatus && orderInfo.value.payStatus.name === 'yes') {
    tempList[6].time = orderInfo.value.payTime
    tempList[6].stepStatus = 'y'
  }

  if (orderInfo.value.transportStatus && orderInfo.value.transportStatus.name === 'finish') {
    tempList[7].time = orderInfo.value.finishTime
    tempList[7].stepStatus = 'y'
  }

  return tempList
})

const viewInfoOpenRef = ref<any>()

function viewInfo(stepName: string) {
  viewInfoOpenRef.value?.open(orderInfo.value, stepName)
}
</script>

<template>
  <div class="scrollBar flex flex-1 flex-col gap-5 overflow-y-auto rounded-[10px] bg-white p-5">
    <div class="flex items-center gap-5 text-[12px]">
      <div class="flex items-center">
        <div class="text-[#999999]">运单号：</div>
        <div class="cursor-pointer text-[#2D57CC] underline" @click="toInfoPage">
          {{ orderInfo.transportNumber || '-' }}
        </div>
      </div>
      <div class="h-[9px] w-[1px] bg-[#C0C4CC]"></div>
      <div class="flex items-center">
        <div class="text-[#999999]">运单状态：</div>
        <div class="text-[#333333]">
          {{ (orderInfo.transportStatus && orderInfo.transportStatus.disName) || '-' }}
        </div>
      </div>
    </div>
    <div class="flex gap-5">
      <div
        class="flex h-[216px] w-[140px] flex-col items-center justify-center gap-4 overflow-hidden bg-[#F5F7FA] p-5"
      >
        <div
          class="flex size-[100px] flex-shrink-0 items-center justify-center rounded-full bg-[#C0C4CC]"
        >
          <el-icon size="50" color="#fff"> <Avatar /></el-icon>
          <!-- <i class="el-icon el-icon-s-custom text-[56px] text-[#FFFFFF]"></i> -->
        </div>
        <div class="flex flex-col items-center justify-center gap-1">
          <div class="w-[100px] truncate text-center text-[14px] font-bold text-[#333333]">
            {{ orderInfo.transportDriverName || '-' }}
          </div>
          <div class="w-[100px] truncate text-center text-[12px] text-[#333333]">
            {{ orderInfo.transportDriverPhone || '-' }}
          </div>
          <div
            class="flex h-[17px] items-center justify-center rounded-[2px] px-1 text-[12px] text-white"
            :class="
              orderInfo.transportCarType && orderInfo.transportCarType.name === 'normal'
                ? 'bg-[#D68711]'
                : 'bg-[#36B336]'
            "
          >
            {{ orderInfo.transportCarNumber || '-' }}
          </div>
        </div>
      </div>
      <div class="h-full w-[1px] bg-[#EBEBEB]"></div>
      <template v-if="sysType === 'ctms'">
        <div class="scrollBar flex flex-1 items-center overflow-x-auto bg-[#F5F7FA] p-[30px]">
          <StepDom
            v-for="(stepInfo, index) in resStepList"
            v-bind="stepInfo"
            :key="stepInfo.stepName"
            :is-last="index === stepList.length - 1"
            @view-info="viewInfo"
          />
        </div>
      </template>
      <template v-else>
        <div class="flex h-[216px] w-[88px] flex-col items-center gap-[10px] overflow-hidden">
          <div class="text-[20px] font-bold text-[#2D57CC]">
            {{ currentTempHum.tempValue || '-' }}<span class="ml-1 text-[12px] font-normal">℃</span>
          </div>
          <div class="flex items-center gap-1">
            <img class="size-[14px]" src="@/assets/images/<EMAIL>" alt="当前温度" />
            <span class="text-[12px] font-bold text-[#333333]">当前温度</span>
          </div>
          <div
            class="flex h-[48px] w-[88px] flex-col items-center justify-center rounded bg-[#2D57CC]/10"
          >
            <div class="text-[12px] font-bold text-[#2D57CC]">
              {{ countData.minTempValue || '-' }}℃
            </div>
            <div class="text-[12px] text-[#333333]">最低温度</div>
          </div>
          <div
            class="flex h-[48px] w-[88px] flex-col items-center justify-center rounded bg-[#2D57CC]/10"
          >
            <div class="text-[12px] font-bold text-[#2D57CC]">
              {{ countData.maxTempValue || '-' }}℃
            </div>
            <div class="text-[12px] text-[#333333]">最高温度</div>
          </div>
          <div
            class="scrollBar flex w-full flex-1 flex-col overflow-y-auto !overflow-x-hidden"
            style=""
          >
            <div class="flex items-center gap-1" v-for="(item, index) in batteryList" :key="index">
              <div class="text-nowrap text-[12px] text-[#999999]"> 设备{{ index + 1 }} </div>
              <BatteryDisplay :battery="item.batteryLevel" />
            </div>
          </div>
        </div>
        <div class="h-full w-[1px] bg-[#EBEBEB]"></div>
        <div class="flex h-[216px] w-[88px] flex-col items-center gap-[10px] overflow-hidden">
          <div class="text-[20px] font-bold text-[#2D57CC]">
            {{ currentTempHum.humValue || '-'
            }}<span class="ml-1 text-[12px] font-normal">%RH</span>
          </div>
          <div class="flex items-center gap-1">
            <img class="size-[14px]" src="@/assets/images/<EMAIL>" alt="当前湿度" />
            <span class="text-[12px] font-bold text-[#333333]">当前湿度</span>
          </div>
          <div
            class="flex h-[48px] w-[88px] flex-col items-center justify-center rounded bg-[#2D57CC]/10"
          >
            <div class="text-[12px] font-bold text-[#2D57CC]">
              {{ countData.minHumValue || '-' }}%RH
            </div>
            <div class="text-[12px] text-[#333333]">最低湿度</div>
          </div>
          <div
            class="flex h-[48px] w-[88px] flex-col items-center justify-center rounded bg-[#2D57CC]/10"
          >
            <div class="text-[12px] font-bold text-[#2D57CC]">
              {{ countData.maxHumValue || '-' }}%RH
            </div>
            <div class="text-[12px] text-[#333333]">最高湿度</div>
          </div>
          <div
            class="scrollBar flex w-full flex-1 flex-col overflow-y-auto !overflow-x-hidden"
            style=""
          >
            <div class="flex items-center gap-1" v-for="(item, index) in batteryList" :key="index">
              <div class="text-nowrap text-[12px] text-[#999999]"> 设备{{ index + 1 }} </div>
              <BatteryDisplay :battery="item.batteryLevel" />
            </div>
          </div>
        </div>
        <div class="h-full w-[1px] bg-[#EBEBEB]"></div>
        <div class="flex flex-1 flex-col gap-2">
          <div class="chart-title flex items-center justify-between">
            <div class="title flex items-center">
              <div class="rect"></div>
              <div class="text-[12px]">感应器变化趋势</div>
            </div>
            <el-radio-group v-model="chartType">
              <el-radio class="temperature" label="温度" />
              <el-radio label="湿度" />
            </el-radio-group>
          </div>
          <div class="flex-1" ref="chartRef"></div>
        </div>
      </template>
    </div>
    <div class="min-h-[300px] flex-1" ref="mapContainerRef"></div>
    <ViewInfoOpen ref="viewInfoOpenRef" />
  </div>
</template>

<style scoped lang="scss">
.chart-title {
  > .title {
    font-weight: bold;
    gap: 4px;

    .rect {
      width: 4px;
      height: 12px;
      background: #2d57cc;
      border-radius: 99px;
    }
  }

  :deep(.el-radio) {
    margin-right: 10px;
    font-size: 12px;

    &:last-child {
      margin-right: 0;
    }
  }

  :deep(.el-radio__input.is-checked .el-radio__inner) {
    background: #1678ff;
    border-color: #1678ff;
  }

  :deep(.el-radio__input.is-checked + .el-radio__label) {
    color: #1678ff;
  }

  .temperature {
    :deep(.el-radio__input.is-checked .el-radio__inner) {
      background: #ff6600;
      border-color: #ff6600;
    }

    :deep(.el-radio__input.is-checked + .el-radio__label) {
      color: #ff6600;
    }
  }
}
</style>
