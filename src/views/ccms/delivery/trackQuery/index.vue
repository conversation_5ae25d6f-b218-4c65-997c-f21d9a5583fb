<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { tms_web } from '@/api/ccms'
import OrderInfo from './OrderInfo.vue'
// import { useRoute } from "@/util/composition-helpers";
import { Search } from '@element-plus/icons-vue'
const orderPageNopageNo = ref(1)
const orderList = ref<any[]>([])
const orderListTotal = ref(0)
const orderPages = ref(0)

const liveOrderPageNopageNo = ref(1)
const liveOrderList = ref<any[]>([])
const liveOrderListTotal = ref(0)
const liveOrderPages = ref(0)

const resList = computed(() => {
  return queryType.value === '实时轨迹' ? liveOrderList.value : orderList.value
})

const loading = ref(true)

const orderId = ref('')
const waybillNumberOrCarNumber = ref('')

const queryType = ref('实时轨迹')

function clickTab(type: string) {
  queryType.value = type
  if (queryType.value === '实时轨迹') {
    queryLiveOrderList()
  } else {
    queryOrderList()
  }
}

function changeOrder(id: string) {
  orderId.value = id
}

function queryTypeCount() {
  tms_web
    .transportOrder_queryBaseInfo_post({
      data: {
        waybillNumberOrCarNumber: waybillNumberOrCarNumber.value,
        history: 'n',
        pageNo: 1,
        pageSize: 1
      }
    })
    .then((data) => {
      liveOrderListTotal.value = data.data?.total || 0
      liveOrderPages.value = data.pages || 0
    })
  tms_web
    .transportOrder_queryBaseInfo_post({
      data: {
        waybillNumberOrCarNumber: waybillNumberOrCarNumber.value,
        history: 'y',
        pageNo: 1,
        pageSize: 1
      }
    })
    .then((data) => {
      orderListTotal.value = data.data?.total || 0
      orderPages.value = data.pages || 0
    })
}

function queryOrderList(pageNo: number = 1) {
  orderPageNopageNo.value = pageNo
  loading.value = true
  tms_web
    .transportOrder_queryBaseInfo_post({
      data: {
        waybillNumberOrCarNumber: waybillNumberOrCarNumber.value,
        history: 'y',
        pageNo,
        pageSize: 20
      }
    })
    .then((data) => {
      console.log(data, '历史轨迹')

      if (orderPageNopageNo.value === 1) {
        orderList.value = data.data?.list || []
        //默认选中
        if (waybillNumberOrCarNumber.value) {
          for (const item of orderList.value) {
            if (item.transportNumber === waybillNumberOrCarNumber.value) {
              changeOrder(item.id)
            }
          }
        }
      } else {
        orderList.value = orderList.value.concat(data.data || [])
      }

      orderListTotal.value = data.data?.total || 0
      orderPages.value = data.pages || 0
    })
    .finally(() => {
      loading.value = false
    })
}

function queryLiveOrderList(pageNo: number = 1) {
  liveOrderPageNopageNo.value = pageNo
  tms_web
    .transportOrder_queryBaseInfo_post({
      data: {
        waybillNumberOrCarNumber: waybillNumberOrCarNumber.value,
        history: 'n',
        pageNo,
        pageSize: 20
      }
    })
    .then((data) => {
      console.log(data, '实时轨迹')

      if (liveOrderPageNopageNo.value === 1) {
        liveOrderList.value = data.data?.list || []

        //默认选中
        if (waybillNumberOrCarNumber.value) {
          for (const item of liveOrderList.value) {
            if (item.transportNumber === waybillNumberOrCarNumber.value) {
              changeOrder(item.id)
            }
          }
        }
      } else {
        liveOrderList.value = liveOrderList.value.concat(data.data || [])
      }

      liveOrderListTotal.value = data.data?.total || 0
      liveOrderPages.value = data.pages || 0
    })
    .finally(() => {
      loading.value = false
    })
}

function load() {
  if (loading.value) {
    return
  }
  if (queryType.value === '实时轨迹') {
    if (liveOrderPageNopageNo.value >= liveOrderPages.value) {
      return
    }
    queryLiveOrderList(liveOrderPageNopageNo.value + 1)
  } else {
    if (orderPageNopageNo.value >= orderPages.value) {
      return
    }
    queryOrderList(orderPageNopageNo.value + 1)
  }
}

onMounted(() => {
  const route = useRoute()
  waybillNumberOrCarNumber.value = route.query.orderNumber || ''
  queryTypeCount()
  clickTab(route && route.query.orderType === 'history' ? '历史轨迹' : '实时轨迹')
})
</script>

<template>
  <div class="page-content flex gap-5">
    <div  class="flex w-[280px] flex-shrink-0 flex-col gap-5 rounded-[10px] bg-white p-5">
      <el-input
        placeholder="运单号或车牌号"
        v-model="waybillNumberOrCarNumber"
        @keydown.enter="clickTab(queryType)"
      >
        <template #suffix>
          <div
          style="margin-right: 2px;margin-top: 2px;"
            class="mr-[5px] flex h-full cursor-pointer items-center"
            @click="clickTab(queryType)"
          >
          <el-button style="border: none;" :icon="Search" circle />
            <!-- <i class="el-icon el-icon-search text-[18px]"></i> -->
          </div>
        </template>
      </el-input>
      <div style="border-bottom:1px solid rgb(235, 235, 235);" class="grid grid-cols-2 text-[12px] text-[#333333] select-none">
        <div
          :class="queryType === '实时轨迹' ? 'border-b-[#2D57CC] text-[#2D57CC]' : ''"
          @click="clickTab('实时轨迹')"
          style="border-style: none;"
          class="cursor-pointer border-b border-solid border-b-[#EBEBEB] pb-[10px] text-center"
        >
          实时轨迹({{ liveOrderListTotal || 0 }})
        </div>
        <div
          :class="queryType === '历史轨迹' ? 'border-b-[#2D57CC] text-[#2D57CC]' : ''"
          @click="clickTab('历史轨迹')"
          style="border-style: none;"
          class="cursor-pointer border-b border-solid border-b-[#EBEBEB] pb-[10px] text-center"
        >
          历史轨迹({{ orderListTotal || 0 }})
        </div>
      </div>
      <div class="flex flex-1 flex-col overflow-hidden">
        <div class="flex h-[29px] items-center bg-[#EDEFF5] text-[12px] text-[#333333]">
          <div class="w-[150px] text-center">运单号</div>
          <div class="w-[90px] text-center">车牌号</div>
        </div>
        <div class="scrollBar flex-1 overflow-y-auto" v-infinite-scroll="load" v-loading="loading">
          <div
          style="justify-content: space-evenly;"
            v-for="(item, index) in resList"
            :key="index"
            @click="changeOrder(item.id)"
            :class="
              orderId === item.id ? '!border !border-[#2D57CC] bg-[#2D57CC]/10 text-[#2D57CC]' : ''
            "
            class="flex h-[29px] cursor-pointer items-center border border-solid border-[#2D57CC]/0 text-[12px] hover:border hover:border-[#2D57CC] hover:bg-[#2D57CC]/10 hover:text-[#2D57CC]"
          >
            <div class="w-[170px] text-nowrap px-[10px]">
              {{ item.transportNumber }}
            </div>
            <div class="w-[90px] text-nowrap px-[10px]">
              {{ item.transportCarNumber }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <OrderInfo v-if="orderId" :order-id="orderId" :order-type="queryType" />
    <div
      v-else
      class="flex flex-1 flex-col items-center justify-center gap-5 rounded-[10px] bg-white p-5"
    >
      <!-- <el-empty description="请选择运单后查看"/> -->
      <div class="map-bg">
        <span class="map-text">请选择左侧“运单号”或“车牌号”查询轨迹数据！</span>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.page-content {
  height: calc(100vh - 100px);
}
.map-bg {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-image: url(@/assets/images/Group.png);
  background-repeat: no-repeat;
  background-position: center;
  .map-text {
    font-size: 18px;
    color: #2d57cc;
  }
}
</style>
