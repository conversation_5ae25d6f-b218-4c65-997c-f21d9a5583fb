<template>
  <el-drawer v-model="drawerVisible" title="搜索" direction="rtl" size="350px">
    <template #header>
      <span class="text-16px font-700">搜索</span>
    </template>
    <div class="search-drawer" style="height: 100%">
      <el-form
        ref="queryFormRef"
        :inline="true"
        :model="queryParams"
        class="-mb-15px"
        label-width="68px"
      >
        <el-form-item label="客户名称" prop="customerName">
          <el-input
            v-model="queryParams.customerName"
            placeholder="请输入"
            clearable
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="运单号" prop="transportNumber">
          <el-input
            v-model="queryParams.transportNumber"
            placeholder="请输入"
            clearable
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="联系电话" prop="sendPhone">
          <el-input
            v-model="queryParams.sendPhone"
            placeholder="请输入"
            clearable
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="下单时间" prop="orderStartTime">
          <el-date-picker
            v-model="datePickerModel"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="startEndTime"
            class="!w-240px"
          />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div style="flex: auto">
        <el-button @click="reset">重置</el-button>
        <el-button type="primary" @click="search">搜索</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useQueryTime } from '@/hooks/useQueryTime'

// 定义 props 和 emits
const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update:modelValue', 'search', 'reset'])

// 控制抽屉显示
const drawerVisible = ref(false)

// 使用 computed 实现双向绑定
const queryParams = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const { datePickerModel, startEndTime } = useQueryTime(
  toRef(queryParams.value, 'orderStartTime'),
  toRef(queryParams.value, 'orderEndTime')
)

// 打开抽屉
function open() {
  drawerVisible.value = true
}

// 关闭抽屉
function close() {
  drawerVisible.value = false
}

// 搜索
function search() {
  close()
  emit('search', queryParams.value)
}

const queryFormRef = ref()

// 重置
function reset() {
  queryFormRef.value.resetFields()
  emit('reset', queryParams.value)
}

// 暴露 open 方法
defineExpose({
  open
})
</script>

<style lang="scss" scoped>
.search-drawer {
  ::v-deep {
    .el-form {
      height: 98%;
    }
  }
}
</style>
