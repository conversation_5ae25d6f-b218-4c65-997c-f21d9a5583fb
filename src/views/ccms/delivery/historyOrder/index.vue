<template>
  <!-- 列表 -->
  <ContentWrap class="full-height">
    <vxe-grid ref="gridRef" v-bind="gridOptions" v-on="gridEvents">
      <template #tools>
        <vxe-button @click="handelSearch" status="info" icon="vxe-icon-search" />
      </template>
      <template #transportStatus="{ row }">
        <CustomStatus
          :title="getDictLabel(DICT_TYPE.TRANSPORT_STATUS, row.transportStatus)"
          :color="transportStatusColorConfig[row.transportStatus]"
        />
      </template>
      <template #errorOrder="{ row }">
        <dict-tag :type="DICT_TYPE.BOOL_STATUS" :value="row.errorOrder" />
      </template>
      <template #carModelName="{ row }">
        <span>{{ `${row.carModelName || ''}/${row.carLength || ''}` }}</span>
      </template>
      <template #transportRequire="{ row }">
        <CustomStatus
          :title="getDictLabel(DICT_TYPE.TRANSPORT_REQUIRE, row.transportRequire)"
          :color="colorConfig[row.transportRequire]"
        />
      </template>
      <template #operate="{ row }">
        <el-button type="primary" size="small" @click="toInfoPage(row)" plain>详情</el-button>
        <el-button type="primary" size="small" @click="toPathPage(row)" plain>历史轨迹</el-button>
      </template>
    </vxe-grid>
  </ContentWrap>

  <!-- 搜索抽屉 -->
  <SearchDrawer ref="searchRef" v-model="queryParams" @search="getList" />
</template>

<script setup lang="ts">
import type { VxeGridInstance, VxeGridListeners, VxeGridProps } from 'vxe-table'
import SearchDrawer from './SearchDrawer.vue'
import { tms_web } from '@/api/ccms'
import { DICT_TYPE, getDictLabel } from '@/utils/dict'
import CustomStatus from '@/components/CustomStatus.vue'
import { cloneDeep } from 'lodash-es'
import { dateFormatterByOptions } from '@/utils/formatTime'
import { sysType } from '@/utils/sysType'

defineOptions({ name: 'DeliveryHistoryOrder' })

const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  transportNumber: undefined,
  customerName: undefined,
  sendPhone: undefined,
  orderStartTime: undefined,
  orderEndTime: undefined
})

const searchRef = ref()
// 表格实例
const gridRef = ref<VxeGridInstance>()

// 调用vxe grid query方法重载表格
const getList = () => {
  if (gridRef.value) {
    gridRef.value.commitProxy('query') // 触发表格重新加载数据
  }
}
// 获取表格数据
const fetchApi = () => {
  return new Promise(async (resolve) => {
    const params: any = cloneDeep(queryParams)
    params.transportStatus = 'finish'
    const res = await tms_web.transportOrder_query_post({ data: params })
    gridOptions.data = res.data?.list || []
    resolve({
      page: {
        total: res.data?.total
      },
      result: res.data?.list
    })
  })
}

const colorConfig: any = {
  cold: '#2D57CC',
  normal: '#36B336'
}

const gridOptions = reactive<
  VxeGridProps<GetApiResByListItem<typeof tms_web.transportOrder_query_post>>
>({
  stripe: true,
  border: true,
  keepSource: true,
  height: '100%',
  columnConfig: {
    resizable: true
  },
  pagerConfig: {
    enabled: true,
    pageSize: 10
  },
  editConfig: {
    trigger: 'click',
    mode: 'row',
    showStatus: true
  },
  toolbarConfig: {
    zoom: true,
    custom: true,
    slots: {
      tools: 'tools'
    }
  },
  proxyConfig: {
    props: {
      result: 'result',
      total: 'page.total'
    },
    ajax: {
      // 接收 Promise
      query: ({ page }) => {
        queryParams.pageNo = page.currentPage
        queryParams.pageSize = page.pageSize
        return fetchApi()
      }
    }
  },
  columns: [
    { type: 'seq', width: 70 },
    {
      field: 'transportNumber',
      title: '运单单号',
      minWidth: '230px'
    },
    { field: 'orderTime', title: '下单时间', minWidth: '160px',formatter: dateFormatterByOptions },
    { field: 'customerName', title: '客户名称', minWidth: '130px' },
    { field: 'sendName', title: '联系人', minWidth: '100px' },
    { field: 'sendPhone', title: '联系电话', minWidth: '120px' },
    {
      field: 'sendAddress',
      title: '装货地址',
      minWidth: '250px',
      formatter: ({ row }) => {
        return `${row.sendProvince || ''}${row.sendCity || ''}${row.sendCounty || ''}${row.sendAddress || ''}`
      }
    },
    {
      field: 'acceptTime',
      title: '接单时间',
      minWidth: '160px',
      formatter: dateFormatterByOptions
    },
    {
      field: 'transportRequire',
      title: '运输要求',
      minWidth: '100px',
      slots: {
        default: 'transportRequire'
      }
    },
    { field: 'transportDriverName', title: '司机名称', minWidth: '160px' },
    { field: 'transportDriverPhone', title: '联系电话', minWidth: '160px' },
    { field: 'transportCarNumber', title: '车牌号码', minWidth: '100px' },
    {
      fixed: 'right',
      field: 'errorOrder',
      title: '是否异常',
      minWidth: '80px',
      slots: {
        default: 'errorOrder'
      }
    },
    {
      fixed: 'right',
      field: 'transportStatus',
      title: '状态',
      minWidth: '100px',
      slots: {
        default: 'transportStatus'
      }
    },
    { title: '操作', width: '160px', slots: { default: 'operate' }, fixed: 'right' }
  ],
  data: []
})

const transportStatusColorConfig = {
  wait_load: '#D68711',
  delivery: '#36B336',
  wait_pay: '#2D57CC'
}

const gridEvents: VxeGridListeners = {}

const handelSearch = () => {
  searchRef.value?.open()
}
const router = useRouter()
const toInfoPage = (rowData?: Required<typeof gridOptions>['data'][number]) => {
  router.push({
    path: 'historyOrder/info',
    query: {
      type: 'view',
      id: rowData?.id
    }
  })
}

function toPathPage(rowData?: Required<typeof gridOptions>['data'][number]) {
  //todo 跳转到轨迹查询页面上
  // 点击携带运单号跳转到轨迹查看页面
  const url = router.resolve({
    path: `/${sysType}/delivery/trackQuery`,
    query: {
      orderNumber: rowData?.transportNumber,
      orderType: 'history'
    }
  })
  window.open(url.href)
}
</script>
