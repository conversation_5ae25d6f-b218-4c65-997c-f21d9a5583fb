<template>
  <!-- 列表 -->
  <ContentWrap class="full-height">
    <vxe-grid ref="gridRef" v-bind="gridOptions" v-on="gridEvents">
      <template #toolbar_buttons>
        <vxe-radio-group v-model="queryParams.auditStatus" @change="getList">
          <vxe-radio-button label="all">所有订单</vxe-radio-button>
          <vxe-radio-button label="not">待审核</vxe-radio-button>
          <vxe-radio-button label="yes">已通过</vxe-radio-button>
          <vxe-radio-button label="back">已驳回</vxe-radio-button>
        </vxe-radio-group>
      </template>
      <template #tools>
        <vxe-button @click="handelSearch" status="info" icon="vxe-icon-search" />
      </template>
      <template #auditStatus="{ row }">
        <CustomStatus
          :title="getDictLabel(DICT_TYPE.AUDIT_STATUS, row.auditStatus)"
          :color="auditStatusColorConfig[row.auditStatus]"
        />
      </template>
      <template #carModelName="{ row }">
        <span>{{ `${row.carModelName || ''}/${row.carLength || ''}` }}</span>
      </template>
      <template #transportRequire="{ row }">
        <CustomStatus
          :title="getDictLabel(DICT_TYPE.TRANSPORT_REQUIRE, row.transportRequire)"
          :color="colorConfig[row.transportRequire]"
        />
      </template>
      <template #operate="{ row }">
        <el-button type="primary" size="small" @click="toInfoPage(row)" plain>详情</el-button>
      </template>
    </vxe-grid>
  </ContentWrap>

  <!-- 搜索抽屉 -->
  <SearchDrawer ref="searchRef" v-model="queryParams" @search="getList" />
</template>

<script setup lang="ts">
import type { VxeGridInstance, VxeGridListeners, VxeGridProps } from 'vxe-table'
import SearchDrawer from './SearchDrawer.vue'
import { tms_web } from '@/api/ccms'
import { DICT_TYPE, getDictLabel } from '@/utils/dict'
import CustomStatus from '@/components/CustomStatus.vue'
import { cloneDeep } from 'lodash-es'
import { dateFormatterByOptions } from '@/utils/formatTime'

defineOptions({ name: 'DeliveryOrder' })

const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  auditStatus: 'all',
  orderNumber: undefined,
  customerName: undefined,
  sendPhone: undefined,
  orderStartTime: undefined,
  orderEndTime: undefined
})

const searchRef = ref()
// 表格实例
const gridRef = ref<VxeGridInstance>()

// 调用vxe grid query方法重载表格
const getList = () => {
  if (gridRef.value) {
    gridRef.value.commitProxy('query') // 触发表格重新加载数据
  }
}
// 获取表格数据
const fetchApi = () => {
  return new Promise(async (resolve) => {
    const params: any = cloneDeep(queryParams)
    if (params.auditStatus === 'all') {
      delete params.auditStatus
    }
    const res = await tms_web.transportOrder_query_post({ data: params })
    gridOptions.data = res.data?.list || []
    resolve({
      page: {
        total: res.data?.total
      },
      result: res.data?.list
    })
  })
}

const colorConfig: any = {
  cold: '#2D57CC',
  normal: '#36B336'
}

const gridOptions = reactive<
  VxeGridProps<GetApiResByListItem<typeof tms_web.transportOrder_query_post>>
>({
  stripe: true,
  border: true,
  keepSource: true,
  height: '100%',
  columnConfig: {
    resizable: true
  },
  pagerConfig: {
    enabled: true,
    pageSize: 10
  },
  editConfig: {
    trigger: 'click',
    mode: 'row',
    showStatus: true
  },
  toolbarConfig: {
    zoom: true,
    custom: true,
    slots: {
      buttons: 'toolbar_buttons',
      tools: 'tools'
    }
  },
  proxyConfig: {
    props: {
      result: 'result',
      total: 'page.total'
    },
    ajax: {
      // 接收 Promise
      query: ({ page }) => {
        queryParams.pageNo = page.currentPage
        queryParams.pageSize = page.pageSize
        return fetchApi()
      }
    }
  },
  columns: [
    { type: 'seq', width: 70 },
    {
      field: 'orderNumber',
      title: '订单单号',
      minWidth: '230px'
    },
    { field: 'orderTime', title: '下单时间', minWidth: '160px', formatter: dateFormatterByOptions },
    { field: 'customerName', title: '客户名称', minWidth: '130px' },
    { field: 'sendName', title: '联系人', minWidth: '100px' },
    { field: 'sendPhone', title: '联系电话', minWidth: '120px' },
    {
      field: 'transportRequire',
      title: '运输要求',
      minWidth: '100px',
      slots: {
        default: 'transportRequire'
      }
    },
    {
      field: 'transportType',
      title: '运输类型',
      minWidth: '100px',
      formatter: ({ cellValue }) => getDictLabel(DICT_TYPE.CAR_ROUTE_TYPE, cellValue)
    },
    {
      field: 'sendAddress',
      title: '装货地址',
      minWidth: '250px',
      formatter: ({ row }) => {
        return `${row.sendProvince || ''}${row.sendCity || ''}${row.sendCounty || ''}${row.sendAddress || ''}`
      }
    },
    {
      field: 'loadingTime',
      title: '预约时间',
      minWidth: '180px',
      formatter: dateFormatterByOptions
    },
    {
      field: 'carModelName',
      title: '车辆规格',
      minWidth: '150px',
      slots: { default: 'carModelName' }
    },
    { field: 'auditRemark', title: '审核意见', minWidth: '120px' },
    {
      fixed: 'right',
      field: 'auditStatus',
      title: '状态',
      minWidth: '100px',
      slots: {
        default: 'auditStatus'
      }
    },
    { title: '操作', width: 90, slots: { default: 'operate' }, fixed: 'right' }
  ],
  data: []
})

const auditStatusColorConfig = {
  wait_load: '#D68711',
  delivery: '#36B336',
  wait_pay: '#2D57CC'
}

const gridEvents: VxeGridListeners = {}

const handelSearch = () => {
  searchRef.value?.open()
}
const router = useRouter()
const toInfoPage = (rowData?: Required<typeof gridOptions>['data'][number]) => {
  router.push({
    path: 'order/info',
    query: {
      audit: "audit",
      type: 'view',
      id: rowData?.id
    }
  })
}
</script>
