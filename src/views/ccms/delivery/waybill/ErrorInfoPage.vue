<script setup lang="ts">
import InfoBlock from '@/components/infoBlock/InfoBlock.vue'
import { computed, nextTick, onMounted, ref } from 'vue'
import SelectAddImage from '@/components/SelectAddImage.vue'
import { tms_web } from '@/api/ccms'
import { DICT_TYPE, getDictOptions } from '@/utils/dict'
import { optionsToEnums } from '@/utils/businessData'

const ReportTypeEnums = optionsToEnums(getDictOptions(DICT_TYPE.REPORT_TYPE))
const AccidentLevelEnums = optionsToEnums(getDictOptions(DICT_TYPE.ACCIDENT_LEVEL))
const AccidentHighwayItLevelEnums = optionsToEnums(
  getDictOptions(DICT_TYPE.ACCIDENT_HIGHWAY_IT_LEAVE)
)
const AccidentHighwayGovLevelEnums = optionsToEnums(
  getDictOptions(DICT_TYPE.ACCIDENT_HIGHWAY_GOV_LEVEL)
)
//AccidentHighwaySectionEnums
const AccidentHighwaySectionEnums = optionsToEnums(
  getDictOptions(DICT_TYPE.ACCIDENT_HIGHWAY_SECTION)
)

const formData = ref<any>({})

const router = useRouter()
const route = useRoute()

const selectFileList = ref<any[]>([])

const imgFilePath = computed(() => {
  return selectFileList.value.map((item) => {
    return item.address || ''
  })
})

function selectFile(fileInfo: any) {
  selectFileList.value.push(fileInfo)
}

function openViewImg(index: number) {
  showPreviewIndex.value = index
  showPreview.value = true
}

function removeImage(path: string) {
  tms_web
    .file_delete_post({
      data: {
        path: path
      },
      load: {
        fullLoading: true
      }
    })
    .then(() => {
      selectFileList.value = selectFileList.value.filter((item) => item.address !== path)
    })
}

const message = useMessage()

function save() {
  let saveApi
  switch (formData.value.reportType) {
    case 'accident':
      saveApi = tms_web.accidentFaultDamageReport_saveIncident_post
      break
    case 'fault':
      saveApi = tms_web.accidentFaultDamageReport_saveFault_post
      break
    case 'damage':
      saveApi = tms_web.accidentFaultDamageReport_saveDamage_post
      break
    default:
      return
  }

  const params = JSON.parse(JSON.stringify(formData.value))
  params.files = selectFileList.value
  params.orderId = route.query.id

  saveApi({
    data: params
  }).then(() => {
    message.success('操作成功！')
    router.back();
  })
}

const showPreview = ref(false)
const showPreviewIndex = ref(0)

onMounted(() => {
  //确保枚举值拿到
  nextTick(() => {
    const temp = JSON.parse(JSON.stringify(formData.value))
    temp.reportType = (ReportTypeEnums[0] && ReportTypeEnums[0].name) || ''
    //accidentHighwaySection
    temp.accidentHighwaySection =
      (AccidentHighwaySectionEnums[0] && AccidentHighwaySectionEnums[0].name) || ''
    formData.value = temp
  })
})
</script>

<template>
  <div class="flex flex-col gap-5">
    <div class="flex items-center gap-[10px] rounded-[10px] bg-white p-5 leading-none">
      <div class="text-[18px] font-bold text-black">异常上报</div>
      <div class="flex cursor-pointer items-center gap-[6px] text-[13px] text-[#A8ABB2]">
        <Icon icon="tdesign:rollback" />
        <span @click="router.back()">返回</span>
      </div>
    </div>
    <div class="flex flex-col gap-[30px] rounded-[10px] bg-white p-10">
      <el-form :model="formData" label-width="200px" label-suffix="：">
        <InfoBlock name="异常上报类型">
          <div class="grid grid-cols-2">
            <el-form-item
              label="异常类型"
              prop="reportType"
              :rules="[
                {
                  required: true,
                  message: '请选择异常上报类型'
                }
              ]"
            >
              <el-radio-group v-model="formData.reportType">
                <el-radio v-for="item in ReportTypeEnums" :key="item.name" :label="item.name"
                  >{{ item.disName }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </div>
        </InfoBlock>
        <InfoBlock name="填写故障上报信息">
          <div class="grid grid-cols-2">
            <el-form-item label="事故等级" prop="accidentLevel">
              <el-select v-model="formData.accidentLevel" placeholder="请选择" class="w-full">
                <el-option
                  v-for="item in AccidentLevelEnums"
                  :key="item.name"
                  :label="item.disName"
                  :value="item.name"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="事故形态" prop="accidentForm">
              <el-input v-model="formData.accidentForm" placeholder="请输入" />
            </el-form-item>
            <el-form-item label="事故发生时间" prop="accidentTime">
              <el-date-picker
                v-model="formData.accidentTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                class="!w-full"
                type="datetime"
                placeholder="选择日期"
              />
            </el-form-item>
            <el-form-item label="天气情况" prop="weatherCondition">
              <el-input v-model="formData.weatherCondition" placeholder="请输入" />
            </el-form-item>
            <el-form-item label="事故发生地点" prop="accidentAddress" class="col-span-2">
              <el-input v-model="formData.accidentAddress" placeholder="请输入" />
            </el-form-item>
            <el-form-item label="事故路段公路技术等级" prop="accidentHighwayItLevel">
              <el-select
                v-model="formData.accidentHighwayItLevel"
                placeholder="请选择"
                class="w-full"
              >
                <el-option
                  v-for="item in AccidentHighwayItLevelEnums"
                  :key="item.name"
                  :label="item.disName"
                  :value="item.name"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="事故路段公路行政等级" prop="accidentHighwayGovLevel">
              <el-select
                v-model="formData.accidentHighwayGovLevel"
                placeholder="请选择"
                class="w-full"
              >
                <el-option
                  v-for="item in AccidentHighwayGovLevelEnums"
                  :key="item.name"
                  :label="item.disName"
                  :value="item.name"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="事故路段线性状况" prop="accidentHighwaySection" class="col-span-2">
              <el-radio-group v-model="formData.accidentHighwaySection">
                <el-radio
                  v-for="item in AccidentHighwaySectionEnums"
                  :key="item.name"
                  :label="item.name"
                  >{{ item.disName }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="事故直接原因" prop="accidentReason" class="col-span-2">
              <el-input
                v-model="formData.accidentReason"
                :autosize="{ minRows: 4 }"
                type="textarea"
                placeholder="请输入"
              />
            </el-form-item>
            <el-form-item label="车牌号" prop="accidentCarNumber">
              <el-input
                disabled
                class="info"
                v-model="formData.accidentCarNumber"
                placeholder="系统自动获取"
              />
            </el-form-item>
            <el-form-item label="车辆类型" prop="accidentCarType">
              <el-input
                disabled
                class="info"
                v-model="formData.accidentCarType"
                placeholder="系统自动获取"
              />
            </el-form-item>
            <el-form-item label="车辆规格" prop="accidentCarModel">
              <el-input
                disabled
                class="info"
                v-model="formData.accidentCarModel"
                placeholder="系统自动获取"
              />
            </el-form-item>
            <el-form-item label="占位" style="visibility: hidden" />
            <el-form-item label="驾驶员姓名" prop="driverName">
              <el-input
                disabled
                class="info"
                v-model="formData.driverName"
                placeholder="系统自动获取"
              />
            </el-form-item>
            <el-form-item label="驾驶员号码" prop="driverNumber">
              <el-input
                disabled
                class="info"
                v-model="formData.driverNumber"
                placeholder="系统自动获取"
              />
            </el-form-item>
            <el-form-item label="主要货物" prop="mainGoods" class="col-span-2">
              <el-input v-model="formData.mainGoods" placeholder="请输入" />
            </el-form-item>
            <el-form-item label="事故初步原因分析" prop="accidentReasonAnalysis" class="col-span-2">
              <el-input
                v-model="formData.accidentReasonAnalysis"
                :autosize="{ minRows: 4 }"
                type="textarea"
                placeholder="请输入"
              />
            </el-form-item>
          </div>
        </InfoBlock>
        <InfoBlock name="上传现场照片">
          <el-form-item label="现场图片">
            <div
              class="grid grid-cols-[repeat(auto-fill,_100px)] justify-between gap-[10px] overflow-hidden"
            >
              <select-add-image @on-add-file="selectFile" />
              <div
                v-for="(file, index) in selectFileList"
                :key="file.address"
                class="img-card size-[100px] overflow-hidden rounded"
              >
                <el-image
                  class="size-full"
                  :src="imgFilePath[index]"
                  fit="cover"
                  :preview-src-list="imgFilePath"
                  :initial-index="index"
                />
                <div class="img-opt flex items-center justify-center gap-[10px]">
                  <Icon icon="ep:zoom-in" @click="openViewImg(index)" />
                  <Icon icon="ep:delete" @click="removeImage(file.address)" />
                </div>
              </div>
            </div>
          </el-form-item>
        </InfoBlock>
        <InfoBlock name="填写事故描述">
          <el-form-item label="事故描述" prop="faultRemark" class="col-span-2">
            <el-input
              v-model="formData.faultRemark"
              :autosize="{ minRows: 4 }"
              type="textarea"
              placeholder="请输入"
            />
          </el-form-item>
        </InfoBlock>
      </el-form>
      <div class="flex items-center justify-center gap-2">
        <el-button type="primary" v-hasPermi="['ccms:accidentFaultDamageReport:saveIncident','ccms:accidentFaultDamageReport:saveFault','ccms:accidentFaultDamageReport:saveDamage']" @click="save">立即提交</el-button>
        <el-button @click="router.back()">返回</el-button>
      </div>
      <el-image-viewer
        v-if="showPreview"
        :url-list="imgFilePath"
        show-progress
        :initial-index="showPreviewIndex"
        @close="showPreview = false"
      />
    </div>
  </div>
</template>

<style scoped lang="scss">
.img-card {
  position: relative;
  overflow: hidden;

  .img-opt {
    overflow: hidden;
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    opacity: 0;
    transition: opacity 0.3s;

    &:hover {
      opacity: 1;
    }

    i {
      color: #fff;
      cursor: pointer;
      font-size: 20px;
    }
  }
}
</style>
