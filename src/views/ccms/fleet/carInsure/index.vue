<template>
  <!-- 列表 -->
  <ContentWrap class="full-height">
    <vxe-grid ref="gridRef" v-bind="gridOptions" v-on="gridEvents">
      <template #type="{ row }">
        <DictTag :type="DICT_TYPE.INSURE_TYPE" :value="row.insureType" />
      </template>
      <template #status="{ row }">
        <DictTag :type="DICT_TYPE.INSURE_STATUS" :value="row.status" />
      </template>
      <template #operate="{ row }">
        <el-button
          type="primary"
          size="small"
          @click="openForm(row.id)"
          v-hasPermi="['ccms:carInsure:get']"
          plain
          >查看保单</el-button
        >
        <!-- <el-button type="danger" size="small" @click="handleDelete(row.id)" v-hasPermi="['wms:contract-message:delete']"
          plain>删除</el-button> -->
      </template>
    </vxe-grid>
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <ContractMessageForm ref="formRef" @success="getList" />
  <!-- 搜索抽屉 -->
  <SearchDrawer ref="searchRef" v-model="queryParams" @search="getList" />
</template>

<script setup lang="ts">
import { dateFormatterByOptions } from '@/utils/formatTime'
// import download from '@/utils/download'
import ContractMessageForm from './ContractMessageForm.vue'
import DictTag from '@/components/DictTag/src/DictTag.vue'
import { DICT_TYPE } from '@/utils/dict'
import { tms_web } from '@/api/ccms'
import type { VxeGridProps, VxeGridListeners, VxeGridInstance } from 'vxe-table'
import SearchDrawer from './SearchDrawer.vue'
// import { rowContextKey } from 'element-plus';
// import { values } from 'lodash-es';

defineOptions({ name: 'ContractMessage' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const queryParams = reactive({
  pageNo: 1,
  pageSize: 10
})

const searchRef = ref()
// 表格实例
const gridRef = ref<VxeGridInstance>()

// 调用vxe grid query方法重载表格
const getList = (item:any) => {
  Object.assign(queryParams,{...queryParams,...item})
  if (gridRef.value) {
    gridRef.value.commitProxy('query') // 触发表格重新加载数据
  }
}
// 获取表格数据
const fetchApi = () => {
  return new Promise(async (resolve) => {
    // ContractMessageApi.getContractMessagePage(queryParams)
    // const res = await ContractMessageApi.getContractMessagePage(queryParams)
    const res = await tms_web.carInsure_query_post({
      data: {
        ...queryParams
      }
    })
    // console.log(res, '车辆保险List')

    // gridOptions.data = res.data.list

    resolve({
      page: {
        total: res.data?.total
      },
      result: res.data?.list
    })
  })
}



const gridOptions = reactive({
  stripe: true,
  border: true,
  keepSource: true,
  height: '100%',
  columnConfig: {
    resizable: true
  },
  pagerConfig: {
    enabled: true,
    pageSize: 10
  },
  editConfig: {
    trigger: 'click',
    mode: 'row',
    showStatus: true
  },
  toolbarConfig: {
    zoom: true,
    custom: true,
    // slots: {
    //   buttons: 'toolbar_buttons'
    // },
    tools: [
      { name: '', code: 'search', icon: 'vxe-icon-search', status: 'info' },
      // { name: '', code: 'customExport', icon: 'vxe-table-icon-download', status: 'info' }
    ]
  },
  proxyConfig: {
    props: {
      result: 'result',
      total: 'page.total'
    },
    ajax: {
      // 接收 Promise
      query: ({ page }) => {
        queryParams.pageNo = page.currentPage
        queryParams.pageSize = page.pageSize
        return fetchApi()
      },
      // body 对象： { removeRecords }
    }
  },
  columns: [
    { type: 'seq', width: 70 },
    { field: 'insureNumber', title: '保单号码', minWidth: '100px' },
    { field: 'carNumber', title: '车牌号码', minWidth: '100px' },
    { field: 'vinNo', title: '车架号码', minWidth: '100px' },
    { field: 'insureType', title: '保单类型', minWidth: '100px', slots: { default: 'type' } },
    { field: 'brandModel', title: '品牌型号', minWidth: '100px' },
    { field: 'carMasterName', title: '车主名称', minWidth: '80px' },
    { field: 'phone', title: '手机号码', minWidth: '80px' },
    // { field: 'contractPeriod', title: '合同期限', minWidth: '100px' },
    {
      field: 'insureStartTime',
      title: '保险起始时间',
      minWidth: '120px',
      formatter: dateFormatterByOptions
    },
    {
      field: 'insureEndTime',
      title: '保险结束时间',
      minWidth: '120px',
      formatter: dateFormatterByOptions
    },
    { field: 'status', title: '保单状态', minWidth: '80px', slots: { default: 'status' } },
    // { field: 'hctdName', title: '招商专员', minWidth: '120px' },
    //   { field: 'effectiveDate', title: '合同生效时间', minWidth: '200px',formatter:dateFormatter2 },
    //   { field: 'expirationDay', title: '合同失效时间', minWidth: '200px',formatter:dateFormatter2 },
    //   { field: 'renewalTime', title: '合同续签时间', minWidth: '200px', formatter: dateFormatter2 },
    // { field: 'originalContractNo', title: '原合同编号', minWidth: '200px' },
    // { field: 'status', title: '合同状态', minWidth: '100px' },
    { title: '操作', width: 120, slots: { default: 'operate' } }
  ],
  data: []
})

const gridEvents: VxeGridListeners = {
  toolbarButtonClick(params) {
    console.log(params.code)
  },

  toolbarToolClick({ code }) {
    // console.log(123, code, tool)
    switch (code) {
      case 'search':
        handelSearch()
        break
      case 'add':
        openForm()
        break
      case 'del':
        if (gridRef.value) {
          gridRef.value.commitProxy('delete')
        }
        break
      case 'customExport':
        console.log('自定义导出')
        break
    }
  }
}

const handelSearch = () => {
  searchRef.value?.open()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (id?: number) => {
  // console.log(formRef.value)

  formRef.value.open(id)
}

/** 删除按钮操作 */
// const handleDelete = async (id: number) => {
//   try {
//     // 删除的二次确认
//     await message.delConfirm()
//     // 发起删除
//     await ContractMessageApi.deleteContractMessage(id)
//     message.success(t('common.delSuccess'))
//     // 刷新列表
//     await getList()
//   } catch { }
// }

/** 导出按钮操作 */
// const handleExport = async () => {
//   try {
//     // 导出的二次确认
//     await message.exportConfirm()
//     // 发起导出
//     const data = await ContractMessageApi.exportContractMessage(queryParams)
//     download.excel(data, '合同信息.xls')
//   } catch {
//   } finally {
//   }
// }
</script>
