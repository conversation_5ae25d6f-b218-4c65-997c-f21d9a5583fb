<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <div class="demo-image__preview">
      <div class="demo-image__error">
        <div class="block">
          <el-image
            style="width: 100px; height: 100px"
            :src="'https://fuss10.elemecdn.com/a/3f/3302e58f9a181d2509f3dc0fa68b0jpeg.jpeg'"
            :zoom-rate="1.2"
            :max-scale="7"
            :min-scale="0.2"
            :preview-src-list="srcList"
            show-progress
            :initial-index="1"
            fit="cover"
          >
            <template #error>
              <div class="image_slot">
                <div class="image_text">加载失败</div>
              </div>
            </template>
          </el-image>
        </div>
      </div>
      <div>图片名称.jpg</div>
    </div>
  </Dialog>
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { tms_web } from '@/api/ccms'
import { Picture as IconPicture } from '@element-plus/icons-vue'
/** 合同信息 表单 */
defineOptions({ name: 'ContractMessageForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题

const imgListViewRef = ref()

const srcList = ['https://fuss10.elemecdn.com/a/3f/3302e58f9a181d2509f3dc0fa68b0jpeg.jpeg']

onMounted(() => {})
// id存储器
const imageId = ref()
const loading = ref(false)
const getImageUrlFn = async () => {
  try {
    const res = await tms_web.carInsure_get_post({
      data: {
        id: imageId.value
      }
    })
    // console.log(res, '图片路径')
    imgListViewRef.value = res.data?.fileList[0].address
  } catch (error) {
    console.log(error)
  }
}
/** 打开弹窗 */
const open = async (id: Number) => {
  dialogVisible.value = true
  dialogTitle.value = '查看保单'
  // 修改时，设置数据
  imageId.value = id
  getImageUrlFn()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
</script>
<style scoped lang="scss">
.demo-image__error .image-slot {
  font-size: 30px;
}

.demo-image__error .image-slot .el-icon {
  font-size: 30px;
}

.demo-image__error .el-image {
  width: 100%;
  height: 200px;
}

.image_slot {
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
  .image_text {
    font-size: 18px;
    text-align: center;
    color: #c0c4cc;
  }
}
</style>
