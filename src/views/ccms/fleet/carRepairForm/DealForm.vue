<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="报单处理" required prop="dealResult">
        <el-radio-group v-model="formData.dealResult">
          <el-radio value="yes">同意</el-radio>
          <el-radio value="back">退回</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="处理意见" required prop="dealRemark">
        <el-input v-model="formData.dealRemark" :rows="10" type="textarea" style="width: 440px" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { tms_web } from '@/api/ccms'

/** 合同信息 表单 */
defineOptions({ name: 'ContractMessageForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formData = reactive<formData>({
  id: null,
  dealResult: '',
  dealRemark: ''
})
const formRules = reactive({
  dealResult: [{ required: true, message: '报单处理', trigger: 'blur' }],
  dealRemark: [{ required: true, message: '处理意见', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref
interface formData {
  id: null
  dealResult: any
  dealRemark: any
}
/** 打开弹窗 */
const open = async (id: any) => {
  dialogVisible.value = true
  dialogTitle.value = t('维修厂报价单处理')
  formData.id = id
  resetForm()
  // 修改时，设置数据
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData
    console.log(data,'data');
    // console.log(data, 'formType');
    await tms_web.carRepairForm_deal_post({
      data: {
        ...data
      }
    })
    // console.log(res, '新增成功');

    message.success(t('common.createSuccess'))

    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formRef.value?.resetFields()
}
</script>
