<template>
  <div class="wrapper">
    <el-card class="self-card header-card" shadow="never">
      <div class="top-box">
        <div class="max-tit">导航页统计</div>
        <div class="radioBox">
          <el-radio-group @change="changeCountType" v-model="countType" size="small">
            <el-radio-button value="1" label="1">今日</el-radio-button>
            <el-radio-button value="2" label="2">近7天</el-radio-button>
            <el-radio-button value="3" label="3">近30天</el-radio-button>
          </el-radio-group>
        </div>
      </div>
      <div class="header">
        <div>
          <div class="icon"><img src="@/assets/images/icon/a.png" alt="" /></div>
          <div class="num">{{ countHomeTotal.transportOrderMoney }}</div>
          <div class="text">交易额（万元）</div>
        </div>
        <div>
          <div class="icon"><img src="@/assets/images/icon/b.png" alt="" /></div>
          <div class="num">{{ countHomeTotal.orderNum }}</div>
          <div class="text">订单量</div>
        </div>
        <div>
          <div class="icon"><img src="@/assets/images/icon/c.png" alt="" /></div>
          <div class="num">{{ countHomeTotal.transportNum }}</div>
          <div class="text">运单量</div>
        </div>
        <div>
          <div class="icon"><img src="@/assets/images/icon/f.png" alt="" /></div>
          <div class="num">{{ countHomeTotal.waitOrderNum }}</div>
          <div class="text">未接订单</div>
        </div>
        <div>
          <div class="icon"><img src="@/assets/images/icon/d.png" alt="" /></div>
          <div class="num">{{ countHomeTotal.deliveryOrderNum }}</div>
          <div class="text">配送中</div>
        </div>
        <div>
          <div class="icon"><img src="@/assets/images/icon/e.png" alt="" /></div>
          <div class="num">{{ countHomeTotal.errorOrderNum }}</div>
          <div class="text">异常运单</div>
        </div>
      </div>
    </el-card>

    <div class="four-box">
      <el-card class="self-card" shadow="never">
        <div class="max-tit">接单率</div>
        <div class="chart" style="width: 100%">
          <DataPieChart idRef="Ethis" :seriesData="JDLseriesData" v-if="JDLseriesData.valNum" />
        </div>
        <div class="mess-b">
          <div class="txt">订单总数： {{ JDLseriesData.huanbi }}</div>
          <div class="txt">运单总数： {{ JDLseriesData.tongbi }}</div>
        </div>
      </el-card>
      <el-card class="self-card" shadow="never">
        <div class="max-tit">运单完成率</div>
        <div class="chart">
          <DataPieChart
            idRef="EthnicDisqwdq"
            :seriesData="YDWCLseriesData"
            v-if="YDWCLseriesData.valNum"
          />
        </div>
        <div class="mess-b">
          <div class="txt">总交易额： {{ YDWCLseriesData.huanbi }}</div>
        </div>
      </el-card>
      <el-card class="self-card" shadow="never">
        <div class="dis-tit">
          <div class="max-tit">司机活跃度</div>
          <div class="tit-right">
            <el-radio-group @change="changeRadioType" v-model="radioType" size="small">
              <el-radio-button value="1" label="1">日活</el-radio-button>
              <el-radio-button value="2" label="2">月活</el-radio-button>
            </el-radio-group>
          </div>
        </div>
        <div class="chart">
          <DataPieChart idRef="EthnicDwqis" :seriesData="seriesData" v-if="seriesData.valNum" />
        </div>
        <div class="mess-b">
          <div class="txt">环比： {{ seriesData.huanbi }}%</div>
          <div class="txt">同比： {{ seriesData.tongbi }}%</div>
        </div>
      </el-card>
      <el-card class="self-card" shadow="never">
        <div class="dis-tit">
          <div class="max-tit">客户活跃度</div>
          <div class="tit-right">
            <el-radio-group @change="changeKaRadioType" v-model="KradroType" size="small">
              <el-radio-button value="1" label="1">日活</el-radio-button>
              <el-radio-button value="2" label="2">月活</el-radio-button>
            </el-radio-group>
          </div>
        </div>
        <div class="chart">
          <DataPieChart
            idRef="EthasddnicDis"
            :seriesData="seriesDataKa"
            v-if="seriesDataKa.valNum"
          />
        </div>
        <div class="mess-b">
          <div class="txt">环比： {{ seriesDataKa.huanbi }}%</div>
          <div class="txt">同比： {{ seriesDataKa.tongbi }}%</div>
        </div>
      </el-card>
    </div>

    <div class="Two-box">
      <el-card class="self-card" shadow="never">
        <div class="dis-tit">
          <div class="max-tit">订单统计</div>
          <div class="tit-right">
            <div class="mess">
              <span v-if="DDradioType == 1">今日</span>
              <span v-else-if="DDradioType == 2">近7天</span>
              <span v-else-if="DDradioType == 3">近30天</span>
              订单总量：{{ DDserverData.value }} 环比：<span style="color: #e04f48"
                >{{ DDserverData.monthOnMonthRatio }}%</span
              ></div
            >
            <el-radio-group @change="changeDDType" v-model="DDradioType" size="small">
              <el-radio-button value="1" label="1">今日</el-radio-button>
              <el-radio-button value="2" label="2">近7天</el-radio-button>
              <el-radio-button value="3" label="3">近30天</el-radio-button>
            </el-radio-group>
          </div>
        </div>
        <div class="chart-two" style="width: 100%">
          <DataLineChart
            idRef="EthnicDion"
            :lineData="DDserverDataList"
            v-if="DDserverDataList?.yData"
          />
        </div>
      </el-card>
      <el-card class="self-card" shadow="never">
        <div class="dis-tit">
          <div class="max-tit">运单统计</div>
          <div class="tit-right">
            <div class="mess">
              <span v-if="YDradioType == 1">今日</span>
              <span v-else-if="YDradioType == 2">近7天</span>
              <span v-else-if="YDradioType == 3">近30天</span>
              订单总量：{{ YDserverData.value }} 环比：
              <span style="color: #e04f48">{{ YDserverData.monthOnMonthRatio }}%</span>
            </div>
            <el-radio-group @change="changeYDType" v-model="YDradioType" size="small">
              <el-radio-button value="1" label="1">今日</el-radio-button>
              <el-radio-button value="2" label="2">近7天</el-radio-button>
              <el-radio-button value="3" label="3">近30天</el-radio-button>
            </el-radio-group>
          </div>
        </div>
        <div class="chart-two">
          <DataLineChart
            idRef="EthnicDionwdqw"
            :lineData="YDserverDataList"
            v-if="YDserverDataList?.yData"
          />
        </div>
      </el-card>
    </div>
    <div class="Two-box">
      <el-card class="self-card" shadow="never">
        <div class="dis-tit">
          <div class="max-tit">接单效率统计</div>
          <div class="tit-right">
            <div class="mess">平均接单时长：{{ jdxyObject }}分钟</div>
          </div>
        </div>
        <div class="chart-two">
          <DataLineChart idRef="EthnicDiosxwen" :lineData="jdxyList" v-if="jdxyList?.yData" />
        </div>
      </el-card>
      <el-card class="self-card" shadow="never">
        <div class="dis-tit">
          <div class="max-tit">活跃度分析</div>
          <div class="tit-right">
            <el-radio-group @change="changeHYDType" v-model="HYDradioType" size="small">
              <el-radio-button value="1" label="客户日活">客户日活</el-radio-button>
              <el-radio-button value="2" label="客户月活">客户月活</el-radio-button>
              <el-radio-button value="3" label="驾驶员日活">驾驶员日活</el-radio-button>
              <el-radio-button value="4" label="驾驶员月活">驾驶员月活</el-radio-button>
            </el-radio-group>
          </div>
        </div>
        <div class="chart-two">
          <DataLineChart
            idRef="EthwqdfenicDion"
            :lineData="HYDserverData"
            v-if="HYDserverData?.yData"
          />
        </div>
      </el-card>
    </div>
  </div>
</template>
<script>
import DataPieChart from '@/components/DataPieChart.vue'
import DataLineChart from '@/components/DataLineChart.vue'
import { tms_web } from '@/api/ccms'

export default {
  components: { DataLineChart, DataPieChart },
  data() {
    return {
      JstartTime: '', // 今日开始时间
      JendTime: '', // 今日结束时间
      QstartTime: '', // 近7天开始时间
      QendTime: '', // 近7天结束时间
      SstartTime: '', // 近30天开始时间
      SendTime: '', // 近30天结束时间
      countHomeTotal: {}, // 首页总数据
      countType: '1', // 首页总数据类型

      // 使用更具体的类型
      radioType: '1', // 司机
      KradroType: '1', // 客户
      DDradioType: '1', // 订单统计
      DDserverData: {}, // 订单统计
      DDserverDataList: [], // 订单-list

      YDradioType: '1', // 运单统计
      YDserverData: {}, // 运单统计
      YDserverDataList: [], // 运单统计-list

      jdxyObject: '', // 接单效率统计
      jdxyList: '', // 接单效率-列表

      HYDradioType: '1', // 活跃度分析
      HYDserverData: {}, // 活跃度-列表

      radio: '1',
      JDLseriesData: {}, //今日接单量
      YDWCLseriesData: {}, //今日运单完成率
      seriesData: {}, //司机活跃度
      seriesDataKa: {}, //客户活跃度

      lineData: {
        xData: [
          '1月',
          '2月',
          '3月',
          '4月',
          '5月',
          '6月',
          '7月',
          '8月',
          '9月',
          '10月',
          '11月',
          '12月'
        ],
        yData: [20, 30, 40, 50, 60, 70, 80, 90, 100, 110, 120, 130],
        lineColor: '#2D57CC', // 线条颜色
        lineAreaColor: 'rgba(45, 87, 204, 0.8)' // 线条区域颜色
      }
    }
  },
  mounted() {
    // 获取当前日期
    const now = new Date()
    // 获取近7天的结束时间
    const endOfDay = new Date(now)
    endOfDay.setHours(23, 59, 59, 999) // 设置时间为当天的23:59:59.999
    // 格式化日期
    const format_date = (date) => {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    }
    // 计算近7天的开始和结束时间
    const startOfDay = new Date(now)
    startOfDay.setHours(0, 0, 0, 0) // 设置时间为当天的00:00:00.000
    startOfDay.setDate(startOfDay.getDate() - 6) // 回溯29天，得到近30天的开始时间
    this.QstartTime = format_date(startOfDay)
    this.QendTime = format_date(endOfDay)

    // 计算近30天的开始时间
    const startOfMonth = new Date(now)
    startOfMonth.setHours(0, 0, 0, 0) // 设置时间为当天的00:00:00.000
    startOfMonth.setDate(startOfMonth.getDate() - 29) // 回溯29天，得到近30天的开始时间
    this.SstartTime = format_date(startOfMonth)
    this.SendTime = format_date(endOfDay)
    // 使用不同的变量名来避免覆盖
    const startOfAnotherMonth = new Date(now)
    startOfAnotherMonth.setHours(0, 0, 0, 0) // 设置时间为当天的00:00:00.000
    startOfAnotherMonth.setDate(startOfAnotherMonth.getDate()) // 回溯29天，得到近30天的开始时间
    this.JstartTime = format_date(startOfAnotherMonth)
    this.JendTime = format_date(endOfDay)

    this.getTopCountHomeTotal()
    this.getCountHomeTotal()
    this.getDriverActivity()
    this.getKarivActivity()

    this.getDdCountTotal()
    this.getDdCountTotalList()
    this.getYdCountTotal()
    this.getYdCountTotalList()

    this.getJDXYCount()
    this.getJDXYList()

    this.getHYDListKH()
  },
  methods: {
    // 统计数据 -- top
    getTopCountHomeTotal() {
      tms_web.countHome_countHomeTotalCountVo_post().then((res) => {
        this.JDLseriesData = {
          bgColor: '#36B336',
          zbColor: '#d7f0d7',
          valNum: res.data.orderRatio,
          valText: '接单率',
          unin: res.data.orderRatio + '%',
          huanbi: res.data.orderNum,
          tongbi: res.data.transportNum
        }
        this.YDWCLseriesData = {
          bgColor: '#36B336',
          zbColor: '#d7f0d7',
          valNum: res.data.transportRatio,
          valText: '完单率',
          unin: res.data.transportRatio + '%',
          huanbi: res.data.transportOrderMoney,
          tongbi: res.data.transportNum
        }
      })
    },
    // 统计数据 -- 基础统计
    getCountHomeTotal() {
      let startTime = ''
      let endTime = ''
      if (this.countType == '1') {
        startTime = this.JstartTime
        endTime = this.JendTime
      } else if (this.countType == '2') {
        startTime = this.QstartTime
        endTime = this.QendTime
      } else if (this.countType == '3') {
        startTime = this.SstartTime
        endTime = this.SendTime
      }
      tms_web
        .countHome_countHomeCountVo_post({
          data: {
            startTime: startTime,
            endTime: endTime
          }
        })
        .then((res) => {
          this.countHomeTotal = res.data
        })
    },
    // 导航页统计-统计数据
    changeCountType(val) {
      this.countHomeTotal = {}
      this.countType = val
      this.getCountHomeTotal()
    },
    // 司机活跃度 数据
    getDriverActivity() {
      tms_web
        .countHome_countDriverActivityCount_post({
          data: {
            type: this.radioType == 1 ? 'day' : 'month'
          }
        })
        .then((res) => {
          let data = res.data || {}
          this.seriesData = {
            bgColor: '#2D57CC',
            zbColor: '#d5ddf5',
            valNum: data.value,
            valText: '司机活跃度',
            unin: data.value + '%',
            huanbi: data.monthOnMonthRatio,
            tongbi: data.yearOnYearRatio
          }
        })
    },
    // 司机活跃度change
    changeRadioType(value) {
      this.seriesData = {}
      this.radioType = value
      this.getDriverActivity()
    },
    // 客户活跃度
    getKarivActivity() {
      let type = this.KradroType == 1 ? 'day' : 'month'
      tms_web
        .countHome_countCustomerActivityCount_post({
          data: {
            type: type
          }
        })
        .then((res) => {
          let data = res.data || {}
          this.seriesDataKa = {
            bgColor: '#2D57CC',
            zbColor: '#d5ddf5',
            valNum: data.value,
            valText: '客户活跃度',
            unin: data.value + '%',
            huanbi: data.monthOnMonthRatio,
            tongbi: data.yearOnYearRatio
          }
        })
    },
    // 客户活跃度change
    changeKaRadioType(value) {
      this.seriesDataKa = {}
      this.KradroType = value
      this.getKarivActivity()
    },
    // 订单统计-统计
    getDdCountTotal() {
      let type = ''
      if (this.DDradioType == '1') {
        type = 'day'
      } else if (this.DDradioType == '2') {
        type = 'weekend'
      } else if (this.DDradioType == '3') {
        type = 'month'
      }
      tms_web
        .countHome_countOrderRatio_post({
          data: {
            type: type
          }
        })
        .then((res) => {
          this.DDserverData = res.data || {}
        })
    },
    // 订单统计-列表
    getDdCountTotalList() {
      let type = ''
      if (this.DDradioType == '1') {
        type = 'day'
      } else if (this.DDradioType == '2') {
        type = 'weekend'
      } else if (this.DDradioType == '3') {
        type = 'month'
      }
      tms_web
        .countHome_countOrderList_post({
          data: {
            type: type
          }
        })
        .then((res) => {
          let data = res.data || []
          let Xdata = []
          let Ydata = []
          data.forEach((item) => {
            Xdata.push(item.name)
            Ydata.push(item.value)
          })
          this.DDserverDataList = {
            xData: Xdata,
            yData: Ydata,
            lineColor: '#2D57CC', // 线条颜色
            lineAreaColor: 'rgba(45, 87, 204, 0.8)' // 线条区域颜色
          }
        })
    },
    // 运单统计-统计
    getYdCountTotal() {
      let type = ''
      if (this.YDradioType == '1') {
        type = 'day'
      } else if (this.YDradioType == '2') {
        type = 'weekend'
      } else if (this.YDradioType == '3') {
        type = 'month'
      }
      tms_web
        .countHome_countTransportRatio_post({
          data: {
            type: type
          }
        })
        .then((res) => {
          this.YDserverData = res.data || {}
        })
    },
    // 运单统计-列表
    getYdCountTotalList() {
      let type = ''
      if (this.YDradioType == '1') {
        type = 'day'
      } else if (this.YDradioType == '2') {
        type = 'weekend'
      } else if (this.YDradioType == '3') {
        type = 'month'
      }
      tms_web
        .countHome_countOrderList_post({
          data: {
            type: type
          }
        })
        .then((res) => {
          let data = res.data || []
          let Xdata = []
          let Ydata = []
          data.forEach((item) => {
            Xdata.push(item.name)
            Ydata.push(item.value)
          })
          this.YDserverDataList = {
            xData: Xdata,
            yData: Ydata,
            lineColor: '#2D57CC', // 线条颜色
            lineAreaColor: 'rgba(45, 87, 204, 0.8)' // 线条区域颜色
          }
        })
    },
    // 订单统计change
    changeDDType(value) {
      this.DDserverData = {}
      this.DDserverDataList = []
      this.DDradioType = value
      this.getDdCountTotal()
      this.getDdCountTotalList()
    },
    // 运单统计change
    changeYDType(value) {
      this.YDserverData = {}
      this.YDserverDataList = []
      this.YDradioType = value
      this.getYdCountTotal()
      this.getYdCountTotalList()
    },
    // 接单效率统计
    getJDXYCount() {
      tms_web
        .countHome_countAvgAccept_post({
          data: {}
        })
        .then((res) => {
          this.jdxyObject = res.data
        })
    },
    // 接单效率统计-列表
    getJDXYList() {
      tms_web
        .countHome_countAcceptTimeList_post({
          data: {
            startTime: this.JstartTime,
            endTime: this.JendTime
          }
        })
        .then((res) => {
          let data = res.data || []
          let Xdata = []
          let Ydata = []
          data.forEach((item) => {
            Xdata.push(item.name)
            Ydata.push(item.value)
          })
          this.jdxyList = {
            xData: Xdata,
            yData: Ydata,
            lineColor: '#2D57CC', // 线条颜色
            lineAreaColor: 'rgba(45, 87, 204, 0.8)' // 线条区域颜色
          }
        })
    },
    // 活跃度分析-客户列表
    getHYDListKH() {
      let userType = ''
      let timeType = ''
      console.log(this.HYDradioType,'this.HYDradioType');
      
      if (this.HYDradioType == '1') {
        userType = 'customer'
        timeType = 'day'
      } else if (this.HYDradioType == '2') {
        userType = 'customer'
        timeType = 'month'
      } else if (this.HYDradioType == '3') {
        userType = 'driver'
        timeType = 'day'
      } else if (this.HYDradioType == '4') {
        userType = 'driver'
        timeType = 'month'
      }
      tms_web
        .countHome_countDriverCustomerActivity_post({
          data: {
            userType: userType,
            timeType: timeType
          }
        })
        .then((res) => {
          let data = res.data || []
          let Xdata = []
          let Ydata = []
          data.forEach((item) => {
            Xdata.push(item.name)
            Ydata.push(item.value)
          })
          this.HYDserverData = {
            xData: Xdata,
            yData: Ydata,
            lineColor: '#2D57CC', // 线条颜色
            lineAreaColor: 'rgba(45, 87, 204, 0.8)' // 线条区域颜色
          }
        })
    },
    // 活跃度分析change
    changeHYDType(value) {
      this.HYDserverData = []
      this.HYDradioType = value
      this.getHYDListKH()
    }
  }
}
</script>
<style scoped lang="scss">
.max-tit {
  font-size: 18px;
  color: #000;
  font-weight: bold;
}

.header-card {
  background: #ffffff;
  border-radius: 20px 20px 20px 20px;
  margin-bottom: 20px;

  .top-box {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
  }
}

.header {
  width: 100%;
  justify-content: space-between;
  display: flex;

  > div {
    width: calc((100% - 100px) / 6);
    height: 120px;
    border-radius: 15px;
    cursor: pointer;
    text-align: center;
    background: rgba(45, 87, 204, 0.1);
    border-radius: 20px 20px 20px 20px;
    padding-top: 20px;

    .icon {
      img {
        width: 24px;
        height: 24px;
        margin: 0 auto;
      }

      i {
        font-size: 20px;
        color: #2d57cc;
      }
    }

    .num {
      font-weight: bold;
      font-size: 24px;
      color: #2d57cc;
      margin: 5px 0;
    }

    .text {
      font-size: 14px;
    }
  }
}

.four-box {
  width: 100%;
  justify-content: space-between;
  display: flex;
  margin-bottom: 20px;

  .self-card {
    width: calc((100% - 60px) / 4);
    height: 250px;
    border-radius: 15px;
    cursor: pointer;

    .max-tit {
      font-size: 18px;
      color: #000;
      font-weight: bold;
    }

    .chart {
      width: 100%;
      height: 150px;
      // background: #ccc;
      margin: 0px 0 10px;
    }

    .mess-b {
      display: flex;
      justify-content: space-around;
      color: #666;
    }

    .dis-tit {
      display: flex;
      justify-content: space-between;
    }
  }
}

.Two-box {
  width: 100%;
  justify-content: space-between;
  display: flex;
  margin-top: 20px;

  .self-card {
    width: calc((100% - 20px) / 2);
    height: 270px;
    border-radius: 15px;
    cursor: pointer;

    .dis-tit {
      display: flex;
      justify-content: space-between;
    }

    .tit-right {
      display: flex;
      align-items: center;

      .mess {
        font-size: 14px;
        margin-right: 20px;
      }
    }

    .chart-two {
      height: 200px;
      width: 100%;
      // background: #ccc;
    }
  }
}
</style>
