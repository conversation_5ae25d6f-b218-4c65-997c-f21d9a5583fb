<template>
  <el-drawer v-model="drawerVisible" title="搜索" direction="rtl" size="350px">
    <template #header>
      <span class="text-16px font-700">搜索</span>
    </template>
    <div class="search-drawer" style="height: 100%">
      <el-form
        ref="queryFormRef"
        :inline="true"
        :model="queryParams"
        class="-mb-15px"
        label-width="68px"
      >
        <el-form-item label="费用名称" prop="name">
          <el-input
            v-model="queryParams.name"
            placeholder="请输入"
            clearable
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="费用类型" prop="expenseTypeId">
          <el-select
            class="!w-240px"
            style="width: 250px"
            v-model="queryParams.expenseTypeId"
            placeholder="请选择"
            clearable
            filterable
          >
            <el-option
              v-for="(item, index) in expenseTypeList"
              :key="index"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div style="flex: auto">
        <el-button @click="reset()">重置</el-button>
        <el-button type="primary" @click="search">搜索</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { getStrDictOptions } from '@/utils/dict'
import { tms_web } from '@/api/ccms'

defineOptions({ name: 'AssetSaleInfoPage' })

const levelList = ref(getStrDictOptions('sale_status'))

// 定义 props 和 emits
const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  }
})
const expenseTypeList:any = ref([])
// 查询费用类型
tms_web.expenseType_query_post({}).then((res) => {
  // console.log(res, '费用类型')
  expenseTypeList.value = res.data || []
})
const emit = defineEmits(['update:modelValue', 'search', 'reset'])
// 时间段数组
const dataTimeList = ref([])

// 控制抽屉显示
const drawerVisible = ref(false)
interface queryParams {
  name: any
  expenseTypeId: any
}
// 使用 computed 实现双向绑定
const queryParams = ref({
  name: undefined,
  expenseTypeId: undefined
})

// 打开抽屉
function open() {
  drawerVisible.value = true
}

// 关闭抽屉
function close() {
  drawerVisible.value = false
}

// 搜索
function search() {
  close()
  // console.log(queryParams.value);
  emit('search', queryParams.value)
  
}

const queryFormRef = ref()
// 重置
function reset() {
  queryFormRef.value?.resetFields()
  
  emit('reset', queryParams.value)
}

// 暴露 open 方法
defineExpose({
  open,
  reset
})
</script>

<style lang="scss" scoped>
.search-drawer {
  ::v-deep {
    .el-form {
      height: 98%;
    }
  }
}
</style>
