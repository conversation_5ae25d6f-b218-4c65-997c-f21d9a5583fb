<template>
  <div class="stage-billing">
    <!-- 表单区域 -->
    <table>
      <thead>
        <tr>
          <th>计费时段</th>
          <th>计费条件</th>
          <th></th>
          <th>计费单价</th>
          <th></th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="(item, index) in items" :key="index">
          <td>
            <el-select v-model="item.expenseTimeId">
              <el-option
                v-for="option in expenseTimeList"
                :value="option.id"
                :label="option.name"
                :key="option.id"
              />
            </el-select>
          </td>
          <td>
            <el-select v-model="item.billingType">
              <el-option value="Gt" label="大于" />
              <el-option value="Lt" label="小于" />
              <el-option value="Nq" label="等于" />
              <el-option value="Ne" label="不等于" />
              <el-option value="Lte" label="小于等于" />
              <el-option value="Gte" label="大于等于" />
            </el-select>
          </td>
          <td>
            <el-input porp="" type="number" v-model.number="item.content" />
          </td>
          <td>
            <el-input type="number" v-model.number="item.price" />
          </td>
          <td class="flex items-center gap-2 ml-[5px] select-none">
            <div
              v-if="index === 0"
              @click="addItem"
              class="flex items-center gap-1 cursor-pointer text-nowrap"
              ><img class="size-[18px]" alt="+" src="@/assets/images/<EMAIL>" /><span
                >添加</span
              ></div
            >
            <div
              v-else
              @click="removeItem(index)"
              class="flex items-center gap-1 cursor-pointer text-nowrap"
              ><img class="size-[18px]" alt="-" src="@/assets/images/<EMAIL>" /><span
                >删除</span
              ></div
            >
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script>
import { ref, watch } from 'vue'
import InfoField from '@/components/infoBlock/InfoField.vue'

export default {
  name: 'StageBilling',
  props: {
    expenseTimeList: {
      type: Array,
      required: true
    }
  },
  setup(props) {
    const setItems = (data) => {
      if (data.length === 0) {
        
        items.value = [
          {
            expenseTimeId: '',
            billingType: '',
            content: '',
            price: ''
          }
        ]
      } else {
        this.items = data
      }
    }
    const items = ref([
      {
        expenseTimeId: '',
        billingType: '',
        content: '',
        price: ''
      }
    ])

    const addItem = () => {
      items.value.push({
        expenseTimeId: '',
        billingType: '',
        content: '',
        price: ''
      })
    }

    const removeItem = (index) => {
      if (items.value.length > 1) {
        items.value.splice(index, 1)
      }
    }

    // 验证表单有效性
    watch(
      items,
      (newValue, oldvalue) => {
        let isValid = false
        for (let i = 0; i < newValue.length; i++) {
          if (newValue[i].expenseTimeId && newValue[i].billingType) {
            isValid = true
            break
          }
        }

        // if (!isValid) {
        //   alert('至少选择一个时间段和一个条件')
        // }
      },
      { deep: false }
    )

    return {
      items,
      setItems,
      addItem,
      removeItem
    }
  }
}
</script>
<style lang="scss" scoped>
.stage-billing {
  display: flex;
  flex-direction: column;
}

.stage-billing table {
  border-collapse: collapse;
  text-align: center;
}

.stage-billing th {
  font-weight: normal;
}

.stage-billing td {
  width: 200px;
  padding-right: 5px;
}

.stage-billing button {
  background-color: white;
  color: black;
  font-size: 13px;
  border-color: #dcdfe6;
  cursor: pointer;
  border-radius: 5px;
  margin-top: 5px;
}

.stage-billing button:hover {
  background-color: deepskyblue;
}

.stage-billing select {
  height: 40px;
  padding: 1px;
  border-color: #dcdfe6;
  margin-bottom: 5px;
}

.stage-billing input[type='number'] {
  height: 40px;
  padding: 1px;
  border-color: #dcdfe6;
  margin-bottom: 5px;
}
</style>
