<template>
  <!-- 列表 -->
  <ContentWrap class="full-height">
    <vxe-grid ref="gridRef" v-bind="gridOptions" v-on="gridEvents">
      <template #tools>
        <!-- <vxe-button
          v-hasPermi="[
            'ccms:tempRangConfig:save',
            'ccms:transportSchemeConfig:save',
            'ccms:warnConfig:save'
          ]"
          status="primary"
          @click="addOrUpdateOpen()"
          icon="vxe-icon-add"
          >新建配置
        </vxe-button> -->
        <VxeButton
          style="margin-left: 20px"
          @click="handelSearch"
          status="info"
          icon="vxe-icon-search"
        />
      </template>
      <template #seasonsType="{ row }">
        <dict-tag :type="DICT_TYPE.SEASONS_TYPE" :value="row.seasonsType" />
      </template>
      <template #warnLevel="{ row }">
        <dict-tag :type="DICT_TYPE.WARN_LEVEL" :value="row.warnLevel" />
      </template>
      <template #status="{ row }">
        <Status :title="row.status" />
      </template>
      <template #filter_status="{ column }">
        <div v-for="(option, index) in column.filters" :key="index">
          <vxe-select
            v-model="option.data"
            :options="getStrDictOptions(DICT_TYPE.ENABLE_STATUS)"
            @change="changeFilter(option)"
          />
        </div>
      </template>
      <!-- <template #toolbar_buttons>
        <vxe-radio-group v-model="queryParams.dataCategory" @change="getList">
          <vxe-radio-button label="tempRangConfig">温区配置</vxe-radio-button>
          <vxe-radio-button label="transportSchemeConfig">运输方案</vxe-radio-button>
          <vxe-radio-button label="warnConfig">预警策略</vxe-radio-button>
          <vxe-radio-button label="errorConfig">异常设置</vxe-radio-button>
        </vxe-radio-group>
      </template> -->
      <template #expenseType="{ row }">
        <dict-tag :type="DICT_TYPE.COST_TYPE" :value="row.expenseTypeId" />
      </template>
      <template #operate="{ row }">
        <el-button
          :type="row.status === 'y' ? 'danger' : 'primary'"
          size="small"
          v-if="queryParams.dataCategory !== 'errorConfig'"
          v-hasPermi="[
            'ccms:expenseManagement:enable',
          ]"
          @click="updateStatus(row)"
          plain
          >{{ row.status === 'y' ? '停用' : '启用' }}
        </el-button>
        <el-button
          type="primary"
          size="small"
          @click="addOrUpdateOpen(row)"
          v-hasPermi="[
            'ccms:expenseManagement:update',
          ]"
          plain
          >编辑
        </el-button>
        <!-- <el-button
          type="danger"
          size="small"
          @click="handleDelete(row.id)"
          v-if="queryParams.dataCategory !== 'errorConfig'"
          v-hasPermi="[
            'ccms:tempRangConfig:delete',
            'ccms:transportSchemeConfig:delete',
            'ccms:warnConfig:delete'
          ]"
          plain
          >删除
        </el-button> -->
      </template>
    </vxe-grid>
  </ContentWrap>
  <AddTempRang ref="addTempRangRef" @on-submit="getList" />
  <SearchDrawer ref="searchRef" v-model="queryParams" @search="getList" />
</template>

<script setup lang="ts">
import type { VxeGridInstance, VxeGridListeners, VxeGridProps } from 'vxe-table'
import { tms_web } from '@/api/ccms'
import { DICT_TYPE, getStrDictOptions } from '@/utils/dict'
import { cloneDeep } from 'lodash-es'
import AddTempRang from './AddTempRang.vue'
import SearchDrawer from './SearchDrawer.vue'
import { removeCommonField } from '@/utils/businessData'
import Status from '@/components/Status.vue'

const addTempRangRef = ref()
const searchRef = ref()
const handelSearch = () => {
  searchRef.value?.open()
}
function addOrUpdateOpen(row?: any) {
  addTempRangRef.value.open(removeCommonField(cloneDeep(row)))
}

const changeFilter = (option) => {
  const $grid = gridRef.value
  if ($grid) {
    $grid.updateFilterOptionStatus(option, !!option.data)
  }
}

const message = useMessage()
const { t } = useI18n()

// 更新任务启用状态
async function updateStatus(row: any) {
  let params = {
    id: row.id,
    status: row.status === 'n' ? 'y' : 'n'
  }
  if (row.status === 'y') {
    await message.alertWarning(
      `您确定要停用“${row.name}”吗？停用后不能被后续功能选择，需重新启用后恢复可选择，请慎重考虑！`
    )
  }
  enableDisable(params)
}

// 停用/ 启用
function enableDisable(params: any) {
  tms_web
      .expenseManagement_enable_post({
        data: {
          ...params
        }
      })
      .then((res) => {
        // console.log(res,'启停');
        getList()
      })
  
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    switch (queryParams.dataCategory) {
      case 'tempRangConfig': {
        await tms_web.tempRangConfig_delete_post({ data: { id } })
        break
      }
      case 'transportSchemeConfig': {
        await tms_web.transportSchemeConfig_delete_post({ data: { id } })
        break
      }
      case 'warnConfig': {
        await tms_web.warnConfig_delete_post({ data: { id } })
        break
      }
    }
    message.success(t('common.delSuccess'))
    // 刷新列表
    getList()
  } catch {}
}

const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  dataCategory: 'tempRangConfig',
  status: undefined
})

// 表格实例
const gridRef = ref<VxeGridInstance>()

// 调用vxe grid query方法重载表格
const getList = (item: any = {}) => {
  Object.assign(queryParams, { ...queryParams, ...item })

  if (gridRef.value) {
    gridRef.value.commitProxy('query') // 触发表格重新加载数据
  }
}
// 获取表格数据
const fetchApi = () => {
  return new Promise(async (resolve, reject) => {
    const params: any = cloneDeep(queryParams)
    delete params.dataCategory
    try {
      const res= await tms_web.expenseManagement_queryExpenses_post({ data: params })
      gridOptions.data = res.data?.list || []
        resolve({
          page: {
            total: res.data?.total
          },
          result: gridOptions.data
        })
      
    } catch (e) {
      console.error('加载失败', e)
      reject()
    }
  })
}

const gridOptions = reactive<VxeGridProps>({
  stripe: true,
  border: true,
  keepSource: true,
  height: '100%',
  columnConfig: {
    resizable: true
  },
  pagerConfig: {
    enabled: true,
    pageSize: 10
  },
  editConfig: {
    trigger: 'click',
    mode: 'row',
    showStatus: true
  },
  filterConfig: {
    remote: true
  },
  toolbarConfig: {
    zoom: true,
    custom: true,
    slots: {
      tools: 'tools'
    }
  },
  proxyConfig: {
    props: {
      result: 'result',
      total: 'page.total'
    },
    ajax: {
      // 接收 Promise
      query: ({ page }) => {
        queryParams.pageNo = page.currentPage
        queryParams.pageSize = page.pageSize
        return fetchApi()
      }
    }
  },
  columns: [
    { type: 'seq', width: 70 },
    {
      field: 'name',
      title: '费用名称',
      minWidth: '230px'
    },
    {
      field: 'expenseTypeId',
      title: '费用类型',
      minWidth: '120px',
      slots: {
        default: 'expenseType'
      }
    },
    {
      field: 'remark',
      title: '备注',
      minWidth: '230px'
    },
    {
      field: 'status',
      title: '状态',
      minWidth: '100px',
      slots: {
        default: 'status'
      },
      // filters: [
      //   {
      //     data: ''
      //   }
      // ]
    },
    { title: '操作', width: '220px', slots: { default: 'operate' }, fixed: 'right' }
  ],
  data: []
})

const gridEvents: VxeGridListeners = {
  filterChange(params) {
    queryParams.status = params.datas[0]
    getList()
  }
}

</script>
