<template>
  <div class="add-type">
    <open ref="open" @on-save="submitData" :title="title" :width="800">
      <el-form
        :model="formData"
        :rules="rules"
        ref="formValidate"
        label-width="120px"
        class="demo-ruleForm"
      >
        <el-form-item label="费用名称：" prop="name">
          <el-input type="text" placeholder="请输入" v-model.trim="formData.name" />
        </el-form-item>
        <el-form-item label="费用类型：" prop="expenseTypeId">
          <el-select placeholder="请选择" v-model="formData.expenseTypeId">
            <el-option
              v-for="item in expenseTypeList"
              :value="item.id"
              :label="item.name"
              :key="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="车辆类型：" class="billingType" prop="billingType">
          <el-radio
            v-model="formData.billingType"
            :label="item.name"
            v-for="(item, index) in billingTypeList"
            :key="index"
          >
            {{ item.disName }}
          </el-radio>
        </el-form-item>

        <el-form-item label="" prop="stageBilling">
          <StageBilling :expenseTimeList="expenseTimeList" ref="stageBillingRef" />
        </el-form-item>
        <el-form-item label="备注：">
          <el-input
            v-model.trim="formData.remark"
            :rows="4"
            :maxlength="255"
            placeholder="请输入"
            type="textarea"
          />
        </el-form-item>
        <!-- <el-form-item label="是否启用：" prop="status">
          <el-switch
            v-model="formData.status"
            active-color="#13ce66"
            inactive-color="#ff4949"
            active-value="y"
            inactive-value="n"
          />
        </el-form-item> -->
      </el-form>
    </open>
  </div>
</template>

<script>
import Open from '@/components/Open.vue'
import { tms_web } from '@/api/ccms'
import StageBilling from './StageBilling.vue'
import { getDictOptions } from '@/utils/dict'
import { optionsToEnums, removeCommonField } from '@/utils/businessData'

export default {
  components: { Open, StageBilling },
  setup() {
    const message = useMessage()

    return {
      message
    }
  },
  data() {
    return {
      loading: false,
      expenseTypeList: [],
      billingTypeList: [],
      expenseTimeList: [],
      id: '',
      formData: {
        billingType: 'mileage'
      },
      rules: {
        name: [{ required: true, message: '请输入费用名称', trigger: 'blur' }],
        expenseTypeId: [{ required: true, message: '请选择费用类型', trigger: 'blur' }],
        billingType: [{ required: true, message: '请选择计费方式', trigger: 'blur' }]
      }
    }
  },
  computed: {
    title() {
      return this.id ? '修改配置' : '新建配置'
    }
  },
  methods: {
    // 提交表单数据
    submitData() {
      this.$refs.formValidate.validate((valid) => {
        if (valid) {
          this.saveOrUpdate()
        }
      })
    },

    // id更新/新增
    async saveOrUpdate() {
      try {
        if (this.id) {
          delete this.formData.expenseConfigTimeList
          await tms_web.expenseManagement_update_post({
            data: {
              id: this.id,
              list: this.$refs.stageBillingRef.items,
              ...this.formData
            }
          })
          this.message.success('修改成功！')
        } else {
          // await tms_web.expenseManagement_save_post({
          //   data: {
          //     list: this.$refs.stageBillingRef.items,
          //     ...this.formData
          //   }
          // })
        }
        this.$emit('on-submit')
        //关闭弹窗
        this.$refs.open.close()
      } catch (e) {
        console.log(e)
      }
    },

    // 弹窗打开
    open(row) {
      this.$refs.open.open()
      this.billingTypeList = optionsToEnums(getDictOptions(DICT_TYPE.CCMS_BILLING_ELEMENT))
      if (row) {
        let { id, code, remark, name } = row
        this.id = id
        this.formData = {
          code: code,
          name: name,
          remark: remark
        }

        // 查询详情
        tms_web
          .expenseManagement_get_post({
            data: {
              id: this.id
            }
          })
          .then((res) => {
            if (res.data) {
              this.formData = removeCommonField(res.data || {})
              // if (res.data.billingType) {
              //   this.formData.billingType = res.data.billingType.name;
              // }
              // console.log(res,'详情');
              this.$refs.stageBillingRef.setItems(res.data.expenseConfigTimeList || [])
              // this.$refs.stageBillingRef.items = res.data.expenseConfigTimeList || []
            }
          })
      } else {
        this.id = ''
        this.formData = {}
      }

      // 查询费用类型
      tms_web.expenseType_query_post({}).then((res) => {
        // console.log(res, '费用类型')
        this.expenseTypeList = res.data || []
      })

      // 查询计费时段
      tms_web.expenseTime_query_post({}).then((res) => {
        // console.log(res, '计费时段')
        this.expenseTimeList = res.data?.list || []
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.add-type {
  ::v-deep .el-form-item__label {
    font-size: 13px;
  }

  ::v-deep(.el-form-item__content) {
    width: 400px;
    font-size: 13px;

    .el-textarea__inner {
      font-family: auto;
      font-size: 13px;
    }
  }
}
</style>
