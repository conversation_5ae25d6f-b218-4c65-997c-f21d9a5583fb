<template>
  <div class="add-type">
    <open ref="open" @on-save="submitData" :title="title" :width="600">
      <el-form
        :model="formData"
        :rules="rules"
        ref="formValidate"
        label-width="140px"
        class="demo-ruleForm"
      >
        <el-form-item label="策略名称：" prop="name">
          <el-input type="text" placeholder="请输入" v-model.trim="formData.name" />
        </el-form-item>

        <el-form-item label="预警等级：" class="warnLevel" prop="warnLevel">
          <el-radio
            v-model="formData.warnLevel"
            :label="item.name"
            v-for="(item, index) in warnLevelList"
            :key="index"
          >
            {{ item.disName }}
          </el-radio>
        </el-form-item>

        <el-form-item label="温度偏差值：" prop="tempConfig">
          <el-input
            type="number"
            placeholder="请输入上偏差值"
            v-model.trim="formData.tempUpValue"
            style="margin-right: 10px"
            class="config-input"
          />
          <el-input
            type="number"
            placeholder="请输入下偏差值"
            v-model.trim="formData.tempDownValue"
            class="config-input"
          />
        </el-form-item>

        <el-form-item label="湿度偏差值：" prop="humConfig">
          <el-input
            type="number"
            placeholder="请输入上偏差值"
            v-model.trim="formData.humUpValue"
            style="margin-right: 10px"
            class="config-input"
          />
          <el-input
            type="number"
            placeholder="请输入下偏差值"
            v-model.trim="formData.humDownValue"
            class="config-input"
          />
        </el-form-item>

        <el-form-item label="持续时长(分钟)：" prop="duration">
          <el-input
            type="number"
            placeholder="请输入"
            v-model.trim="formData.duration"
          />
        </el-form-item>

        <el-form-item label="是否启用：" prop="status">
          <el-switch
            v-model="formData.status"
            active-color="#13ce66"
            inactive-color="#ff4949"
            active-value="y"
            inactive-value="n"
          />
        </el-form-item>
      </el-form>
    </open>
  </div>
</template>

<script>
import Open from '@/components/Open.vue'
import { tms_web } from '@/api/ccms'
import { optionsToEnums, removeCommonField } from '@/utils/businessData'
import { getDictOptions } from '@/utils/dict'

export default {
  components: { Open },
  setup() {
    const message = useMessage()
    return {
      message
    }
  },
  data() {
    return {
      loading: false,
      warnLevelList: [],
      id: '',
      formData: {
        warnLevel: 'level1'
      },
      rules: {
        name: [{ required: true, message: '请输入策略名称', trigger: 'blur' }],
        warnLevel: [{ required: true, message: '请选择预警等级', trigger: 'blur' }],
        duration: [{ required: true, message: '请输入持续时长', trigger: 'blur' }],
        tempConfig: [{ required: true, validator: this.validatePass, trigger: 'blur' }],
        humConfig: [{ required: true, validator: this.validatePass2, trigger: 'blur' }]
      }
    }
  },
  computed: {
    title() {
      return this.id ? '修改策略' : '新建策略'
    }
  },
  methods: {
    validatePass(rule, value, callback) {
      if (!this.formData.tempUpValue) {
        callback(new Error('请输入温度上偏差值'))
      } else if (!this.formData.tempDownValue) {
        callback(new Error('请输入温度下偏差值'))
      } else {
        callback()
      }
    },

    validatePass2(rule, value, callback) {
      if (!this.formData.humUpValue) {
        callback(new Error('请输入湿度上偏差值'))
      } else if (!this.formData.humDownValue) {
        callback(new Error('请输入湿度下偏差值'))
      } else {
        callback()
      }
    },
    // 提交表单数据
    submitData() {
      this.$refs.formValidate.validate((valid) => {
        if (valid) {
          this.saveOrUpdate()
        }
      })
    },

    // id更新/新增
    async saveOrUpdate() {
      try {
        if (this.id) {
          delete this.formData.expenseConfigTimeList
          await tms_web.warnConfig_update_post({
            data: {
              id: this.id,
              ...this.formData
            }
          })
          this.message.success('修改成功！')
        } else {
          await tms_web.warnConfig_save_post({
            data: {
              ...this.formData
            }
          })
        }
        this.$emit('on-submit')
        //关闭弹窗
        this.$refs.open.close()
      } catch (e) {
        console.log(e)
      }
    },

    // 弹窗打开
    open(row) {
      this.$refs.open.open()
      if (row) {
        let { id, name } = row
        this.id = id
        this.formData = {
          name: name
        }

        // 查询详情
        tms_web
          .warnConfig_get_post({
            data: {
              id: this.id
            }
          })
          .then((res) => {
            if (res.data) {
              this.formData = removeCommonField(res.data || {})
            }
          })
      }
      this.warnLevelList = optionsToEnums(getDictOptions(DICT_TYPE.WARN_LEVEL))
    }
  }
}
</script>

<style lang="scss" scoped>
.add-type {
  :deep(.el-form-item__label) {
    font-size: 13px;
  }

  :deep(.el-form-item__content) {
    width: 400px;
    font-size: 13px;

    .el-textarea__inner {
      font-family: auto;
      font-size: 13px;
    }
  }
}

.config-input {
  width: 48%;
}
</style>
