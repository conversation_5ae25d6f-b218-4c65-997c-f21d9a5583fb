<template>
  <!-- 列表 -->
  <ContentWrap class="full-height">
    <vxe-grid ref="gridRef" v-bind="gridOptions" v-on="gridEvents">
      <template #tools>
        <vxe-button
          v-if="queryParams.dataCategory !== 'errorConfig'"
          v-hasPermi="[
            'ccms:tempRangConfig:save',
            'ccms:transportSchemeConfig:save',
            'ccms:warnConfig:save'
          ]"
          status="primary"
          @click="addOrUpdateOpen()"
          icon="vxe-icon-add"
          >新增{{ queryParams.dataCategory === 'transportSchemeConfig' ? '方案' : '策略' }}
        </vxe-button>
      </template>
      <template #seasonsType="{ row }">
        <dict-tag :type="DICT_TYPE.SEASONS_TYPE" :value="row.seasonsType" />
      </template>
      <template #warnLevel="{ row }">
        <dict-tag :type="DICT_TYPE.WARN_LEVEL" :value="row.warnLevel" />
      </template>
      <template #status="{ row }">
        <Status :title="row.status" />
      </template>
      <template #filter_status="{ column }">
        <div v-for="(option, index) in column.filters" :key="index">
          <vxe-select
            v-model="option.data"
            :options="getStrDictOptions(DICT_TYPE.ENABLE_STATUS)"
            @change="changeFilter(option)"
          />
        </div>
      </template>
      <template #toolbar_buttons>
        <vxe-radio-group v-model="queryParams.dataCategory" @change="getList">
          <vxe-radio-button label="tempRangConfig">温区配置</vxe-radio-button>
          <vxe-radio-button label="transportSchemeConfig">运输方案</vxe-radio-button>
          <vxe-radio-button label="warnConfig">预警策略</vxe-radio-button>
          <vxe-radio-button label="errorConfig">异常设置</vxe-radio-button>
        </vxe-radio-group>
      </template>
      <template #carCategory="{ row }">
        <dict-tag :type="DICT_TYPE.CAR_CATEGORY" :value="row.carCategory" />
      </template>
      <template #operate="{ row }">
        <el-button
          :type="row.status === 'y' ? 'danger' : 'primary'"
          size="small"
          v-if="queryParams.dataCategory !== 'errorConfig'"
          v-hasPermi="[
            'ccms:tempRangConfig:enable',
            'ccms:transportSchemeConfig:enable',
            'ccms:warnConfig:enable'
          ]"
          @click="updateStatus(row)"
          plain
          >{{ row.status === 'y' ? '停用' : '启用' }}
        </el-button>
        <el-button
          type="primary"
          size="small"
          @click="addOrUpdateOpen(row)"
          v-hasPermi="[
            'ccms:tempRangConfig:update',
            'ccms:transportSchemeConfig:update',
            'ccms:warnConfig:update',
            'ccms:errorConfig:save'
          ]"
          plain
          >编辑
        </el-button>
        <el-button
          type="danger"
          size="small"
          @click="handleDelete(row.id)"
          v-if="queryParams.dataCategory !== 'errorConfig'"
          v-hasPermi="[
            'ccms:tempRangConfig:delete',
            'ccms:transportSchemeConfig:delete',
            'ccms:warnConfig:delete'
          ]"
          plain
          >删除
        </el-button>
      </template>
    </vxe-grid>
  </ContentWrap>
  <AddTempRang ref="addTempRangRef" @on-submit="getList" />
  <AddTransport ref="addTransportRef" @on-submit="getList" />
  <AddWarnConfig ref="addWarnConfigRef" @on-submit="getList" />
  <AddError ref="addErrorRef" @on-submit="getList" />
</template>

<script setup lang="ts">
import type { VxeGridInstance, VxeGridListeners, VxeGridProps } from 'vxe-table'
import { tms_web } from '@/api/ccms'
import { DICT_TYPE, getStrDictOptions } from '@/utils/dict'
import { cloneDeep } from 'lodash-es'
import AddTempRang from './AddTempRang.vue'
import { removeCommonField } from '@/utils/businessData'
import AddTransport from './AddTransport.vue'
import AddWarnConfig from './AddWarnConfig.vue'
import AddError from './AddError.vue'
import Status from '@/components/Status.vue'

const addTempRangRef = ref()
const addTransportRef = ref()
const addWarnConfigRef = ref()
const addErrorRef = ref()

function addOrUpdateOpen(row?: any) {
  switch (queryParams.dataCategory) {
    case 'tempRangConfig': {
      addTempRangRef.value.open(removeCommonField(cloneDeep(row)))
      break
    }
    case 'transportSchemeConfig': {
      addTransportRef.value.open(removeCommonField(cloneDeep(row)))
      break
    }
    case 'warnConfig': {
      addWarnConfigRef.value.open(removeCommonField(cloneDeep(row)))
      break
    }
    case 'errorConfig': {
      addErrorRef.value.open(removeCommonField(cloneDeep(row)))
      break
    }
  }
}

const changeFilter = (option) => {
  const $grid = gridRef.value
  if ($grid) {
    $grid.updateFilterOptionStatus(option, !!option.data)
  }
}

const message = useMessage()
const { t } = useI18n()

// 更新任务启用状态
async function updateStatus(row: any) {
  let params = {
    id: row.id,
    status: row.status === 'n' ? 'y' : 'n'
  }
  if (row.status === 'y') {
    await message.alertWarning(
      `您确定要停用“${row.name}”吗？停用后不能被后续功能选择，需重新启用后恢复可选择，请慎重考虑！`
    )
  }
  enableDisable(params)
}

// 停用/ 启用
function enableDisable(params: any) {
  if (queryParams.dataCategory === 'tempRangConfig') {
    tms_web
      .tempRangConfig_enable_post({
        data: {
          ...params
        }
      })
      .then(() => {
        getList()
      })
  } else if (queryParams.dataCategory === 'transportSchemeConfig') {
    tms_web
      .transportSchemeConfig_enable_post({
        data: {
          ...params
        }
      })
      .then(() => {
        getList()
      })
  } else if (queryParams.dataCategory === 'warnConfig') {
    tms_web
      .warnConfig_enable_post({
        data: {
          ...params
        }
      })
      .then(() => {
        getList()
      })
  }
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    switch (queryParams.dataCategory) {
      case 'tempRangConfig': {
        await tms_web.tempRangConfig_delete_post({ data: { id } })
        break
      }
      case 'transportSchemeConfig': {
        await tms_web.transportSchemeConfig_delete_post({ data: { id } })
        break
      }
      case 'warnConfig': {
        await tms_web.warnConfig_delete_post({ data: { id } })
        break
      }
    }
    message.success(t('common.delSuccess'))
    // 刷新列表
    getList()
  } catch {}
}

const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  dataCategory: 'tempRangConfig',
  status: undefined
})

// 表格实例
const gridRef = ref<VxeGridInstance>()

// 调用vxe grid query方法重载表格
const getList = () => {
  if (gridRef.value) {
    gridRef.value.commitProxy('query') // 触发表格重新加载数据
  }
}
// 获取表格数据
const fetchApi = () => {
  return new Promise(async (resolve, reject) => {
    const params: any = cloneDeep(queryParams)
    delete params.dataCategory
    try {
      let res
      switch (queryParams.dataCategory) {
        case 'tempRangConfig': {
          res = await tms_web.tempRangConfig_query_post({ data: params })
          break
        }
        case 'transportSchemeConfig': {
          res = await tms_web.transportSchemeConfig_query_post({ data: params })
          break
        }
        case 'warnConfig': {
          res = await tms_web.warnConfig_query_post({ data: params })
          break
        }
        case 'errorConfig': {
          res = await tms_web.errorConfig_get_post({ data: params })
          break
        }
      }
      if (queryParams.dataCategory === 'errorConfig') {
        // 永远只有一条数据,需要参考原来的数据
        gridOptions.data = [res.data || {}]
        resolve({
          page: {
            total: 1
          },
          result: gridOptions.data
        })
      } else {
        gridOptions.data = res.data?.list || []
        resolve({
          page: {
            total: res.data?.total
          },
          result: gridOptions.data
        })
      }
    } catch (e) {
      console.error('加载失败', e)
      reject()
    }
  })
}

const gridOptions = reactive<VxeGridProps>({
  stripe: true,
  border: true,
  keepSource: true,
  height: '100%',
  columnConfig: {
    resizable: true
  },
  pagerConfig: {
    enabled: true,
    pageSize: 10
  },
  editConfig: {
    trigger: 'click',
    mode: 'row',
    showStatus: true
  },
  filterConfig: {
    remote: true
  },
  toolbarConfig: {
    zoom: true,
    custom: true,
    slots: {
      buttons: 'toolbar_buttons',
      tools: 'tools'
    }
  },
  proxyConfig: {
    props: {
      result: 'result',
      total: 'page.total'
    },
    ajax: {
      // 接收 Promise
      query: ({ page }) => {
        queryParams.pageNo = page.currentPage
        queryParams.pageSize = page.pageSize
        return fetchApi()
      }
    }
  },
  columns: [],
  data: []
})

const gridEvents: VxeGridListeners = {
  filterChange(params) {
    queryParams.status = params.datas[0]
    getList()
  }
}

watch(
  () => queryParams.dataCategory,
  () => {
    switch (queryParams.dataCategory) {
      case 'tempRangConfig': {
        gridOptions.columns = [
          { type: 'seq', width: 70 },
          {
            field: 'name',
            title: '策略名称',
            minWidth: '230px'
          },
          {
            field: 'carCategory',
            title: '冷藏车类型',
            minWidth: '120px',
            slots: {
              default: 'carCategory'
            }
          },
          {
            field: 'carModelName',
            title: '车辆规格',
            minWidth: '200px',
            formatter: ({ row }) => {
              return `${row.carModelName ? row.carModelName + '-' + row.carLengthName : '-'}`
            }
          },
          {
            field: 'num',
            title: '温区数',
            minWidth: '130px'
          },
          {
            field: 'status',
            title: '状态',
            minWidth: '100px',
            slots: {
              default: 'status',
              filter: 'filter_status'
            },
            filters: [
              {
                data: ''
              }
            ]
          },
          { title: '操作', width: '220px', slots: { default: 'operate' }, fixed: 'right' }
        ]
        break
      }
      case 'transportSchemeConfig': {
        gridOptions.columns = [
          { type: 'seq', width: 70 },
          {
            field: 'name',
            title: '方案名称',
            minWidth: '230px'
          },
          {
            field: 'seasonsType',
            title: '适用季节',
            minWidth: '120px',
            slots: {
              default: 'seasonsType'
            }
          },
          {
            field: 'status',
            title: '状态',
            minWidth: '100px',
            slots: {
              default: 'status',
              filter: 'filter_status'
            },
            filters: [
              {
                data: ''
              }
            ]
          },
          { title: '操作', width: '220px', slots: { default: 'operate' }, fixed: 'right' }
        ]
        break
      }
      case 'warnConfig': {
        gridOptions.columns = [
          { type: 'seq', width: 70 },
          {
            field: 'name',
            title: '策略名称',
            minWidth: '230px'
          },
          {
            field: 'tempUpValue',
            title: '温度偏差值',
            minWidth: '200px',
            formatter: ({ row }) => {
              return `${row.tempUpValue ? row.tempUpValue + '~' + row.tempDownValue : '-'}`
            }
          },
          {
            field: 'humUpValue',
            title: '温度偏差值',
            minWidth: '200px',
            formatter: ({ row }) => {
              return `${row.humUpValue ? row.humUpValue + '~' + row.humDownValue : '-'}`
            }
          },
          {
            field: 'duration',
            title: '持续时长',
            minWidth: '230px'
          },
          {
            field: 'warnLevel',
            title: '预警等级',
            minWidth: '120px',
            slots: {
              default: 'warnLevel'
            }
          },
          {
            field: 'status',
            title: '状态',
            minWidth: '100px',
            slots: {
              default: 'status',
              filter: 'filter_status'
            },
            filters: [
              {
                data: ''
              }
            ]
          },
          { title: '操作', width: '220px', slots: { default: 'operate' }, fixed: 'right' }
        ]
        break
      }
      case 'errorConfig': {
        gridOptions.columns = [
          { type: 'seq', width: 70 },
          {
            field: 'responseError',
            title: '无响应时间(分钟)',
            minWidth: '230px'
          },
          {
            field: 'locationError',
            title: '定位距离跳变(千米)',
            minWidth: '230px'
          },
          { title: '操作', width: '220px', slots: { default: 'operate' }, fixed: 'right' }
        ]
        break
      }
    }
  },
  {
    immediate: true
  }
)
</script>
