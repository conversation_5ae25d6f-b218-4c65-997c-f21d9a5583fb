<template>
  <div class="add-type">
    <open ref="open" @on-save="submitData" :title="title" :width="600">
      <el-form
        :model="formData"
        :rules="rules"
        ref="formValidate"
        label-width="120px"
        class="demo-ruleForm"
      >
        <el-form-item label="策略名称：" prop="name">
          <el-input type="text" placeholder="请输入" v-model.trim="formData.name" />
        </el-form-item>
        <el-form-item label="车型：" prop="carModelId">
          <el-select placeholder="请选择" v-model="formData.carModelId">
            <el-option
              v-for="item in carModelList"
              :value="Number(item.id)"
              :label="item.name"
              :key="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="车长：" prop="carLengthId">
          <el-select placeholder="请选择" v-model="formData.carLengthId">
            <el-option
              v-for="item in carLengthList"
              :value="Number(item.id)"
              :label="item.carLength"
              :key="item.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="车辆类型：" class="carCategory" prop="carCategory">
          <el-radio
            v-model="formData.carCategory"
            :label="item.name"
            v-for="(item, index) in carCategoryList"
            :key="index"
          >
            {{ item.disName }}
          </el-radio>
        </el-form-item>

        <el-form-item label="" prop="stageBilling">
          <StageBilling :expenseTimeList="expenseTimeList" ref="stageBillingRef" />
        </el-form-item>

        <el-form-item label="是否启用：" prop="status">
          <el-switch
            v-model="formData.status"
            active-color="#13ce66"
            inactive-color="#ff4949"
            active-value="y"
            inactive-value="n"
          />
        </el-form-item>
      </el-form>
    </open>
  </div>
</template>

<script>
import Open from '@/components/Open.vue'
import { tms_web } from '@/api/ccms'
import StageBilling from './StageBilling.vue'
import { getDictOptions } from '@/utils/dict'
import { optionsToEnums, removeCommonField } from '@/utils/businessData'

export default {
  components: { Open, StageBilling },
  setup(){
    const message = useMessage()

    return {
      message
    }
  },
  data() {
    return {
      loading: false,
      carModelList: [],
      carLengthList: [],
      carCategoryList: [],
      expenseTimeList: [],
      id: '',
      formData: {
        carCategory: 'A'
      },
      rules: {
        name: [{ required: true, message: '请输入策略名称', trigger: 'blur' }],
        carModelId: [{ required: true, message: '请选择车型', trigger: 'blur' }],
        carLengthId: [{ required: true, message: '请选择车长', trigger: 'blur' }],
        carCategory: [{ required: true, message: '请选择车辆类型', trigger: 'blur' }]
      }
    }
  },
  computed: {
    title() {
      return this.id ? '修改策略' : '新建策略'
    }
  },
  methods: {
    // 提交表单数据
    submitData() {
      this.$refs.formValidate.validate((valid) => {
        if (valid) {
          this.saveOrUpdate()
        }
      })
    },

    // id更新/新增
    async saveOrUpdate() {
      try {
        if (this.id) {
          delete this.formData.expenseConfigTimeList
          await tms_web.tempRangConfig_update_post({
            data: {
              id: this.id,
              detailList: this.$refs.stageBillingRef.items,
              ...this.formData
            }
          })
          this.message.success('修改成功！')
        } else {
          await tms_web.tempRangConfig_save_post({
            data: {
              detailList: this.$refs.stageBillingRef.items,
              ...this.formData
            }
          })
        }
        this.$emit('on-submit')
        //关闭弹窗
        this.$refs.open.close()
      } catch (e) {
        console.log(e)
      }
    },

    // 弹窗打开
    open(row) {
      this.$refs.open.open()
      this.carCategoryList = optionsToEnums(getDictOptions(DICT_TYPE.CAR_CATEGORY))
      if (row) {
        let { id, name } = row
        this.id = id
        this.formData = {
          name: name
        }

        // 查询详情
        tms_web
          .tempRangConfig_get_post({
            data: {
              id: this.id
            }
          })
          .then((res) => {
            if (res.data) {
              this.formData = removeCommonField(res.data || {})
              this.$refs.stageBillingRef.items = this.formData.detailList || []
            }
          })
      }else{
        this.formData.carCategory = this.carCategoryList[0]&&this.carCategoryList[0].name
      }



      // 查询车型
      tms_web.vehicleSpecifications_queryModels_post({}).then((res) => {
        this.carModelList = res.data?.list || []
      })

      // 查询车长
      tms_web.vehicleSpecifications_queryLength_post({}).then((res) => {
        this.carLengthList = res.data?.list || []
        
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.add-type {
  ::v-deep .el-form-item__label {
    font-size: 13px;
  }

  ::v-deep .el-form-item__content {
    width: 400px;
    font-size: 13px;

    .el-textarea__inner {
      font-family: auto;
      font-size: 13px;
    }
  }
}
</style>
