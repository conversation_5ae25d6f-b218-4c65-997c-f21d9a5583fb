<template>
  <div class="add-type">
    <open ref="open" @on-save="submitData" :title="title" :width="600">
      <el-form
        :model="formData"
        :rules="rules"
        ref="formValidate"
        label-width="120px"
        class="demo-ruleForm"
      >
        <el-form-item label="方案名称：" prop="name">
          <el-input type="text" placeholder="请输入" v-model.trim="formData.name" />
        </el-form-item>

        <el-form-item label="适用季节：" class="seasonsType" prop="seasonsType">
          <el-radio
            v-model="formData.seasonsType"
            :label="item.name"
            v-for="(item, index) in seasonsTypeList"
            :key="index"
          >
            {{ item.disName }}
          </el-radio>
        </el-form-item>

        <el-form-item label="关联路线：" prop="pointRouteId">
          <el-select placeholder="请选择" v-model="formData.pointRouteId">
            <el-option
              v-for="item in pointRouteList"
              :value="item.id"
              :label="item.routeName"
              :key="item.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="货物类型：" prop="goodsTypeId">
          <el-select placeholder="请选择" v-model="formData.goodsTypeId">
            <el-option
              v-for="item in goodsTypeList"
              :value="parseInt(item.id)"
              :label="item.name"
              :key="item.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="温控方案：" prop="tempConfig">
          设定温度：
          <el-input
            type="number"
            placeholder="请输入"
            v-model.trim="formData.tempSet"
            class="config-input"
          />
          设定湿度：
          <el-input
            type="number"
            placeholder="请输入"
            v-model.trim="formData.humSet"
            class="config-input"
          />
        </el-form-item>

        <el-form-item label="是否启用：" prop="status">
          <el-switch
            v-model="formData.status"
            active-color="#13ce66"
            inactive-color="#ff4949"
            active-value="y"
            inactive-value="n"
          />
        </el-form-item>
      </el-form>
    </open>
  </div>
</template>

<script>
import Open from '@/components/Open.vue'
import { tms_web } from '@/api/ccms'
import { optionsToEnums, removeCommonField } from '@/utils/businessData'
import { getDictOptions } from '@/utils/dict'

export default {
  components: { Open },
  setup() {
    const message = useMessage()
    return {
      message
    }
  },
  data() {
    return {
      loading: false,
      pointRouteList: [],
      goodsTypeList: [],
      seasonsTypeList: [],
      id: '',
      formData: {
        seasonsType: 'spring'
      },
      rules: {
        name: [{ required: true, message: '请输入方案名称', trigger: 'blur' }],
        seasonsType: [{ required: true, message: '请选择适用季节', trigger: 'blur' }],
        pointRouteId: [{ required: true, message: '请选择关联路线', trigger: 'blur' }],
        goodsTypeId: [{ required: true, message: '请选择货物类型', trigger: 'blur' }],
        tempConfig: [{ required: true, validator: this.validatePass, trigger: 'blur' }]
      }
    }
  },
  computed: {
    title() {
      return this.id ? '修改方案' : '新建方案'
    }
  },
  methods: {
    validatePass(rule, value, callback) {
      if (!this.formData.tempSet) {
        callback(new Error('请输入设定温度'))
      } else if (!this.formData.humSet) {
        callback(new Error('请输入设定湿度'))
      } else {
        callback()
      }
    },

    // 提交表单数据
    submitData() {
      this.$refs.formValidate.validate((valid) => {
        if (valid) {
          this.saveOrUpdate()
        }
      })
    },

    // id更新/新增
    async saveOrUpdate() {
      try {
        if (this.id) {
          delete this.formData.expenseConfigTimeList
          await tms_web.transportSchemeConfig_update_post({
            data: {
              id: this.id,
              ...this.formData
            }
          })
          this.message.success('修改成功！')
        } else {
          await tms_web.transportSchemeConfig_save_post({
            data: {
              ...this.formData
            }
          })
        }
        this.$emit('on-submit')
        //关闭弹窗
        this.$refs.open.close()
      } catch (e) {
        console.log(e)
      }
    },
    // 弹窗打开
    open(row) {
      this.$refs.open.open()
      if (row) {
        let { id, name } = row
        this.id = id
        this.formData = {
          name: name
        }

        // 查询详情
        tms_web
          .transportSchemeConfig_get_post({
            data: {
              id: this.id
            }
          })
          .then((res) => {
            if (res.data) {
              this.formData = removeCommonField(res.data || {})
            }
          })
      }

      this.seasonsTypeList = optionsToEnums(getDictOptions(DICT_TYPE.SEASONS_TYPE))

      // 查询定点路线
      tms_web.routes_queryRoute_post({}).then((res) => {
        this.pointRouteList = res.data?.list || []
      })

      // 查询货物类型
      tms_web.goodsType_queryGoods_post({}).then((res) => {
        this.goodsTypeList = res.data || []
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.add-type {
  :deep(.el-form-item__label) {
    font-size: 13px;
  }

  :deep(.el-form-item__content) {
    width: 400px;
    font-size: 13px;

    .el-textarea__inner {
      font-family: auto;
      font-size: 13px;
    }
  }
}

.config-input {
  width: 29%;
}
</style>
