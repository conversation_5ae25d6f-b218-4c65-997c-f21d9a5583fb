<template>
  <div class="stage-billing">
    <!-- 表单区域 -->
    <table>
      <thead>
      <tr>
        <th>温区名称</th>
        <th>设定温度</th>
        <th>设定湿度</th>
      </tr>
      </thead>
      <tbody>
      <tr v-for="(item, index) in items" :key="index">
        <td>
          <el-input type="text" v-model.number="item.name" />
        </td>
        <td>
          <el-input type="number" v-model.number="item.tempSet" />
        </td>
        <td>
          <el-input type="number" v-model.number="item.humSet" />
        </td>
        <td class="flex items-center gap-2 ml-[5px] select-none">
          <div v-if="index===0" @click="addItem" class="flex items-center gap-1 cursor-pointer text-nowrap"><img class="size-[18px]" alt="+" src="@/assets/images/<EMAIL>"/><span>添加</span></div>
          <div v-else @click="removeItem(index)" class="flex items-center gap-1 cursor-pointer text-nowrap"><img class="size-[18px]" alt="-" src="@/assets/images/<EMAIL>"/><span>删除</span></div>
        </td>
      </tr>
      </tbody>
    </table>
  </div>
</template>

<script>
import { ref } from "vue";

export default {
  name: "StageBilling",
  props: {
    expenseTimeList: {
      type: Array,
      required: true
    }
  },
  setup(props) {
    const items = ref([{
      name: "",
      tempSet: "",
      humSet: ""
    }]);

    const addItem = () => {
      items.value.push({
        name: "",
        tempSet: "",
        humSet: ""
      });
    };

    const removeItem = index => {
      if (items.value.length > 1) {
        items.value.splice(index, 1);
      }
    };

    // // 验证表单有效性
    // watch(items, newValue => {
    //   let isValid = false;
    //   console.log(11);
    //
    //   for (let i = 0; i < newValue.length; i++) {
    //     if (newValue[i].expenseTimeId && newValue[i].billingType) {
    //       isValid = true;
    //       break;
    //     }
    //   }
    //
    //   if (!isValid) {
    //     alert("至少选择一个时间段和一个条件");
    //   }
    // }, { deep: false });

    return {
      items,
      addItem,
      removeItem
    };
  },
  methods: {
    setItems(data) {
      this.items = data;
    }
  }
};
</script>
<style lang="scss" scoped>
.stage-billing {
  display: flex;
  flex-direction: column;
}

.stage-billing table {
  border-collapse: collapse;
  text-align: center;
}

.stage-billing th {
  font-weight: normal;
}

.stage-billing td {
  padding-right: 5px;

}

.stage-billing button {
  background-color: white;
  color: black;
  font-size: 13px;
  border-color: #dcdfe6;
  cursor: pointer;
  border-radius: 5px;
  margin-top: 5px;
}

.stage-billing button:hover {
  background-color: deepskyblue;
}

.stage-billing select {
  height: 40px;
  padding: 1px;
  border-color: #dcdfe6;
  margin-bottom: 5px;
}

.stage-billing input[type="number"] {
  height: 40px;
  padding: 1px;
  border-color: #dcdfe6;
  margin-bottom: 5px;
}
</style>
