<template>
  <div class="add-type">
    <open ref="open" @on-save="submitData" :title="title" :width="600">
      <el-form
        :model="formData"
        :rules="rules"
        ref="formValidate"
        label-width="170px"
        class="demo-ruleForm"
      >
        <el-form-item label="无响应时间(分钟)：" prop="responseError">
          <el-input
            size="medium"
            type="number"
            placeholder="请输入"
            v-model.trim="formData.responseError"
          />
        </el-form-item>

        <el-form-item label="定位距离跳变(千米)：" prop="locationError">
          <el-input
            size="medium"
            type="number"
            placeholder="请输入"
            v-model.trim="formData.locationError"
          />
        </el-form-item>
      </el-form>
    </open>
  </div>
</template>

<script>
import Open from '@/components/Open.vue'
import { tms_web } from '@/api/ccms'
import { removeCommonField } from '@/utils/businessData'

export default {
  components: { Open },
  setup() {
    const message = useMessage()
    return {
      message
    }
  },
  data() {
    return {
      loading: false,
      id: '',
      formData: {},
      rules: {}
    }
  },
  computed: {
    title() {
      return this.id ? '修改设置' : '新建设置'
    }
  },
  methods: {
    // 提交表单数据
    submitData() {
      this.$refs.formValidate.validate((valid) => {
        if (valid) {
          this.saveOrUpdate()
        }
      })
    },
    // id更新/新增
    async saveOrUpdate() {
      try {
        if (this.id) {
          await tms_web.errorConfig_save_post({
            data: {
              id: this.id,
              ...this.formData
            }
          })
          this.message.success('修改成功！')
        } else {
          await tms_web.errorConfig_save_post({
            data: {
              ...this.formData
            }
          })
        }
        this.$emit('on-submit')
        //关闭弹窗
        this.$refs.open.close()
      } catch (e) {
        console.log(e)
      }
    },

    // 弹窗打开
    open(row) {
      this.$refs.open.open()
      if (row) {
        let { id, name } = row
        this.id = id
        this.formData = {
          name: name
        }

        // 查询详情
        tms_web
          .errorConfig_get_post({
            data: {
              id: this.id
            }
          })
          .then((res) => {
            if (res.data) {
              this.formData = removeCommonField(res.data || {})
            }
          })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.add-type {
  :deep(.el-form-item__label) {
    font-size: 13px;
  }

  :deep(.el-form-item__content) {
    width: 300px;
    font-size: 13px;

    .el-textarea__inner {
      font-family: auto;
      font-size: 13px;
    }
  }
}
</style>
