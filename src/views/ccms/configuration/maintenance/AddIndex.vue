<template>
  <div class="add-index">
    <open ref="open" @on-save="submitData" :title="title" :width="500">
      <el-form
        :model="formData"
        :rules="rules"
        ref="formValidate"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-form-item label="维修厂名称：" prop="shopName">
          <el-input
            type="text"
            placeholder="请输入"
            v-model.trim="formData.shopName"
          />
        </el-form-item>
        <el-form-item label="联系人：" prop="contacts">
          <el-input
            type="text"
            placeholder="请输入"
            v-model.trim="formData.contacts"
          />
        </el-form-item>
        <el-form-item label="联系电话：" prop="contactsPhone">
          <el-input
            type="text"
            placeholder="请输入"
            v-model.trim="formData.contactsPhone"
          />
        </el-form-item>
        <el-form-item label="地址：" prop="contactsAddress">
          <el-input
            type="text"
            placeholder="请输入"
            v-model.trim="formData.contactsAddress"
          />
        </el-form-item>
        <el-form-item label="备注：">
          <el-input
            type="textarea"
            :rows="4"
            :maxlength="255"
            placeholder="请输入"
            v-model.trim="formData.remark"
          />
        </el-form-item>
      </el-form>
    </open>
  </div>
</template>

<script>
import Open from '@/components/Open.vue'
import { tms_web } from '@/api/ccms'

export default {
  components: { Open },
  setup() {
    const message = useMessage()

    return {
      message
    }
  },
  data() {
    return {
      id: '',
      formData: {},
      rules: {
        shopName: [{ required: true, message: '请输入维修厂名称', trigger: 'blur' }],
        contacts: [{ required: true, message: '请输入联系人', trigger: 'blur' }],
        contactsPhone: [{ required: true, message: '请输入联系电话', trigger: 'blur' }],
        contactsAddress: [{ required: true, message: '请输入地址', trigger: 'blur' }]
      }
    }
  },
  computed: {
    title() {
      return this.id ? '修改配置' : '新建配置'
    }
  },
  methods: {
    // 提交表单数据
    submitData() {
      this.$refs.formValidate.validate((valid) => {
        if (valid) {
          this.saveOrUpdate()
        }
      })
    },
    // id更新/新增
    async saveOrUpdate() {
      try {
        if (this.id) {
          await tms_web.maintenance_editCarInquire_post({
            data: {
              id: this.id,
              ...this.formData
            }
          })
          this.message.success('修改成功！')
        } else {
          await tms_web.maintenance_saveCarInquire_post({
            data: {
              ...this.formData
            }
          })
        }
        this.$emit('on-submit')
        this.$refs.open.close()
      } catch (e) {
        console.log(e)
      }
    },

    // 弹窗打开
    open(row) {
      this.$refs.open.open()
      if (row) {
        let { id, shopName, contacts, remark, contactsPhone, contactsAddress } = row
        this.id = id
        this.formData = {
          shopName: shopName,
          contacts: contacts,
          contactsPhone: contactsPhone,
          contactsAddress: contactsAddress,
          remark: remark
        }
      } else {
        this.id = ''
        this.formData = {}
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.add-index {
  :deep(.el-form-item__label) {
    font-size: 12px;
  }

  :deep(.el-form-item__content) {
    width: 320px;
    font-size: 13px;

    .el-textarea__inner {
      font-family: auto;
      font-size: 13px;
    }
  }
}
</style>
