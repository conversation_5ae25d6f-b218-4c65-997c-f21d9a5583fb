<template>
  <div class="add-type">
    <open ref="open" @on-save="submitData" :title="title" :width="500">
      <el-form
        :model="formData"
        :rules="rules"
        ref="formValidate"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-form-item label="规则名称：" prop="name">
          <el-input type="text" placeholder="请输入" v-model.trim="formData.name" />
        </el-form-item>
        <el-form-item label="用户类型：" prop="userType">
          <el-select placeholder="请选择" v-model="formData.userType">
            <el-option value="customer" label="客户" />
          </el-select>
        </el-form-item>

        <el-form-item label="选择费用：" prop="expenseId">
          <el-select placeholder="请选择" multiple v-model="formData.expenseId">
            <el-option
              v-for="item in expenseManageList"
              :value="item.id"
              :label="item.name"
              :key="item.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="选择车型：" prop="carModelId">
          <el-select placeholder="请选择" multiple v-model="formData.carModelId">
            <el-option
              v-for="item in carModelList"
              :value="item.id"
              :label="item.name"
              :key="item.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="选择车长：" prop="carLengthId">
          <el-select placeholder="请选择" multiple v-model="formData.carLengthId">
            <el-option
              v-for="item in carLengthList"
              :value="item.id"
              :label="item.carLength"
              :key="item.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="选择路线：" prop="routeId">
          <el-select placeholder="请选择" multiple v-model="formData.routeId">
            <el-option
              v-for="item in routesList"
              :value="item.id"
              :label="item.routeName"
              :key="item.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="规则描述：">
          <el-input
            type="textarea"
            :rows="4"
            :maxlength="255"
            placeholder="请输入"
            v-model.trim="formData.remark"
          />
        </el-form-item>

        <el-form-item label="是否启用：" prop="status">
          <el-switch
            v-model="formData.status"
            active-color="#13ce66"
            inactive-color="#ff4949"
            active-value="y"
            inactive-value="n"
          />
        </el-form-item>
      </el-form>
    </open>
  </div>
</template>

<script>
import Open from '@/components/Open.vue'
import { tms_web } from '@/api/ccms'
import { removeCommonField } from '@/utils/businessData'

export default {
  components: { Open },
  setup() {
    const message = useMessage()
    return {
      message
    }
  },
  data() {
    return {
      id: '',
      formData: {},
      expenseManageList: [],
      carModelList: [],
      carLengthList: [],
      routesList: [],
      rules: {
        name: [{ required: true, message: '请输入规则名称', trigger: 'blur' }],
        userType: [{ required: true, message: '请选择用户类型', trigger: 'blur' }],
        expenseId: [{ required: true, message: '请选择费用', trigger: 'blur' }],
        carModelId: [{ required: true, message: '请选择车型', trigger: 'blur' }],
        carLengthId: [{ required: true, message: '请选择车长', trigger: 'blur' }],
        routeId: [{ required: true, message: '请选择路线', trigger: 'blur' }]
      }
    }
  },
  computed: {
    title() {
      return this.id ? '修改规则' : '新建规则'
    }
  },
  methods: {
    // 提交表单数据
    submitData() {
      this.$refs.formValidate.validate((valid) => {
        if (valid) {
          this.addOrUpdate()
        }
      })
    },

    // id更新/新增
    async addOrUpdate() {
      try {
        let params = {
          id: this.formData.id,
          name: this.formData.name,
          userType: this.formData.userType,
          remark: this.formData.remark,
          status: this.formData.status,
          configTypeList: []
        }
        //组装费用数据
        for (let item of this.formData.expenseId || []) {
          params.configTypeList.push({
            configId: item,
            configType: 'expense'
          })
        }

        //组装车型数据
        for (let item of this.formData.carModelId || []) {
          params.configTypeList.push({
            configId: item,
            configType: 'carModel'
          })
        }

        //组装车长数据
        for (let item of this.formData.carLengthId || []) {
          params.configTypeList.push({
            configId: item,
            configType: 'carLength'
          })
        }

        //组装路线数据
        for (let item of this.formData.routeId || []) {
          params.configTypeList.push({
            configId: item,
            configType: 'routes'
          })
        }

        if (this.id) {
          await tms_web.businessRule_update_post({
            data: {
              id: this.id,
              ...params
            },
            load: {
              fullLoading: true
            }
          })
          this.message.success('修改成功！')
        } else {
          await tms_web.businessRule_save_post({
            data: {
              ...params
            },
            load: {
              fullLoading: true
            }
          })
        }
        this.$emit('on-submit')
        this.$refs.open.close()
      } catch (e) {
        console.log(e)
      }
    },

    // 弹窗打开
    open(row) {
      this.$refs.open.open()

      // 查询费用
      tms_web
        .expenseManagement_queryExpenses_post({
          data: {
            pageNo: 1,
            pageSize: 999999
          }
        })
        .then((res) => {
          this.expenseManageList = res.data?.list || []
        })

      // 查询车型
      tms_web.vehicleSpecifications_queryModels_post({}).then((res) => {
        this.carModelList = res.data?.list || []
      })

      // 查询车长
      tms_web.vehicleSpecifications_queryLength_post({}).then((res) => {
        this.carLengthList = res.data?.list || []
      })

      // 查询定点路线
      tms_web.routes_queryRoute_post({}).then((res) => {
        this.routesList = res.data?.list || []
      })

      if (row) {
        let { id } = row
        this.id = id
        // 查询定点路线
        tms_web
          .businessRule_get_post({
            data: {
              id: this.id
            }
          })
          .then((res) => {
            const temp = removeCommonField(res.data || {})
            temp['expenseId'] = []
            temp['carModelId'] = []
            temp['carLengthId'] = []
            temp['routeId'] = []
            temp['expenseId'] = []

            if (temp.configTypeList) {
              //组装路线数据
              for (let item of temp.configTypeList || []) {
                if (item.configType === 'expense') {
                  temp.expenseId.push(item.configId)
                } else if (item.configType === 'carModel') {
                  temp.carModelId.push(item.configId)
                } else if (item.configType === 'carLength') {
                  temp.carLengthId.push(item.configId)
                } else if (item.configType === 'routes') {
                  temp.routeId.push(item.configId)
                }
              }
            }
            this.formData = temp
          })
      } else {
        this.id = ''
        this.formData = {}
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.add-type {
  :deep(.el-form-item__label) {
    font-size: 13px;
  }

  :deep(.el-form-item__content) {
    font-size: 13px;

    .el-textarea__inner {
      font-family: auto;
      font-size: 13px;
    }
  }
}
</style>
