<template>
  <div class="add-index">
    <open ref="open" @on-save="submitData" :title="title" :width="500">
      <el-form
        :model="formData"
        :rules="rules"
        ref="formValidate"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-form-item label="时段名称：" prop="name">
          <el-input type="text" placeholder="请输入" v-model.trim="formData.name" />
        </el-form-item>

        <el-form-item label="选择时间" prop="date">
          <el-date-picker
            v-model="formData.date"
            style="width: 320px"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            placeholder="选择日期"
          />
        </el-form-item>
        <el-form-item label="备注：">
          <el-input
            type="textarea"
            :rows="4"
            :maxlength="255"
            placeholder="请输入"
            v-model.trim="formData.remark"
          />
        </el-form-item>
      </el-form>
    </open>
  </div>
</template>

<script>
import Open from '@/components/Open.vue'
import { tms_web } from '@/api/ccms'
import dayjs from 'dayjs'

export default {
  components: { Open },
  setup() {
    const message = useMessage()
    return {
      message
    }
  },
  data() {
    return {
      id: '',
      formData: {},
      rules: {
        name: [{ required: true, message: '请输入时段名称', trigger: 'blur' }],
        date: [{ required: true, message: '请选择时间', trigger: 'blur' }]
      }
    }
  },
  computed: {
    title() {
      return this.id ? '修改配置' : '新建配置'
    }
  },
  methods: {
    // 提交表单数据
    submitData() {
      this.$refs.formValidate.validate((valid) => {
        if (valid) {
          this.expenseType()
        }
      })
    },
    // id更新/新增
    async expenseType() {
      try {
        let params = JSON.parse(JSON.stringify(this.formData))
        if (params.date && params.date.length) {
          params.startTime = params.date ? dayjs(params.date[0]).format('YYYY-MM-DD 00:00:00') : ''
          params.endTime = params.date ? dayjs(params.date[1]).format('YYYY-MM-DD 23:59:59') : ''
        }
        delete params.date
        if (this.id) {
          await tms_web.expenseTime_update_post({
            data: {
              id: this.id,
              ...params
            }
          })
          this.message.success('修改成功！')
        } else {
          await tms_web.expenseTime_save_post({
            data: {
              ...params
            }
          })
        }
        this.$emit('on-submit')
        this.$refs.open.close()
      } catch (e) {
        console.log(e)
      }
    },

    // 弹窗打开
    open(row) {
      this.$refs.open.open()
      if (row) {
        let { id, startTime, endTime, remark, name } = row
        this.id = id
        this.formData = {
          name: name,
          startTime: startTime,
          endTime: endTime,
          date: [startTime, endTime],
          remark: remark
        }
      } else {
        this.id = ''
        this.formData = {}
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.add-index {
  :deep(.el-form-item__label) {
    font-size: 13px;
  }

  :deep(.el-form-item__content) {
    font-size: 13px;

    .el-textarea__inner {
      font-family: auto;
      font-size: 13px;
    }
  }
}
</style>
