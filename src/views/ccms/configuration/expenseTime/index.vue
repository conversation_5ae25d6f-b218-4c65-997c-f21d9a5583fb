<template>
  <!-- 列表 -->
  <ContentWrap class="full-height">
    <vxe-grid ref="gridRef" v-bind="gridOptions" v-on="gridEvents">
      <template #tools>
        <vxe-button
          v-hasPermi="['ccms:maintenance:saveCarInquire']"
          status="primary"
          @click="addOrUpdateOpen()"
          icon="vxe-icon-add"
          >新建
        </vxe-button>
      </template>
      <template #filter_status="{ column }">
        <div v-for="(option, index) in column.filters" :key="index">
          <vxe-select
            v-model="option.data"
            :options="getStrDictOptions(DICT_TYPE.ENABLE_STATUS)"
            @change="changeFilter(option)"
          />
        </div>
      </template>
      <template #status="{ row }">
        <Status :title="row.status" />
      </template>
      <template #operate="{ row }">
        <el-button
          v-hasPermi="['ccms:maintenance:enable']"
          :type="row.status === 'y' ? 'danger' : 'primary'"
          size="small"
          @click="updateStatus(row)"
          plain
          >{{ row.status === 'y' ? '停用' : '启用' }}
        </el-button>
        <el-button
          type="primary"
          size="small"
          @click="addOrUpdateOpen(row)"
          v-hasPermi="['ccms:maintenance:editCarInquire']"
          plain
          >编辑
        </el-button>
        <el-button
          type="danger"
          size="small"
          @click="handleDelete(row.id)"
          v-hasPermi="['ccms:maintenance:delete']"
          plain
          >删除
        </el-button>
      </template>
    </vxe-grid>
  </ContentWrap>

  <AddIndex ref="addOpenRef" @on-submit="getList" />
</template>

<script setup lang="ts">
import type { VxeGridInstance, VxeGridListeners, VxeGridProps } from 'vxe-table'
import { tms_web } from '@/api/ccms'
import { DICT_TYPE, getStrDictOptions } from '@/utils/dict'
import { cloneDeep } from 'lodash-es'
import { removeCommonField } from '@/utils/businessData'
import AddIndex from './AddIndex.vue'
import Status from '@/components/Status.vue'
import { dateFormatter2ByOptions, dateFormatterByOptions } from '@/utils/formatTime'

const addOpenRef = ref()

function addOrUpdateOpen(row?: any) {
  addOpenRef.value.open(removeCommonField(cloneDeep(row)))
}

const message = useMessage()

// 更新任务启用状态
async function updateStatus(row: any) {
  let params = {
    id: row.id,
    status: row.status === 'n' ? 'y' : 'n'
  }
  if (row.status === 'y') {
    await message.alertWarning(
      `您确定要停用“${row.name}”吗？停用后不能被后续功能选择，需重新启用后恢复可选择，请慎重考虑！`
    )
  }
  tms_web
    .expenseTime_enable_post({
      data: {
        ...params
      } as any
    })
    .then(() => {
      getList()
    })
}

const { t } = useI18n()
/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    await tms_web.expenseTime_delete_post({ data: { id } })
    message.success(t('common.delSuccess'))
    // 刷新列表
    getList()
  } catch {}
}

const changeFilter = (option) => {
  const $grid = gridRef.value
  if ($grid) {
    $grid.updateFilterOptionStatus(option, !!option.data)
  }
}

const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  status: undefined
})

// 表格实例
const gridRef = ref<VxeGridInstance>()

// 调用vxe grid query方法重载表格
const getList = () => {
  if (gridRef.value) {
    gridRef.value.commitProxy('query') // 触发表格重新加载数据
  }
}
// 获取表格数据
const fetchApi = () => {
  return new Promise(async (resolve) => {
    const params: any = cloneDeep(queryParams)
    const res = await tms_web.expenseTime_query_post({ data: params })
    gridOptions.data = res.data?.list || []
    resolve({
      page: {
        total: res.data?.total
      },
      result: res.data?.list
    })
  })
}

const gridOptions = reactive<
  VxeGridProps<GetApiResByListItem<typeof tms_web.expenseTime_query_post>>
>({
  stripe: true,
  border: true,
  keepSource: true,
  height: '100%',
  columnConfig: {
    resizable: true
  },
  pagerConfig: {
    enabled: true,
    pageSize: 10
  },
  filterConfig: {
    remote: true
  },
  editConfig: {
    trigger: 'click',
    mode: 'row',
    showStatus: true
  },
  toolbarConfig: {
    zoom: true,
    custom: true,
    slots: {
      tools: 'tools'
    }
  },
  proxyConfig: {
    props: {
      result: 'result',
      total: 'page.total'
    },
    ajax: {
      // 接收 Promise
      query: ({ page }) => {
        queryParams.pageNo = page.currentPage
        queryParams.pageSize = page.pageSize
        return fetchApi()
      }
    }
  },
  columns: [
    { type: 'seq', width: 70 },
    {
      field: 'name',
      title: '时段名称',
      minWidth: '120px'
    },
    {
      field: 'startTime',
      title: '时间段',
      minWidth: '120px',
      formatter({ row }) {
        return `${dateFormatter2ByOptions({ cellValue: row.startTime || '' })} ~ ${dateFormatter2ByOptions({ cellValue: row.endTime || '' })}`
      }
    },
    { field: 'remark', title: '备注', minWidth: '220px' },
    {
      field: 'status',
      title: '状态',
      minWidth: '100px',
      slots: {
        default: 'status',
        filter: 'filter_status'
      },
      filters: [
        {
          data: ''
        }
      ]
    },
    { title: '操作', width: 220, slots: { default: 'operate' }, fixed: 'right' }
  ],
  data: []
})

const gridEvents: VxeGridListeners = {
  filterChange(params) {
    queryParams.status = params.datas[0]
    getList()
  }
}
</script>
