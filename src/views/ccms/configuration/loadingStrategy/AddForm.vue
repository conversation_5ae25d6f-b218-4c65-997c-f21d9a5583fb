<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="策略名称" required prop="name">
        <el-input v-model="formData.name" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="策略描述" prop="remark">
        <!-- <el-input v-model="formData.remark" placeholder="请输入厂商名称" /> -->
        <el-input v-model="formData.remark" :rows="3" type="textarea" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="兼容商品：" required prop="includeGoods">
        <div class="row_cls">
          <el-input
            size="large"
            type="text"
            placeholder="请输入"
            v-model.trim="formData.include"
            class="good_input"
            @keydown.enter="addIncludeGoods"
          />
          <div
            @click="addIncludeGoods"
            class="flex items-center gap-1 cursor-pointer text-nowrap ml-4"
            ><img class="size-[18px]" alt="+" src="@/assets/images/<EMAIL>" /><span
              >添加</span
            ></div
          >
        </div>

        <div class="good_div" >
          <el-button
            v-for="(btn, index) in includeGoodsButtons"
            :key="index"
            class="tag-btn"
            title="点击删除"
            @click="removeIncludeGoods(index)"
          >
            <i class="icon"></i>
            {{ btn.text }}
          </el-button>
        </div>
      </el-form-item>
      <el-form-item label="排斥商品：" required prop="excludeGoods">
        <div class="row_cls">
          <el-input
            size="large"
            type="text"
            placeholder="请输入"
            v-model.trim="formData.exclude"
            class="good_input"
            @keydown.enter="addExcludeGoods"
          />
          <div
            @click="addExcludeGoods"
            class="flex items-center gap-1 cursor-pointer text-nowrap ml-4"
            ><img class="size-[18px]" alt="+" src="@/assets/images/<EMAIL>" /><span
              >添加</span
            ></div
          >
        </div>

        <div class="good_div" >
          <el-button
            v-for="(btn, index) in excludeGoodsButtons"
            :key="index"
            class="tag-btn"
            title="点击删除"
            @click="removeExcludeGoods(index)"
          >
            <i class="icon"></i>
            {{ btn.text }}
          </el-button>
        </div>
      </el-form-item>
      <!-- <el-form-item label="联系人" required prop="contactsName">
        <el-input v-model="formData.contactsName" placeholder="请输入联系人" />
      </el-form-item>
      <el-form-item label="联系电话" required prop="phone">
        <el-input v-model="formData.phone" placeholder="请输入联系人电话" />
      </el-form-item> -->
      <el-form-item label="是否启用：" prop="status">
        <el-switch
          v-model="formData.status"
          style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949"
          active-value="y"
          inactive-value="n"
        />
      </el-form-item>
      <!-- <el-form-item label="地址" required prop="includeGoods">
        <el-input v-model="formData.includeGoods" placeholder="请输入地址" />
      </el-form-item> -->
    </el-form>
    <template #footer>
      <el-button
        @click="submitForm"
        type="primary"
        v-hasPermi="['ccms:loadingStrategyConfig:save']"
        :disabled="formLoading"
        >确 定
      </el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { tms_web } from '@/api/ccms'

/** 合同信息 表单 */
defineOptions({ name: 'ContractMessageForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const updateId = ref()
const formData = reactive({
  name: undefined,
  remark: undefined,
  include: undefined,
  includeGoods: '',
  exclude: undefined,
  excludeGoods: '',
  status: undefined
})
const formRules = reactive({
  name: [{ required: true, message: '厂商名称', trigger: 'blur' }],
   includeGoods: [
      { required: true, message: "请输入兼容商品并点击添加", trigger: "blur" }
    ],
    excludeGoods: [
      { required: true, message: "请输入排斥商品并点击添加", trigger: "blur" }
    ]
  
})
const formRef = ref() // 表单 Ref
interface Object {
  id: any
  name: any
  remark: any
  include: any
  includeGoods: any
  exclude: any
  excludeGoods: any
  status: any
}

/** 打开弹窗 */
const open = async (type: string, row?: Object) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (row?.id) {
    updateId.value = row.id

    // formData.value.name = row.name
    // formData.value.remark = row.remark
    // formData.value.include = row.include
    // formData.value.includeGoods = row.includeGoods

    // 查询
    tms_web
      .loadingStrategyConfig_get_post({
        data: {
          id: updateId.value
        }
      })
      .then((res) => {
        // formData = (res.data || {});
        Object.assign(formData, res.data)
        if (formData.includeGoods) {
          let tmp = formData.includeGoods.split(',')
          for (const tmpElement of tmp) {
            includeGoodsTmp.value.push(tmpElement)
            includeGoodsButtons.value.push({
              text: tmpElement
            })
          }
          formData.includeGoods = includeGoodsTmp.value
        }
        if (formData.excludeGoods) {
          let tmp = formData.excludeGoods.split(',')
          for (const tmpElement of tmp) {
            excludeGoodsTmp.value.push(tmpElement)
            excludeGoodsButtons.value.push({
              text: tmpElement
            })
          }
          formData.excludeGoods = excludeGoodsTmp.value
        }
      })
    // formLoading.value = true
    // try {
    //   formData.value = await ContractMessageApi.getContractMessage(id)
    // } finally {
    //   formLoading.value = false
    // }
  } else {
    updateId.value = ''
    // formData.value = {};
    // Object.assign(formData,{})
    // includeGoodsButtons.value = [];
    // excludeGoodsButtons.value = [];
  }
}
//临时
const includeGoodsTmp: any = ref([])
const excludeGoodsTmp: any = ref([])
const includeGoodsButtons: any = ref([])
const excludeGoodsButtons: any = ref([])
//添加兼容商品
const addIncludeGoods = () => {
  if (formData.include && !includeGoodsTmp.value.includes(formData.include)) {
    includeGoodsTmp.value.push(formData.include)
    formData.includeGoods = includeGoodsTmp.value
    includeGoodsButtons.value.push({
      text: formData.include
    })
    formData.include = undefined //添加后清空
  } else if (includeGoodsTmp.value.includes(formData.include)) {
    message.warning('商品已存在')
  } else if (!formData.include) {
    message.warning('请输入商品名称')
  }
}
//移除兼容商品
const removeIncludeGoods = (index: any) => {
  includeGoodsTmp.value.splice(index, 1)
  includeGoodsButtons.value.splice(index, 1)
}

//添加排斥商品
const addExcludeGoods = () => {
  if (formData.exclude && !excludeGoodsTmp.value.includes(formData.exclude)) {
    excludeGoodsTmp.value.push(formData.exclude)
    formData.excludeGoods = excludeGoodsTmp.value
    excludeGoodsButtons.value.push({
      text: formData.exclude
    })
    formData.exclude = undefined //添加后清空
  } else if (excludeGoodsTmp.value.includes(formData.exclude)) {
    message.warning('商品已存在')
  } else if (!formData.exclude) {
    message.warning('请输入商品名称')
  }
}
//移除排斥商品
const removeExcludeGoods = (index: any) => {
  excludeGoodsTmp.value.splice(index, 1)
  excludeGoodsButtons.value.splice(index, 1)
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗
// id更新/新增
// const  expenseType= async()=> {
//     try {
//       if (id) {
//         await tms_web.factoryManage_editFactoryManage_post({
//           data: {
//             id: id,
//             ...formData
//           }
//         });
//         $notify({
//           title: "成功",
//           message: "修改成功！",
//           type: "success"
//         });
//       } else {
//         await tms_web.factoryManage_saveFactoryManage_post({
//           data: {
//             ...formData
//           }
//         });
//       }
//       $emit("on-submit");
//       $refs.open.close();
//     } catch (e) {
//       console.log(e);
//     }
//   }
/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()

  // 提交请求
  formLoading.value = true
  try {
    // const data = formData.value

    let data: any = {
      name: formData.name,
      remark: formData.remark,
      includeGoods: formData.includeGoods ? formData.includeGoods.join(',') : '',
      excludeGoods: formData.excludeGoods ? formData.excludeGoods.join(',') : '',
      status: formData.status
    }
    if (formType.value === 'create') {
      // console.log(data, 'formType');
      await tms_web.loadingStrategyConfig_save_post({
        data: {
          ...data
        }
      })
      // console.log(res, '新增成功');

      message.success(t('common.createSuccess'))
    } else {
      await tms_web.loadingStrategyConfig_update_post({
        data: {
          id: updateId.value,
          ...data
        }
      })
      // console.log(res, '修改成功');

      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formRef.value?.resetFields()
  Object.assign(formData, {
    name: undefined,
    remark: undefined,
    include: undefined,
    includeGoods: '',
    exclude: undefined,
    excludeGoods: '',
    status: undefined
  })
  includeGoodsButtons.value = []
  excludeGoodsButtons.value = []
  includeGoodsTmp.value = []
  excludeGoodsTmp.value = []
}
</script>
<style scoped lang="scss">
.add-type {
  ::v-deep(.el-form-item__label) {
    font-size: 13px;
  }

  ::v-deep(.el-form-item__content) {
    width: 600px;
    font-size: 13px;

    .el-textarea__inner {
      font-family: auto;
      font-size: 13px;
    }
  }
}

.good_input {
  width: 87%;
}

.good_textarea {
  margin-top: 10px;
}

.tag-btn {
  color: var(--el-color-primary);
  border-color: var(--el-color-primary);
  font-size: 13px;

  &:hover {
    color: var(--el-color-danger);
    border-color: var(--el-color-danger);
    background-color: white;
    //文字下划线
    text-decoration: underline;
  }
}

.row_cls {
  display: flex;
  width: 100%;
}

.good_div {
  margin-top: 10px;
  color: #c0c4cc;
  min-height: 150px;
  width: 100%;
  border: 1px solid; /* 设置边框 */
  padding: 10px; /* 设置内边距 */
  background-color: #fff; /* 背景颜色 */
  display: flex; /* 使用弹性布局 */
  align-items: start;
  justify-content: start;
  gap: 10px;
  flex-wrap: wrap;
  overflow: hidden;
  align-content: flex-start;

  button + button {
    margin-left: 0;
  }
}
</style>
