<template>
    <!-- 列表 -->
    <ContentWrap class="full-height">
      <vxe-grid ref="gridRef" v-bind="gridOptions" v-on="gridEvents">
        <template #tools>
          <VxeButton
            @click="openForm('create')"
            v-hasPermi="['ccms:loadingStrategyConfig:save']"
            status="primary"
            icon="vxe-icon-add"
            >新增策略</VxeButton
          >
        <VxeButton
          style="margin-left: 20px"
          @click="handelSearch"
          status="info"
          icon="vxe-icon-search"
        />
      </template>
        <template #status="{ row }">
          <DictTag :type="DICT_TYPE.ENABLE_STATUS" :value="row.status" />
        </template>
        <template #operate="{ row }">
          <el-button
            type="primary"
            size="small"
            @click="updateStatus(row)"
            v-hasPermi="['ccms:loadingStrategyConfig:enable']"
            plain
            >{{ row.status == 'n' ? '启用' : '停用' }}</el-button
          >
          <el-button
            type="primary"
            size="small"
            @click="openForm('update', row)"
            v-hasPermi="['ccms:loadingStrategyConfig:update']"
            plain
            >编辑</el-button
          >
          <el-button
            type="danger"
            size="small"
            @click="handleDelete(row.id)"
            v-hasPermi="['ccms:loadingStrategyConfig:delete']"
            plain
            >删除</el-button
          >
        </template>
      </vxe-grid>
    </ContentWrap>
  
    <!-- 表单弹窗：添加/修改 -->
    <AddForm ref="formRef" @success="getList" />
  
    <!-- 搜索抽屉 -->
    <SearchForm ref="searchRef" v-model="queryParams" @search="getList" />
  </template>
  
  <script setup lang="ts">
  import {dateFormatterByOptions} from '@/utils/formatTime'
  import DictTag from '@/components/DictTag/src/DictTag.vue'
  // import download from '@/utils/download'
  import { DICT_TYPE } from '@/utils/dict'
  import AddForm from './AddForm.vue'
  import type { VxeGridProps, VxeGridListeners, VxeGridInstance } from 'vxe-table'
  import SearchForm from './SearchForm.vue'
  import { tms_web } from '@/api/ccms'
  import { ElMessage, ElMessageBox } from 'element-plus'
  defineOptions({ name: 'ContractMessage' })
  
  const message = useMessage() // 消息弹窗
  const { t } = useI18n() // 国际化
  
  const queryParams = reactive({
    name: undefined,
    pageNo: 1,
    pageSize: 10
  })
  
  const searchRef = ref()
  // 表格实例
  const gridRef = ref<VxeGridInstance>()
  
  // 调用vxe grid query方法重载表格
  const getList = (query1: any = {}) => {
    Object.assign(queryParams, { ...queryParams, ...query1 })
    // fetchApi()
    if (gridRef.value) {
      gridRef.value.commitProxy('query') // 触发表格重新加载数据
    }
  }
  // 获取表格数据
  // const fetchApi = () => {
  //   return new Promise(async (resolve) => {
  //     const res = await ContractMessageApi.getContractMessagePage(queryParams)
  //     gridOptions.data = res.list
  //     resolve({
  //       page: {
  //         total: res.total
  //       },
  //       result: res.list
  //     })
  //   })
  // }
  // 获取表格数据
  const fetchApi = () => {
    return new Promise(async (resolve) => {
      const res = await tms_web.loadingStrategyConfig_query_post({ data: { ...queryParams } })
      // console.log(res, '表格数据');
  
      // res.data?.list.forEach(item => {
      //   gridOptions.data?.push(item)
      // })
      // gridOptions.data = res.data.list
      resolve({
        page: {
          total: res.data?.total
        },
        result: res.data?.list
      })
    })
  }
  
  const gridOptions = reactive({
    stripe: true,
    border: true,
    keepSource: true,
    height: '100%',
    columnConfig: {
      resizable: true
    },
    pagerConfig: {
      enabled: true,
      pageSize: 10
    },
    editConfig: {
      trigger: 'click',
      mode: 'row',
      showStatus: true
    },
    toolbarConfig: {
      zoom: true,
      custom: true,
      slots: {
         tools: 'tools',
      },
    //   tools: [
    //     { name: '新增', icon:'vxe-icon-add', code: 'add', status: 'primary' },
    //     { name: '', code: 'search', icon: 'vxe-icon-search', status: 'info' },
    //     { name: '', code: 'customExport', icon: 'vxe-table-icon-download', status: 'info' }
    //   ]
    },
    proxyConfig: {
      props: {
        result: 'result',
        total: 'page.total'
      },
      ajax: {
        // 接收 Promise
        query: ({ page }) => {
          queryParams.pageNo = page.currentPage
          queryParams.pageSize = page.pageSize
          return fetchApi()
        }
        // body 对象： { removeRecords }
      }
    },
    columns: [
      { type: 'seq', width: 70 },
      { field: 'name', title: '策略名称', minWidth: '150px' },
      { field: 'remark', title: '策略说明', minWidth: '150px' },
      { field: 'createTime', title: '创建时间', minWidth: '100px',formatter:dateFormatterByOptions },
      { field: 'status', title: '策略状态', minWidth: '80px', slots: { default: 'status' } },
      { title: '操作', width: 200, slots: { default: 'operate' } }
      //   { field: 'contractPeriod', title: '备注', minWidth: '250px' },
      //   { field: 'address', title: '策略状态', minWidth: '120px' },
      // { field: 'status', title: '合同状态', minWidth: '100px' },
      // { field: 'originalContractNo', title: '原合同编号', minWidth: '200px' },
      // { field: 'signDate', title: '合同签订时间', minWidth: '200px',formatter:dateFormatterByOptions },
      // { field: 'effectiveDate', title: '合同生效时间', minWidth: '200px',formatter:dateFormatter2 },
      // { field: 'expirationDay', title: '合同失效时间', minWidth: '200px',formatter:dateFormatter2 },
      // { field: 'renewalTime', title: '合同续签时间', minWidth: '200px', formatter: dateFormatter2 },
    ],
    data: []
  })
  
  const gridEvents: VxeGridListeners = {
    toolbarButtonClick(params) {
      // console.log(params.code)
    },
  
    toolbarToolClick({ code }) {
      // console.log(123, code, tool)
      switch (code) {
        case 'search':
          handelSearch()
          break
        case 'add':
          openForm('create')
          break
        case 'del':
          if (gridRef.value) {
            gridRef.value.commitProxy('delete')
          }
          break
        case 'customExport':
          console.log('自定义导出')
          break
      }
    }
  }
  
  const handelSearch = () => {
    searchRef.value?.open()
  }
  
  /** 添加/修改操作 */
  const formRef = ref()
  const openForm = (type: string, row?: Object) => {
    formRef.value.open(type, row)
  }
  
  /** 删除按钮操作 */
  const handleDelete = async (id: number) => {
    try {
      // 删除的二次确认
      await message.delConfirm()
      // 发起删除
      await tms_web.loadingStrategyConfig_delete_post({
        data: {
          id: id
        }
      })
      // console.log(res, '删除数据');
  
      message.success(t('common.delSuccess'))
      // 刷新列表
      await getList()
    } catch (err) {
      console.log(err)
    }
  }
  
  // 停用/ 启用
  const expenseTypeEnable = (params: object) => {
    tms_web
      .loadingStrategyConfig_enable_post({
        data: {
          ...params
        }
      })
      .then(() => {
        getList()
      })
  }
  // 更新任务启用状态
  const updateStatus = (row: any) => {
    // console.log(row,'row');
    let params = {
      id: row.id,
      status: row.status === 'n' ? 'y' : 'n'
    }
    if (row.status === 'y') {
      ElMessageBox.confirm(
        `您确定要停用吗？停用后不能被后续功能选择，需重新启用后恢复可选择，请慎重考虑！`,
        '停用提醒！',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
        .then(() => {
          expenseTypeEnable(params)
          ElMessage({
            type: 'success',
            message: '停用完成'
          })
        })
        .catch(() => {
          ElMessage({
            type: 'info',
            message: '停用取消'
          })
        })
    } else {
      expenseTypeEnable(params)
    }
  }
  
  /** 导出按钮操作 */
  // const handleExport = async () => {
  //   try {
  //     // 导出的二次确认
  //     await message.exportConfirm()
  //     // 发起导出
  //     const data = await ContractMessageApi.exportContractMessage(queryParams)
  //     download.excel(data, '合同信息.xls')
  //   } catch {
  //   } finally {
  //   }
  // }
  </script>
  