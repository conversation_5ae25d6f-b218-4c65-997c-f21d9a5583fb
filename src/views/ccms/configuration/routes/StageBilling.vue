<template>
  <div class="stage-billing">
    <!-- 表单区域 -->
    <el-form
      ref="formdataref"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      style="width: 850px"
      v-loading="formLoading"
    >
      <div style="display: flex" v-for="(item, index) in items" :key="index">
        <div>
          <div class="addrss">
            <el-form-item prop="province" required label="途径点">
              <el-cascader
                style="width: 160px"
                size="large"
                v-model="item.province"
                :options="regionData"
                :props="{ value: 'label' }"
                @change="handleChange"
              />
            </el-form-item>
            <el-form-item label-width="10px" prop="address">
              <el-input
                style="margin-left: 10px; width: 440px"
                size="large"
                porp="address"
                placeholder="请输入详细地址"
                v-model="item.address"
              />
            </el-form-item>

            <div
              v-if="index === items.length - 1"
              @click="removeItem(index)"
              class="flex items-center gap-1 cursor-pointer text-nowrap"
              ><img class="size-[18px]" alt="-" src="@/assets/images/<EMAIL>" /><span
                >删除点位</span
              ></div
            >
          </div>
          <div class="lng_lat">
            <el-form-item prop="lng" required label="经度">
              <el-input
                style="width: 160px"
                size="large"
                type="number"
                v-model.number="item.lng"
              />
            </el-form-item>
            <el-form-item prop="lat" required label="纬度">
              <el-input
                style="width: 160px"
                size="large"
                type="number"
                v-model.number="item.lat"
              />
            </el-form-item>
            <div @click="openForm(item, index)" class="icon_con" style="display: flex">
              <img src="@/assets/images/<EMAIL>" alt="" />
              <span class="text_con">地图选点</span>
            </div>
          </div>
        </div>
      </div>
    </el-form>

    <div
      style="margin-left: 50px; width: 100px"
      @click="addItem"
      class="flex items-center gap-1 cursor-pointer text-nowrap"
      ><img class="size-[18px]" alt="+" src="@/assets/images/<EMAIL>" /><span
        >添加新点位</span
      ></div
    >

    <!-- <el-form label-width="auto" style="max-width: 600px">
      <el-form-item label="途径点">
      </el-form-item> -->
    <!-- <table>
      <thead>
        <tr>
          <th>点位名称</th>
          <th>经度</th>
          <th>纬度</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="(item, index) in items" :key="index">
          <td>
            <el-input label="你好" porp="pointName" v-model.number="item.pointName" />
          </td>
          <td>
            <el-input porp="lng" type="number" v-model.number="item.lng" />
          </td>
          <td>
            <el-input porp="lat" type="number" v-model.number="item.lat" />
          </td>
          <td class="flex items-center gap-2 ml-[5px] select-none">
            <div
              v-if="index === 0"
              @click="addItem"
              class="flex items-center gap-1 cursor-pointer text-nowrap"
              ><img class="size-[18px]" alt="+" src="@/assets/images/<EMAIL>" /><span
                >添加</span
              ></div
            >
            <div
              v-else
              @click="removeItem(index)"
              class="flex items-center gap-1 cursor-pointer text-nowrap"
              ><img class="size-[18px]" alt="-" src="@/assets/images/<EMAIL>" /><span
                >删除</span
              ></div
            >
          </td>
        </tr>
      </tbody>
    </table> -->
  </div>
  <AddMap ref="formRef" @success="successFn" />
</template>

<script setup>
import { ref, watch } from 'vue'
import InfoField from '@/components/infoBlock/InfoField.vue'
import AddMap from './addmap.vue'
import { regionData } from '@/components/selectProvincialAndCities/areaData'

import { useVModel } from '@vueuse/core'
// import { computed } from 'vue'
const formRef = ref()
const formdataref = ref()
const openForm = (item, index) => {
  formRef.value.open(item, index)
}
// 省市区选择器变化函数
const handleChange = (e) => {
}
const formRules = reactive({
  province: [{ required: true, message: '请选择省市区', trigger: 'blur' }],
  lng: [{ required: true, message: '请输入', trigger: 'blur' }],
  lat: [{ required: true, message: '请输入', trigger: 'blur' }]
})
AMap.plugin('AMap.DistrictSearch', function () {
  var districtSearch = new AMap.DistrictSearch({
    // 关键字对应的行政区级别，country表示国家
    level: 'country',
    //  显示下级行政区级数，1表示返回下一级行政区
    subdistrict: 2
  })


})
const successFn = (row) => {
  // console.log(row, 'laglat')
  items[row.index].lng = row.lnglat.lng
  items[row.index].lat = row.lnglat.lat
}
// name: 'StageBilling',
const props = defineProps({
  detailList: {
    type: Array,
    required: true
  }
})

// props: {
//   detailList: {
//     type: Array,
//     required: true
//   }
// },
const selectedProvince = '111'
const selectedCity = ''
const cascaderProps = {
  expandTrigger: 'const'
}
const selectedRegion = ref([])
// 模拟城市数据
const abc = ref([])
const items = reactive([
  {
    province: '',
    city: '',
    county:'',
    address: '',
    lng: '',
    lat: ''
  },
  {
    province: '',
    city: '',
    county:'',
    address: '',
    lng: '',
    lat: ''
  }
])

const addItem = () => {
  items.push({
    province: '',
    city: '',
    address: '',
    lng: '',
    lat: ''
  })
}

const removeItem = (index) => {
  if (items.length > 1) {
    items.splice(index, 1)
  }
}

// 验证表单有效性
// watch(
//   items,
//   (newValue) => {
//     let isValid = false

//     for (let i = 0; i < newValue.length; i++) {
//       if (newValue[i].pointName && newValue[i].lng && newValue[i].lat) {
//         isValid = true
//         break
//       }
//     }

//     if (!isValid) {
//       alert('点位参数不能为空')
//     }
//   },
//   { deep: false }
// )
const setItems = (data) => {
  items = data
}
defineExpose({
  items,
  addItem,
  removeItem
})
// return {
//   items,
//   addItem,
//   removeItem
// }
</script>
<style scoped lang="scss">
.addrss {
  display: flex;
}
.lng_lat {
  display: flex;
  align-items: center;
}

.stage-billing {
  display: flex;
  flex-direction: column;
}

.stage-billing table {
  border-collapse: collapse;
  text-align: center;
}

.stage-billing th {
  font-weight: normal;
}

.stage-billing td {
  padding-right: 5px;
}

.stage-billing button {
  background-color: white;
  color: black;
  font-size: 13px;
  border-color: #dcdfe6;
  cursor: pointer;
  border-radius: 5px;
  margin-top: 5px;
}

.stage-billing button:hover {
  background-color: deepskyblue;
}

.stage-billing select {
  height: 40px;
  padding: 1px;
  border-color: #dcdfe6;
  margin-bottom: 5px;
}

.stage-billing input[type='number'] {
  height: 40px;
  padding: 1px;
  border-color: #dcdfe6;
  margin-bottom: 5px;
}
.icon_con {
  display: flex;
  align-items: center;
  margin-left: 20px;
  height: 100%;
  cursor: pointer;
  img {
    width: 20px;
    height: 20px;
  }
}
.text_con {
  font-family:
    Source Han Sans SC,
    Source Han Sans SC;
  font-weight: 400;
  font-size: 14px;
  color: #2d57cc;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
</style>
