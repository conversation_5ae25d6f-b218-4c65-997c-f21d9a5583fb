<template>
  <div class="flex flex-col gap-5">
    <div class="flex items-center gap-[10px] rounded-[10px] bg-white p-5 leading-none">
      <div class="text-[18px] font-bold text-black">
        {{ isView ? '新建配置' : '编辑配置' }}
      </div>
      <div class="flex cursor-pointer items-center gap-[6px] text-[13px] text-[#A8ABB2]">
        <Icon icon="tdesign:rollback" />
        <span @click="router.back()">返回</span>
      </div>
    </div>
    <div class="flex flex-col gap-[30px] rounded-[10px] bg-white p-10">
      <InfoBlock name="路线信息">
        <el-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          label-width="100px"
          style="width: 450px"
          v-loading="formLoading"
        >
          <el-form-item label="路线名称" required prop="routeName">
            <el-input size="large" v-model="formData.routeName" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <!-- <el-input v-model="formData.remark" placeholder="请输入厂商名称" /> -->
            <el-input
              size="large"
              v-model="formData.remark"
              :rows="3"
              type="textarea"
              placeholder="请输入"
            />
          </el-form-item>
        </el-form>
      </InfoBlock>
      <InfoBlock name="点位信息">
        <StageBilling ref="stageBillingRef" :detailList="detailList" />
      </InfoBlock>
      <div class="flex justify-center">
        <el-button type="primary" @click="submitForm" v-hasPermi="['ccms:routes:saveCarRoute']"
          >保存</el-button
        >
        <el-button @click="router.back()">返回</el-button>
      </div>
      <!-- <el-form-item label="地址" required prop="includeGoods">
        <el-input v-model="formData.includeGoods" placeholder="请输入地址" />
      </el-form-item> -->
    </div>
  </div>
</template>
<script setup lang="ts">
import { tms_web } from '@/api/ccms'
import StageBilling from './StageBilling.vue'
import { ref, onMounted } from 'vue'

const route = useRoute()
const router = useRouter()
onMounted(() => {
  // console.log(route.query)
  if (route.query.id) {
    tms_web.routes_getRoute_post({ data: { id: route.query.id } }).then((res) => {
      if (res.data) {
        formData.routeName = res.data.routeName
        formData.remark = res.data.remark
        res.data.list?.forEach((item, index) => {
          stageBillingRef.value.items[index].province = [item.province, item.city,item.county]
          stageBillingRef.value.items[index].city = item.city
          stageBillingRef.value.items[index].city = item.county
          stageBillingRef.value.items[index].lng = item.lng
          stageBillingRef.value.items[index].lat = item.lat
          stageBillingRef.value.items[index].address = item.address
        })
      }
      // console.log(stageBillingRef.value.items)
    })
  }
})

const isView = computed(() => {
  return route.query.type === 'create'
})

const isDeal = computed(() => {
  return route.query.type === 'update'
})

/** 合同信息 表单 */
defineOptions({ name: 'ContractMessageForm' })
const detailList = ref([])
const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref(route.query.type) // 表单的类型：create - 新增；update - 修改
const updateId = ref()
const formData = reactive({
  routeName: '',
  remark: ''
})
const formRules = reactive({
  routeName: [{ required: true, message: '请输入路线名称', trigger: 'blur' }],
  includeGoods: [{ required: true, message: '请输入兼容商品并点击添加', trigger: 'blur' }],
  excludeGoods: [{ required: true, message: '请输入排斥商品并点击添加', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref
interface Object {
  id: any
  routeName: any
  remark: any
}
const stageBillingRef = ref()

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  if (formLoading.value) {
    return
  }
  // console.log(stageBillingRef.value.rulesFn())
  // 校验表单
  await formRef.value.validate()
  if (stageBillingRef.value.items.some((item) => item.province === '')) {
    return message.error('请选择省市区')
  } else if (stageBillingRef.value.items.some((item) => item.lng === '' || item.lat === '')) {
    return message.error('请补全经纬度')
  }
  stageBillingRef.value.items.forEach((item) => {
    item.city = item.province[1]
    item.county = item.province[2] || ''
    item.province = item.province[0]
  })
  // console.log(stageBillingRef.value.items, 'stageBillingRef.value.items')

  // 提交请求
  formLoading.value = true
  try {
    // const data = formData.value

    let data: any = {
      name: formData.routeName,
      remark: formData.remark
    }
    if (formType.value === 'create') {
      // console.log(data, 'formType');
      await tms_web.routes_saveCarRoute_post({
        data: {
          list: stageBillingRef.value.items,
          ...formData
        }
      })
      // console.log(res, '新增成功');
      router.back()
      message.success('新增成功')
    } else {
      await tms_web.routes_editCarRoute_post({
        data: {
          id: route.query.id,
          list: stageBillingRef.value.items,
          ...formData
        }
      })
      // console.log(res, '修改成功');
      router.back()
      message.success(t('common.updateSuccess'))
    }
    // 发送操作成功的事件
    // emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formRef.value?.resetFields()
  Object.assign(formData, {
    name: undefined,
    remark: undefined
  })
}
</script>
<style scoped lang="scss">
.routes_content {
  display: flex;
  justify-content: space-around;
  align-items: center;
  .con_right {
    width: 600px;
    height: 100%;
    .gd_map_box {
      // background-color: pink;
      width: 600px;
      height: 350px;
    }
  }
}
.add-type {
  ::v-deep(.el-form-item__label) {
    font-size: 13px;
  }

  ::v-deep(.el-form-item__content) {
    width: 700px;
    font-size: 13px;
    margin-left: 0;
    .el-textarea__inner {
      font-family: auto;
      font-size: 13px;
    }
  }
}

.good_input {
  width: 87%;
}

.good_textarea {
  margin-top: 10px;
}

.tag-btn {
  color: var(--el-color-primary);
  border-color: var(--el-color-primary);
  font-size: 13px;

  &:hover {
    color: var(--el-color-danger);
    border-color: var(--el-color-danger);
    background-color: white;
    //文字下划线
    text-decoration: underline;
  }
}

.row_cls {
  display: flex;
  width: 100%;
}

.good_div {
  margin-top: 10px;
  color: #c0c4cc;
  min-height: 150px;
  width: 100%;
  border: 1px solid; /* 设置边框 */
  padding: 10px; /* 设置内边距 */
  background-color: #fff; /* 背景颜色 */
  display: flex; /* 使用弹性布局 */
  align-items: start;
  justify-content: start;
  gap: 10px;
  flex-wrap: wrap;
  overflow: hidden;
  align-content: flex-start;

  button + button {
    margin-left: 0;
  }
}
</style>
