<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <div class="con_right">
      <div ref="container" id="container" class="gd_map_box">
        <el-input
          v-model="input3"
          style="max-width: 300px; position: absolute; z-index: 999; top: 20px; left: 20px"
          placeholder="请输入地址"
          class="input-with-select"
        >
          <template #append>
            <el-button
              style="background-color: #2d57cc; color: #fff"
              type="primary"
              @click="searchFn"
              >搜索</el-button
            >
          </template>
        </el-input>
      </div>
    </div>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { tms_web } from '@/api/ccms'
import { Search } from '@element-plus/icons-vue'
/** 合同信息 表单 */
defineOptions({ name: 'AddShipments' })
const input3 = ref('')
const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const searchFn = () => {
  AMap.plugin('AMap.Geocoder', function () {
    var geocoder = new AMap.Geocoder({
      // city 指定进行编码查询的城市，支持传入城市名、adcode 和 citycode
      city: input3.value
    })
    geocoder.getLocation(input3.value, function (status, result) {
      if (status === 'complete' && result.info === 'OK') {
        // result中对应详细地理坐标信息
        var position = new AMap.LngLat(
          result.geocodes[0]?.location.lng,
          result.geocodes[0]?.location.lat
        ) // 标准写法
        // 简写 var position = [116, 39];
        map.setCenter(position)
      }
    })
  })
}

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('地图选点') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
// const formType = ref('') // 表单的类型：create - 新增；update - 修改
const container = ref(null)
let map: any

onMounted(() => {
  //   map = new AMap.Map(container.value)
})

/** 打开弹窗 */
const lnglat = reactive({
  lng: '',
  lat: ''
})
// 地图双击函数
const mapDblclickFn = (ev) => {
  if (lnglat.lng) {
    map.clearMap()
  }
  var marker = new AMap.Marker({
    position: new AMap.LngLat(ev.lnglat.lng, ev.lnglat.lat) // 经纬度对象，也可以是经纬度构成的一维数组[116.39, 39.9]
  })
  ;(lnglat.lng = ev.lnglat.lng), (lnglat.lat = ev.lnglat.lat)
  // 将创建的点标记添加到已有的地图实例：
  map.add(marker)
  AMap.plugin('AMap.Geocoder', function () {
    var geocoder = new AMap.Geocoder({
      // city 指定进行编码查询的城市，支持传入城市名、adcode 和 citycode
      city: '010'
    })

    var lnglat = [ev.lnglat.lng, ev.lnglat.lat]

    geocoder.getAddress(lnglat, function (status, result) {
      if (status === 'complete' && result.info === 'OK') {
        // result为对应的地理位置详细信息
        console.log(result, 'result')
      }
    })
  })
}
const lnglatIndex = ref()
const open = async (item, index?: any) => {
  lnglatIndex.value = index
  dialogVisible.value = true
  setTimeout(() => {
    // console.log(container.value, 'container.value')
    map = new AMap.Map(container.value)
    // 双击地图事件
    map.on('dblclick', mapDblclickFn)

    // 根据输入的地址设置中心点并标点
    AMap.plugin('AMap.Geocoder', function () {
      var geocoder = new AMap.Geocoder({
        // city 指定进行编码查询的城市，支持传入城市名、adcode 和 citycode
        city: item.province[0] || '010'
      })
      const sumArray = (arr) => arr.reduce((a, b) => a + b, 0)
      geocoder.getLocation(sumArray(item.province) + item.address, function (status, result) {
        if (status === 'complete' && result.info === 'OK') {
          // result中对应详细地理坐标信息
          var position = new AMap.LngLat(
            result.geocodes[0]?.location.lng,
            result.geocodes[0]?.location.lat
          ) // 标准写法
          // 简写 var position = [116, 39];
          map.setCenter(position)
          var marker = new AMap.Marker({
            position: position // 经纬度对象，也可以是经纬度构成的一维数组[116.39, 39.9]
          })
          ;(lnglat.lng = result.geocodes[0]?.location.lng),
            (lnglat.lat = result.geocodes[0]?.location.lat)
          // 将创建的点标记添加到已有的地图实例：
          map.add(marker)
        }
      })
    })
  }, 100)

  // dialogTitle.value = t('action.' + type)
  //   map = new AMap.Map(container.value)
  //   console.log(container.value, 'container.value')
  //   map = new AMap.Map(container.value, {
  //     zoom: 10, //地图级别
  //     center: [116.397428, 39.90923], //地图中心点
  //     layers: [new AMap.TileLayer.Satellite()], //设置图层,可设置成包含一个或多个图层的数组
  //     mapStyle: 'amap://styles/whitesmoke', //设置地图的显示样式
  //     viewMode: '2D' //设置地图模式
  //   })
  // formType.value = type
  resetForm()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗
/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 发送操作成功的事件
  dialogVisible.value = false
  emit('success', { index: lnglatIndex.value, lnglat })
}

/** 重置表单 */
const resetForm = () => {}
</script>
<style scoped lang="scss">
.con_right {
  width: 100%;
  height: 100%;
  .gd_map_box {
    // background-color: pink;
    width: 600px;
    height: 350px;
  }
}
</style>
