<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref, watch } from 'vue'
import { tms_web } from '@/api/ccms'
import echarts from '@/plugins/echarts'
import dayjs from 'dayjs'
import { cloneDeep, debounce } from 'lodash'
import { useOrderCarMap } from '@/hooks/useOrderCarMap'
import { getDownloadPath } from '@/utils/apiUtil'
import { setTenantId, setToken } from '@/utils/auth'

const orderId = ref('')

const mapContainerRef = ref<HTMLDivElement>()
const { currentMapPoint, setStartEndPointByAddress, setMapPoints } = useOrderCarMap(mapContainerRef)

//是否初始化过视野,用于判断更新数据时视野不动
let isMapViewportInit = false

const setViewportIng = ref(false)

function queryMapData() {
  tms_web
    .assetsRecord_queryLatLngRecord_post({
      data: {
        orderId: orderId.value as any
      }
    })
    .then((data) => {
      if (!isMapViewportInit) {
        setViewportIng.value = true
      }

      setMapPoints(data.data || [], isMapViewportInit)

      if (!isMapViewportInit) {
        isMapViewportInit = true
        setTimeout(() => {
          setViewportIng.value = false
        }, 3000)
      }
    })
}

const tempHumRecords = ref<any>([])

const currentTempHum = computed(() => {
  return tempHumRecords.value[tempHumRecords.value.length - 1] || {}
})

const currentLocation = ref('')

const geoc = new BMapGL.Geocoder()
let getCurrentLocation = function () {
  if (!currentMapPoint.value || !currentMapPoint.value.lngValue) {
    currentLocation.value = '未知位置'
    return
  }
  const pt = new BMapGL.Point(currentMapPoint.value.lngValue, currentMapPoint.value.latValue)
  geoc.getLocation(pt, function (rs: any) {
    currentLocation.value = rs.address || '未知位置'
  })
}
getCurrentLocation = debounce(getCurrentLocation, 1000)

watch(() => currentMapPoint.value.uid, getCurrentLocation)

function queryTempHumRecords() {
  tms_web
    .assetsRecord_queryTempHumRecords_post({
      data: {
        orderId: orderId.value
      }
    })
    .then((data) => {
      tempHumRecords.value = data.data || []
      updateChart()
    })
}

const countData = ref<any>({})

function queryCount() {
  tms_web
    .assetsRecord_count_post({
      data: {
        orderId: orderId.value
      }
    })
    .then((data) => {
      countData.value = data.data || {}
    })
}

const orderInfo = ref<any>({})

function getOrderInfo() {
  tms_web
    .transportOrder_get_post({
      data: {
        id: orderId.value
      }
    })
    .then((data) => {
      console.log(data)
      orderInfo.value = data.data || {}
      //获取起点终点的坐标位置
      const start = `${orderInfo.value.sendProvince || ''}${orderInfo.value.sendCity || ''}${orderInfo.value.sendCounty || ''}${orderInfo.value.sendAddress}`
      const endList: string[] = []
      for (const item of orderInfo.value.addressList || []) {
        endList.push(`${item.province}${item.city}${item.county}${item.address}`)
      }
      setStartEndPointByAddress(start, endList)
    })
}

//实时定位(订单状态是配送中时显示)
const isRealPositioning = ref(false)

let timer: any = null

watch(isRealPositioning, () => {
  clearInterval(timer)
  if (isRealPositioning.value) {
    function loop() {
      queryMapData()
      queryTempHumRecords()
    }

    timer = setInterval(loop, 5000)
    loop()
  }
})

const chartType = ref('温度')
watch(chartType, () => {
  updateChart()
})

const chartRef = ref<HTMLDivElement>()

let chartInstance: any

function updateChart() {
  //处理数据,按照24小时保存数据
  const tempHumData: any[] = []
  const xAxisData: string[] = []
  for (const item of tempHumRecords.value) {
    xAxisData.push(dayjs(item.collectTime as string).format('HH:mm'))
    tempHumData.push(chartType.value === '温度' ? item.tempValue : item.humValue)
  }

  const color = chartType.value === '温度' ? '#FF6600' : '#1678FF'
  chartInstance.setOption({
    color: [color],
    grid: {
      top: 50,
      bottom: 20
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: xAxisData,
      axisTick: {
        show: false
      },
      axisLine: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLine: {
        show: false
      }
    },
    series: [
      {
        name: chartType.value,
        type: 'line',
        symbol: 'none',
        connectNulls: true,
        smooth: true,
        data: tempHumData,
        markPoint: {
          symbolSize: 50,
          itemStyle: {
            color: color
          },
          data: [
            { type: 'max', name: 'Max' },
            { type: 'min', name: 'Min' }
          ]
        },
        lineStyle: {
          color: color,
          width: 3
        }
        //设定最高点和最低点
        /*markLine: {
          symbol: "none",
          symbolSize: 0,
          lineStyle: {
            color: "#E04F48",
          },
          data: [
            {
              x: 30,
              yAxis: 15,
            },
            {
              x: 30,
              yAxis: 5,
            },
          ],
        },*/
      }
    ]
  })
}

const VITE_SYS_TYPE = import.meta.env.VITE_SYS_TYPE

const stepList = [
  {
    stepName: '已下单',
    stepStatus: 'y',
    labelTime: '订单时间',
    time: '',
    labelOther: '订单编号',
    other: ''
  },
  {
    stepName: '已接单',
    stepStatus: 'y',
    labelTime: '接单时间',
    time: '',
    labelOther: '车牌号码',
    other: ''
  },
  {
    stepName: '已装货',
    stepStatus: 'n',
    labelTime: '装货时间',
    time: '',
    labelOther: '货物检查',
    isView: true
  },
  {
    stepName: '配送中',
    stepStatus: 'n',
    labelTime: '配送时间',
    time: '',
    labelOther: '货物检查',
    isView: true
  },
  {
    stepName: '已卸货',
    stepStatus: 'n',
    labelTime: '卸货时间',
    time: '',
    labelOther: '货物检查',
    isView: true
  },
  {
    stepName: '确认卸货',
    stepStatus: 'n',
    labelTime: '确认时间',
    time: ''
  },
  {
    stepName: '已支付',
    stepStatus: 'n',
    labelTime: '支付时间',
    time: '2024-11-18 14:18:18'
  },
  {
    stepName: '已完成',
    stepStatus: 'n',
    labelTime: '完成时间',
    time: '2024-11-18 14:18:18'
  }
]

const checkList = ref<any[]>([])

function queryCheckList() {
  tms_web
    .transportOrder_queryCheck_post({
      data: {
        orderId: orderId.value
      }
    })
    .then((data) => {
      checkList.value = (data.data || []).map((item) => {
        return {
          time: item.createTime || '-',
          content: item.remark || '-',
          imgList: (item.fileList || []).map((imgItem: any) => {
            return getDownloadPath(imgItem.address)
          })
        }
      })
    })
}

const resStepList = computed(() => {
  const tempList: any[] = cloneDeep(stepList)
  tempList[0].time = orderInfo.value.orderTime
  tempList[0].other = orderInfo.value.orderNumber
  tempList[1].time = orderInfo.value.transportTime
  tempList[1].other = orderInfo.value.transportNumber
  if (orderInfo.value.driverLoadStatus && orderInfo.value.driverLoadStatus.name === 'y') {
    tempList[2].time = orderInfo.value.driverLoadTime
    tempList[2].stepStatus = 'y'
  }

  if (orderInfo.value.customerLoadStatus && orderInfo.value.customerLoadStatus.name === 'y') {
    tempList[3].time = orderInfo.value.customerLoadTime
    tempList[3].stepStatus = 'y'
  }

  if (orderInfo.value.driverUnloadStatus && orderInfo.value.driverUnloadStatus.name === 'y') {
    tempList[4].time = orderInfo.value.driverUnloadTime
    tempList[4].stepStatus = 'y'
  }

  if (orderInfo.value.customerUnloadStatus && orderInfo.value.customerUnloadStatus.name === 'y') {
    tempList[5].time = orderInfo.value.customerUnloadTime
    tempList[5].stepStatus = 'y'
  }

  if (orderInfo.value.payStatus && orderInfo.value.payStatus.name === 'yes') {
    tempList[6].time = orderInfo.value.payTime
    tempList[6].stepStatus = 'y'
  }

  if (orderInfo.value.transportStatus && orderInfo.value.transportStatus.name === 'finish') {
    tempList[7].time = orderInfo.value.finishTime
    tempList[7].stepStatus = 'y'
  }

  for (const item of tempList) {
    let imgList: string[] = []

    if (item.stepName === '已装货') {
      if (orderInfo.value.fileList) {
        imgList = orderInfo.value.fileList
          .filter((item: any) => item.type && item.type.name === 'load_picture')
          .map((item: any) => {
            return getDownloadPath(item.address || '')
          })
      }
      item.dataList = [
        {
          time: orderInfo.value.driverLoadTime || '-',
          content: orderInfo.value.loadRemark || '-',
          imgList: imgList
        }
      ]
    } else if (item.stepName === '已卸货') {
      if (orderInfo.value.fileList) {
        imgList = orderInfo.value.fileList
          .filter((item: any) => item.type && item.type.name === 'unload_picture')
          .map((item: any) => {
            return getDownloadPath(item.address || '')
          })
      }
      item.dataList = [
        {
          time: orderInfo.value.driverUnloadTime || '-',
          content: orderInfo.value.unloadRemark || '-',
          imgList: imgList
        }
      ]
    } else if (item.stepName === '配送中') {
      item.dataList = checkList.value
    }
  }

  return tempList
})

onMounted(() => {
  const url = new URL(location.href)
  orderId.value = url.searchParams.get('orderId') || ''
  const tenantId = url.searchParams.get('tenantId') || '',
    accessToken = url.searchParams.get('accessToken') || '',
    refreshToken = url.searchParams.get('refreshToken') || ''

  if (tenantId) {
    setTenantId(tenantId)
  }
  if (accessToken && refreshToken) {
    setToken({
      accessToken: accessToken,
      refreshToken: refreshToken
    } as any)
  }

  getOrderInfo()
  queryCheckList()
  queryMapData()
  queryTempHumRecords()
  queryCount()
  if (chartRef.value) {
    chartInstance = echarts.init(chartRef.value)
  }
})

onUnmounted(() => {
  clearInterval(timer)
})
</script>

<template>
  <div class="map-container">
    <div
      class="map-block size-full"
      :class="setViewportIng ? 'viewport-ing' : ''"
      ref="mapContainerRef"
    ></div>
    <div class="bottom-block absolute z-10 flex flex-col bg-white">
      <div class="flex items-center justify-between">
        <div class="location-text flex items-center">
          <img alt="图标" src="@/assets/images/<EMAIL>" />
          <div>{{ currentLocation || '-' }}</div>
        </div>
        <el-switch
          v-model="isRealPositioning"
          active-text="实时"
          inactive-text="实时"
          active-color="#36B336"
        />
      </div>
      <div class="split-line"></div>
      <div class="custom-timeline" v-if="VITE_SYS_TYPE === 'cd'">
        <el-timeline class="w-full">
          <el-timeline-item
            v-for="stepInfo in resStepList"
            :color="stepInfo.stepStatus === 'y' ? '#1678FF' : '#999999'"
            :key="stepInfo.stepName"
          >
            <div class="custom-timeline-item">{{ stepInfo.stepName }}</div>
            <div class="custom-timeline-item-field text-[#999999]">
              <div>{{ stepInfo.labelTime }}</div>
              <div class="text-[#333333]">{{ stepInfo.time }}</div>
            </div>
            <div class="data-list" v-if="stepInfo.isView">
              <div v-for="(item, index) in stepInfo.dataList" :key="index">
                <div class="text-[#333333]">
                  {{ item.content }}
                </div>
                <div class="grid w-full grid-cols-5">
                  <el-image
                    v-for="(imgInfo, imgIndex) in item.imgList"
                    :key="imgIndex"
                    class="custom-img"
                    :src="imgInfo"
                    fit="cover"
                    :preview-src-list="[imgInfo]"
                    :initial-index="0"
                  />
                </div>
              </div>
            </div>
            <div v-else class="custom-timeline-item-field text-[#999999]">
              <div>{{ stepInfo.labelOther }}</div>
              <div class="text-[#333333]">{{ stepInfo.other }}</div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>
      <template v-else>
        <div class="count-field">
          <div class="temperature">
            <div>
              <label>当前温度</label>
              <span>{{ currentTempHum.tempValue || '-' }}℃</span>
            </div>
            <div class="split-line-y"></div>
            <div>
              <label>最低</label>
              <span>{{ countData.minTempValue || '-' }}℃</span>
            </div>
            <div class="split-line-y"></div>
            <div>
              <label>最高</label>
              <span>{{ countData.maxTempValue || '-' }}℃</span>
            </div>
          </div>
          <div>
            <div>
              <label>当前湿度</label>
              <span>{{ currentTempHum.humValue || '-' }}%</span>
            </div>
            <div class="split-line-y"></div>
            <div>
              <label>最低</label>
              <span>{{ countData.minHumValue || '-' }}%</span>
            </div>
            <div class="split-line-y"></div>
            <div>
              <label>最高</label>
              <span>{{ countData.maxHumValue || '-' }}%</span>
            </div>
          </div>
        </div>
        <div class="split-line"></div>
        <div class="chart-content">
          <div class="chart-title flex items-center justify-between">
            <div class="title flex items-center">
              <div class="rect"></div>
              <div>感应器变化趋势</div>
            </div>
            <el-radio-group v-model="chartType">
              <el-radio class="temperature" label="温度" />
              <el-radio label="湿度" />
            </el-radio-group>
          </div>
          <div class="chart-wrapper" ref="chartRef"></div>
        </div>
      </template>
    </div>
  </div>
</template>

<style scoped lang="scss">
.custom-timeline {
  max-height: 600px;
  padding: 0 4px;
  overflow-y: auto;

  .custom-timeline-item {
    font-size: 32px;
    color: #333333;
    font-weight: bold;
    margin-bottom: 10px;
  }

  .custom-timeline-item-field {
    font-size: 26px;
    display: flex;
    gap: 20px;
  }

  .data-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-top: 20px;

    > div {
      display: flex;
      flex-direction: column;
      gap: 10px;

      > div {
        &:last-child {
          gap: 5px;
        }
      }

      .custom-img {
        border-radius: 10px;
        height: 100px;
      }
    }
  }
}

.map-block {
  &.viewport-ing {
    height: calc(100vh - 670px) !important;
  }
}

.map-container {
  width: 100vw;
  height: 100vh;
  position: relative;

  ::v-deep .el-switch__label {
    position: absolute;
    display: none;
    color: #fff;
  }

  /*打开时文字位置设置*/
  ::v-deep .el-switch__label--right {
    z-index: 1;
    left: 0px;
  }

  /*关闭时文字位置设置*/
  ::v-deep .el-switch__label--left {
    z-index: 1;
    left: 40px;
  }

  /*显示文字*/
  ::v-deep .el-switch__label.is-active {
    display: block;
  }

  ::v-deep .el-switch .el-switch__core,
  ::v-deep .el-switch .el-switch__label {
    width: 114px !important;
    font-size: 24px;
  }
}

.count-field {
  > div {
    display: flex;
    align-items: center;

    &.temperature {
      > div > span {
        color: #ff6600;
      }
    }

    > div {
      display: flex;
      align-items: center;
      font-size: 24px;
      width: calc((100% - 84px) / 3);

      > label {
        color: #333333;
        text-wrap: nowrap;
      }

      > span {
        margin-left: 8px;
        color: #1678ff;
        font-weight: bold;
      }
    }
  }
}

.split-line {
  height: 2px;
  background: #ebebeb;
}

.split-line-y {
  height: 18px;
  width: 2px !important;
  background: #ebebeb;
  margin: 0 20px;
}

.chart-title {
  > .title {
    font-weight: bold;
    gap: 8px;

    .rect {
      width: 8px;
      height: 24px;
      background: #2d57cc;
      border-radius: 198px;
    }
  }

  ::v-deep .el-radio {
    margin-right: 42px;
    font-size: 26px;

    &:last-child {
      margin-right: 0;
    }
  }

  ::v-deep .el-radio__input.is-checked .el-radio__inner {
    background: #1678ff;
    border-color: #1678ff;
  }

  ::v-deep .el-radio__input.is-checked + .el-radio__label {
    color: #1678ff;
  }

  .temperature {
    ::v-deep .el-radio__input.is-checked .el-radio__inner {
      background: #ff6600;
      border-color: #ff6600;
    }

    ::v-deep .el-radio__input.is-checked + .el-radio__label {
      color: #ff6600;
    }
  }
}

.chart-wrapper {
  height: 290px;
}

.bottom-block {
  padding: 30px;
  margin: 0 20px;
  bottom: 20px;
  border-radius: 16px;
  width: calc(100% - 40px);
  gap: 30px;

  .location-text {
    gap: 20px;

    > img {
      width: 36px;
      height: 36px;
    }

    > div {
      font-size: 26px;
      color: #333333;
    }
  }
}
</style>
