import _, { toNumber, isObject } from 'lodash-es'

/**
 * 移除通用字段(created,createdBy,modified,modifiedBy,createdName,modifiedName)
 * 防止修改时出现的意外错误
 * @param data
 */
export function removeCommonField<T>(data: T) {
  if (data) {
    delete (data as any).createTime
    delete (data as any).creator
    delete (data as any).updateTime
    delete (data as any).updater
    delete (data as any).deleted
    delete (data as any)._X_ROW_KEY
    for (const key in data) {
      if (Array.isArray(data[key])) {
        for (const [index, item] of (data[key] as any[]).entries()) {
          data[key][index] = removeCommonField(item)
        }
      }
    }
  }
  return data
}

//逗号转顿号
export function commaToDot(str: string) {
  return str.replace(/,/g, '、')
}

const excludeKeys = ['$$hashKey']

export function traverseArray(obj: any, isObj?: boolean, attr?: string, res?: any) {
  //数据初始化
  res = res ? res : {}
  attr = attr ? attr : ''
  //执行数据转换
  if (
    obj &&
    isObject(obj) &&
    obj.toString().indexOf('[object File]') === -1 &&
    obj.toString().indexOf('[object Blob]') === -1
  ) {
    let attrRes
    if (Array.isArray(obj)) {
      //是数组转换成[?]的形式
      for (const [index, item] of (obj as []).entries()) {
        attrRes = attr //备份属性名称
        attr += '[' + index + '].'
        res = traverseArray(item, isObj, attr, res)
        attr = attrRes //还原属性名称
      }
    } else {
      for (const index of Object.keys(obj)) {
        //@ts-ignore
        const item = obj[index]
        attrRes = attr
        if (
          isObject(item) &&
          item.toString().indexOf('[object File]') === -1 &&
          item.toString().indexOf('[object Blob]') === -1 &&
          !Array.isArray(item) &&
          isObj
        ) {
          //如果item是一个对象且不是一个数组就增加点,否则增加[?]
          attr += index + '.'
        } else {
          attr += index
        }
        res = traverseArray(item, isObj, attr, res)
        attr = attrRes
      }
    }
  } else {
    if (!excludeKeys.includes(attr)) {
      res[attr] = obj
    }
  }
  return res
}

type options<T> = {
  //树结构的子节点名称
  childList?: string
  beforeFun?: (list: T[], parent?: T) => void
  filterFun?: (item: T, index?: number, list?: T[], parent?: T) => boolean | void
  afterFun?: (list: T[], parent?: T) => void
}

/**
 * 操作树结构数据
 * @param list
 * @param options
 * @param parent
 */
export function traversingTree<T>(list: T[], options?: options<T>, parent?: T): void {
  if (!list) {
    return
  }
  const childList = options && options.childList ? options.childList : 'childList'
  if (!options) {
    options = {}
  }
  if (options.beforeFun) {
    options.beforeFun(list, parent)
  }
  for (const [index, item] of list.entries()) {
    let flag: boolean | void = true
    if (options.filterFun) {
      flag = options.filterFun(item, index, list, parent)
    }
    //@ts-ignore
    if (item[childList] && flag !== false) {
      //@ts-ignore
      traversingTree(item[childList], options, item)
    }
  }
  if (options.afterFun) {
    options.afterFun(list, parent)
  }
}

export function deleteNodesNotInList(
  treeData: any[],
  idList: string[],
  options?: { key?: string; childKey?: string }
): any[] {
  options = options || {}
  const childKey = options.childKey || 'childList'
  return treeData.filter((node) => {
    if (idList.includes(node[options?.key || 'id'])) {
      if (node[childKey] && node[childKey].length > 0) {
        node[childKey] = deleteNodesNotInList(node[childKey], idList, options)
      }
      return true
    }
    return false
  })
}

/**
 * 转换数字为逗号隔开的格式
 * @param num 数字
 * @param n 保留小数位数 默认2
 */
export function numFormatMoney(num: string | number, n: number = 2) {
  //存在逗号时直接返回元数据
  if (num && (num + '').indexOf(',') !== -1) {
    return num
  }
  num = toNumber(num + '')
  num = num.toFixed(n)
  num = parseFloat(num)
  num = num.toLocaleString()
  return num
}

export function optionsToEnums(options: { label: string; value: string }[]) {
  return options.map((item) => {
    return {
      name: item.value,
      disName: item.label
    }
  })
}

/**
 * 返回接口需要的文件对象
 * @param path minio文件路径
 * @param type 业务文件类型
 */
export function getFileObj(path: string, type: string) {
  const suffix = _.last(path.split('.'))
  const name = _.last(path.split('/'))
  return {
    type,
    suffix: '.' + suffix,
    name: name,
    address: path
  }
}
