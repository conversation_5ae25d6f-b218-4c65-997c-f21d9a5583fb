/**
 * 数据字典工具类
 */
import { useDictStoreWithOut } from '@/store/modules/dict'
import { ElementPlusInfoType } from '@/types/elementPlus'

const dictStore = useDictStoreWithOut()

/**
 * 获取 dictType 对应的数据字典数组
 *
 * @param dictType 数据类型
 * @returns {*|Array} 数据字典数组
 */
export interface DictDataType {
  dictType: string
  label: string
  value: string | number | boolean
  colorType: ElementPlusInfoType | ''
  cssClass: string
}

export interface NumberDictDataType extends DictDataType {
  value: number
}

export interface StringDictDataType extends DictDataType {
  value: string
}

export const getDictOptions = (dictType: string) => {
  return dictStore.getDictByType(dictType) || []
}

export const getIntDictOptions = (dictType: string): NumberDictDataType[] => {
  // 获得通用的 DictDataType 列表
  const dictOptions: DictDataType[] = getDictOptions(dictType)
  // 转换成 number 类型的 NumberDictDataType 类型
  // why 需要特殊转换：避免 IDEA 在 v-for="dict in getIntDictOptions(...)" 时，el-option 的 key 会告警
  const dictOption: NumberDictDataType[] = []
  dictOptions.forEach((dict: DictDataType) => {
    dictOption.push({
      ...dict,
      value: parseInt(dict.value + '')
    })
  })
  return dictOption
}

export const getStrDictOptions = (dictType: string) => {
  // 获得通用的 DictDataType 列表
  const dictOptions: DictDataType[] = getDictOptions(dictType)
  // 转换成 string 类型的 StringDictDataType 类型
  // why 需要特殊转换：避免 IDEA 在 v-for="dict in getStrDictOptions(...)" 时，el-option 的 key 会告警
  const dictOption: StringDictDataType[] = []
  dictOptions.forEach((dict: DictDataType) => {
    dictOption.push({
      ...dict,
      value: dict.value + ''
    })
  })
  return dictOption
}

export const getBoolDictOptions = (dictType: string) => {
  const dictOption: DictDataType[] = []
  const dictOptions: DictDataType[] = getDictOptions(dictType)
  dictOptions.forEach((dict: DictDataType) => {
    dictOption.push({
      ...dict,
      value: dict.value + '' === 'true'
    })
  })
  return dictOption
}

/**
 * 获取指定字典类型的指定值对应的字典对象
 * @param dictType 字典类型
 * @param value 字典值
 * @return DictDataType 字典对象
 */
export const getDictObj = (dictType: string, value: any): DictDataType | undefined => {
  const dictOptions: DictDataType[] = getDictOptions(dictType)
  for (const dict of dictOptions) {
    if (dict.value === value + '') {
      return dict
    }
  }
}

/**
 * 获得字典数据的文本展示
 *
 * @param dictType 字典类型
 * @param value 字典数据的值
 * @return 字典名称
 */
export const getDictLabel = (dictType: string, value: any): string => {
  const dictOptions: DictDataType[] = getDictOptions(dictType)
  const dictLabel = ref('')
  dictOptions.forEach((dict: DictDataType) => {
    if (dict.value === value + '') {
      dictLabel.value = dict.label
    }
  })
  return dictLabel.value
}

export enum DICT_TYPE {
  USER_TYPE = 'user_type',
  COMMON_STATUS = 'common_status',
  TERMINAL = 'terminal', // 终端
  DATE_INTERVAL = 'date_interval', // 数据间隔

  // ========== INFRA 模块 ==========
  INFRA_BOOLEAN_STRING = 'infra_boolean_string',
  INFRA_JOB_STATUS = 'infra_job_status',
  INFRA_JOB_LOG_STATUS = 'infra_job_log_status',
  INFRA_API_ERROR_LOG_PROCESS_STATUS = 'infra_api_error_log_process_status',
  INFRA_CONFIG_TYPE = 'infra_config_type',
  INFRA_CODEGEN_TEMPLATE_TYPE = 'infra_codegen_template_type',
  INFRA_CODEGEN_FRONT_TYPE = 'infra_codegen_front_type',
  INFRA_CODEGEN_SCENE = 'infra_codegen_scene',
  INFRA_FILE_STORAGE = 'infra_file_storage',
  INFRA_OPERATE_TYPE = 'infra_operate_type',

  // ========== BPM 模块 ==========
  BPM_MODEL_TYPE = 'bpm_model_type',
  BPM_MODEL_FORM_TYPE = 'bpm_model_form_type',
  BPM_TASK_CANDIDATE_STRATEGY = 'bpm_task_candidate_strategy',
  BPM_PROCESS_INSTANCE_STATUS = 'bpm_process_instance_status',
  BPM_TASK_STATUS = 'bpm_task_status',
  BPM_OA_LEAVE_TYPE = 'bpm_oa_leave_type',
  BPM_PROCESS_LISTENER_TYPE = 'bpm_process_listener_type',
  BPM_PROCESS_LISTENER_VALUE_TYPE = 'bpm_process_listener_value_type',

  //===========ccms模块====================
  BANK_ACCOUNT_TYPE = 'bank_account_type',
  BANK = 'bank',
  OPEN_CLOSE = 'open_close',
  ASSETS_ONLINE_STATUS = 'assets_online_status',
  ASSETS_STATUS = 'assets_status',
  ENABLE_STATUS = 'enable_status',
  INSURE_TYPE = 'insure_type',
  INSURE_STATUS = 'insure_status',
  DEAL_STATUS = 'deal_status',
  DEAL_RESULT = 'deal_result',
  SALE_TYPE = 'sale_type',
  DEAL_REPORT_STATUS = 'deal_report_status',
  ACCIDENT_LEVEL = 'accident_level',
  TRANSPORT_REQUIRE = 'transport_require',
  CAR_ROUTE_TYPE = 'car_route_type',
  CAR_TYPE = 'car_type',
  AUDIT_STATUS = 'audit_status',
  TRANSPORT_ORDER_STATUS = 'transport_order_status',
  ACCIDENT_HIGHWAY_SECTION = 'accident_highway_section',
  ACCIDENT_HIGHWAY_IT_LEAVE = 'accident_highway_it_level',
  ACCIDENT_HIGHWAY_GOV_LEVEL = 'accident_highway_gov_level',
  REPORT_TYPE = 'report_type',
  BOOL_STATUS = 'bool_status',
  TRANSPORT_STATUS = 'transport_status',
  FAULT_LEVEL = 'fault_level',
  SIGN_STATUS = 'sign_status',
  DAMAGE_LEVEL = 'damage_level',
  IDENTITY_TYPE = 'identity_type',
  ORDER_TRANSPORT_TYPE = 'order_transport_type',
  SALE_STATUS = 'sale_status',
  CAR_CATEGORY = 'car_category',
  WARN_LEVEL = 'warn_level',
  SEASONS_TYPE = 'seasons_type',
  WARN_TYPE = 'warn_type',
  ERROR_TYPE = 'error_type',
  COST_TYPE = 'cost_type',
  CCMS_BILLING_ELEMENT = 'ccms_billing_element',
  VEHICLE_KIND = 'vehicle_kind',
  ROUTE_RANGE = 'route_range'
}
