import type { App } from 'vue'
import { hasRole } from './permission/hasRole'
import { hasPermi } from './permission/hasPermi'
import { ElLoading } from 'element-plus'
import { debounce } from 'lodash-es'

/**
 * 导出指令：v-xxx
 * @methods hasRole 用户权限，用法: v-hasRole
 * @methods hasPermi 按钮权限，用法: v-hasPermi
 */
export const setupAuth = (app: App<Element>) => {
  hasRole(app)
  hasPermi(app)
}

/**
 * 导出指令：v-mountedFocus
 */
export const setupMountedFocus = (app: App<Element>) => {
  app.directive('mountedFocus', {
    mounted(el) {
      el.focus()
    }
  })
}

/**
 * 导出指令：v-eleLoadMore
 */
export const setupEleLoadMore = (app: App<Element>) => {
  app.directive('eleLoadMore', {
    mounted(el, binding) {
      //遮罩显示的范围
      const SCROLL_DOM: HTMLDivElement | null = el.querySelector('.el-select-dropdown')
      // 获取element-ui定义好的scroll盒子
      const SELECT_WRAP_DOM: HTMLDivElement | null = el.querySelector(
        '.el-select-dropdown .el-select-dropdown__wrap'
      )
      if (SELECT_WRAP_DOM && SCROLL_DOM) {
        const fun = debounce(async function () {
          //@ts-ignore
          const self: any = this
          const CONDITION = self.scrollHeight - self.scrollTop <= self.clientHeight
          if (CONDITION) {
            const { params, callback } = binding.value
            if (params) {
              if (params.pageSize * params.pageNo > params.total) {
                return
              }
              params.pageNo += 1
            }

            if (callback) {
              const load = ElLoading.service({ target: SCROLL_DOM, lock: true })
              //执行的函数应该返回当前的页码用于更新控件内部的页码
              await callback(params)
              load.close()
            }
          }
        }, 100)
        SELECT_WRAP_DOM.addEventListener('scroll', fun)
      }
    }
  })
}
