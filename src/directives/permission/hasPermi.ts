import type { App } from 'vue'
import { CACHE_KEY, useCache } from '@/hooks/web/useCache'
import { isCD } from '@/utils/sysType'

const { t } = useI18n() // 国际化

export function hasPermi(app: App<Element>) {
  app.directive('hasPermi', (el, binding) => {
    let { value } = binding

    if (value && value instanceof Array && value.length > 0) {
      // 这里如果是城配的话替换前缀`ccms:`为`ctms:`
      if(isCD){
        value = value.map(item => {
          return item.replace('ccms:', 'ctms:')
        })
      }
      const hasPermissions = hasPermission(value)

      if (!hasPermissions) {
        el.parentNode && el.parentNode.removeChild(el)
      }
    } else {
      throw new Error(t('permission.hasPermission'))
    }
  })
}

export const hasPermission = (permission: string[]) => {
  const { wsCache } = useCache()
  const all_permission = '*:*:*'
  const userInfo = wsCache.get(CACHE_KEY.USER)
  const permissions = userInfo?.permissions || []

  return permissions.some((p: string) => {
    // 这里如果是城配的话替换前缀`ccms:`为`ctms:`
    if(isCD){
      p = p.replace('ccms:', 'ctms:')
    }
    return all_permission === p || permission.includes(p)
  })
}
