// 引入unocss css
import '@/plugins/unocss'

// 导入全局的svg图标
import '@/plugins/svgIcon'

// 初始化多语言
import { setupI18n } from '@/plugins/vueI18n'

// 引入状态管理
import { setupStore } from '@/store'

// 全局组件
import { setupGlobCom } from '@/components'

// 引入 element-plus
import { setupElementPlus } from '@/plugins/elementPlus'

// 引入 form-create
import { setupFormCreate } from '@/plugins/formCreate'

// 引入全局样式
import '@/styles/index.scss'

// 引入动画
import '@/plugins/animate.css'

// 路由
import router, { setupRouter } from '@/router'

// 指令
import { setupAuth, setupEleLoadMore, setupMountedFocus } from '@/directives'

import { createApp } from 'vue'

import App from './App.vue'

import './permission'

import '@/plugins/tongji' // 百度统计
import Logger from '@/utils/Logger'

import VueDOMPurifyHTML from 'vue-dompurify-html' // 解决v-html 的安全隐患

// vxe
import VxeUIAll from 'vxe-pc-ui'
import 'vxe-pc-ui/lib/style.css'
import VxeUITable from 'vxe-table'
import 'vxe-table/lib/style.css'

import vue3SeamlessScroll from 'vue3-seamless-scroll'

//             VxeUI.setIcon({
//               // table
//               TABLE_SORT_ASC: 'vxe-icon-caret-up',
//               TABLE_SORT_DESC: 'vxe-icon-caret-down',
//               TABLE_FILTER_NONE: 'vxe-icon-funnel',
//               TABLE_FILTER_MATCH: 'vxe-icon-funnel',
//               TABLE_EDIT: 'vxe-icon-edit',
//               TABLE_TITLE_PREFIX: 'vxe-icon-question-circle-fill',
//               TABLE_TITLE_SUFFIX: 'vxe-icon-question-circle-fill',
//               TABLE_TREE_LOADED: 'vxe-icon-spinner roll',
//               TABLE_TREE_OPEN: 'vxe-icon-caret-right rotate90',
//               TABLE_TREE_CLOSE: 'vxe-icon-caret-right',
//               TABLE_EXPAND_LOADED: 'vxe-icon-spinner roll',
//               TABLE_EXPAND_OPEN: 'vxe-icon-arrow-right rotate90',
//               TABLE_EXPAND_CLOSE: 'vxe-icon-arrow-right',
//               TABLE_CHECKBOX_CHECKED: 'vxe-icon-checkbox-checked-fill',
//               TABLE_CHECKBOX_UNCHECKED: 'vxe-icon-checkbox-unchecked',
//               TABLE_CHECKBOX_INDETERMINATE: 'vxe-icon-checkbox-indeterminate-fill',
//               TABLE_RADIO_CHECKED: 'vxe-icon-radio-checked-fill',
//               TABLE_RADIO_UNCHECKED: 'vxe-icon-radio-unchecked',
//               TABLE_CUSTOM_SORT: 'vxe-icon-drag-handle',
//               TABLE_MENU_OPTIONS: 'vxe-icon-arrow-right',
//               TABLE_DRAG_ROW: 'vxe-icon-drag-handle',
//               TABLE_DRAG_COLUMN: 'vxe-icon-drag-handle',

//               // toolbar
//               TOOLBAR_TOOLS_REFRESH: 'vxe-icon-repeat',
//               TOOLBAR_TOOLS_REFRESH_LOADING: 'vxe-icon-repeat roll',
//               TOOLBAR_TOOLS_IMPORT: 'vxe-icon-upload',
//               TOOLBAR_TOOLS_EXPORT: 'vxe-icon-download',
//               TOOLBAR_TOOLS_PRINT: 'vxe-icon-print',
//               TOOLBAR_TOOLS_FULLSCREEN: 'vxe-icon-fullscreen',
//               TOOLBAR_TOOLS_MINIMIZE: 'vxe-icon-minimize',
//               TOOLBAR_TOOLS_CUSTOM: 'vxe-icon-custom-column',
//               TOOLBAR_TOOLS_FIXED_LEFT: 'vxe-icon-fixed-left',
//               TOOLBAR_TOOLS_FIXED_LEFT_ACTIVE: 'vxe-icon-fixed-left-fill',
//               TOOLBAR_TOOLS_FIXED_RIGHT: 'vxe-icon-fixed-right',
//               TOOLBAR_TOOLS_FIXED_RIGHT_ACTIVE: 'vxe-icon-fixed-right-fill'
//             })

// 创建实例
const setupAll = async () => {
  const app = createApp(App)

  await setupI18n(app)

  setupStore(app)

  setupGlobCom(app)

  setupElementPlus(app)

  setupFormCreate(app)

  setupRouter(app)

  // directives 指令
  setupAuth(app)
  setupMountedFocus(app)
  setupEleLoadMore(app)

  await router.isReady()

  app.use(VueDOMPurifyHTML)
  app.use(VxeUIAll)
  app.use(VxeUITable)
  app.use(vue3SeamlessScroll, {
    name: 'vue-seamless-scroll'
  })

  app.mount('#app')
}

setupAll()

Logger.prettyPrimary(`欢迎使用`, import.meta.env.VITE_APP_TITLE)
