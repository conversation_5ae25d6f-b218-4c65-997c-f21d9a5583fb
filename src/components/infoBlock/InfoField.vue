<script setup lang="ts">
import { computed, inject, ref, Ref } from 'vue'
import { getDictLabel } from '@/utils/dict'

const props = defineProps<{
  name: string
  text?: string
  labelWidth?: string
  enumType?: string
  //必填
  required?: boolean
}>()

const globalLabelWidth = inject<Ref<string>>('labelWidth', ref('100px'), true)

const labelStyle = computed(() => {
  return {
    width: props.labelWidth || globalLabelWidth.value
  }
})
</script>

<template>
  <div class="flex gap-[10px]">
    <div class="flex-shrink-0 text-nowrap text-right" :style="labelStyle">
      <div class="text-[12px] text-[#999999]">
        <span class="text-[#E04F48]" v-if="props.required"> * </span>
        {{ props.name }}：
      </div>
    </div>
    <div class="flex-1 truncate text-[12px] text-[#333333]">
      <slot>
        <template v-if="props.enumType && props.text">
          {{ getDictLabel(props.enumType, props.text) }}
        </template>
        <template v-else>{{ props.text || '-' }}</template>
      </slot>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
