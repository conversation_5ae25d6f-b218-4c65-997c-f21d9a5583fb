<script lang="ts" setup>
import InfoTitle from "@/components/infoBlock/InfoTitle.vue";
import { provide, toRef } from "vue";

const props = defineProps<{
  name: string;
  labelWidth?: string;
}>();

//声明labelWidth
provide("labelWidth", toRef(props, "labelWidth"));
</script>

<template>
  <div class="flex flex-col gap-5">
    <InfoTitle :name="props.name"/>
    <div class="flex flex-col gap-5">
      <slot></slot>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
