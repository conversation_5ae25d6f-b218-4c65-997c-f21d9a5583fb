import { h } from 'vue'

function tempRender(...params: any[]) {
  if (params[1]) {
    let innerHTML = params[2]
    //修复vue3中innerHTML属性无效的问题,暂时将这个参数覆盖到第三个参数撒好难过
    if (params[1].domProps) {
      if (params[1].domProps.innerHTML) {
        innerHTML = params[1].domProps.innerHTML
      }
    }

    //修复vue3中on事件无效的问题
    const tempProps = params[1]
    if (params[1].on) {
      for (const key in params[1].on) {
        tempProps[`on${key}`] = params[1].on[key]
      }
    }
    if(innerHTML){
      return h(params[0], tempProps, innerHTML)
    }else{
      return h(params[0], tempProps)
    }

  } else {
    return h(...(params as [string]))
  }
}

export default {
  name: 'TableExpand',
  functional: true,
  props: {
    row: Object,
    render: Function,
    index: Number,
    column: {
      type: Object,
      default: null
    }
  },
  render: (props: any) => {
    const params: any = {
      row: props.row,
      index: props.index
    }
    if (props.column) params.column = props.column
    return props.render(tempRender, params)
  }
}
