<template>
  <div :title="iconObj.title" :style="{ color: color }" @click="$emit('click')">
    <customIcon :icon-class="iconObj.icon"></customIcon>
  </div>
</template>
<script>
import CustomIcon from '@/components/customIcon/Index.vue'

export default {
  components: {
    CustomIcon
  },
  props: {
    iconObj: {
      type: Object,
      default: () => {
        return {}
      }
    },
    color: {
      type: String,
      default: '#3D6DCC'
    }
  }
}
</script>

<style scoped lang="scss">
.icon {
  cursor: pointer;
  margin-right: 15px;

  div {
    //margin-right: 15px;

    > i {
      color: #3d6dcc;
      font-size: 16px;
    }
  }
}
</style>
