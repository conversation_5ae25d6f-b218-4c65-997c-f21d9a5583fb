<template>
  <el-table-column
    :sortable="itemColumns.sortable ? true : false"
    :filter-multiple="false"
    :key="itemColumns.key"
    :prop="itemColumns.key"
    :fixed="itemColumns.fixed"
    :align="itemColumns.align ? itemColumns.align : 'left'"
    :column-key="itemColumns.columnKey"
    :label="itemColumns.title"
    :filters="itemColumns.filters"
    :filtered-value="itemColumns.filteredValue"
    :width="itemColumns.width"
    :show-overflow-tooltip="itemColumns.key !== 'option'"
    :minWidth="itemColumns.minWidth"
  >
    <template #header="scope" v-if="itemColumns.renderHeader">
      <Expand :row="scope.column" :render="itemColumns.renderHeader"/>
    </template>
    <template #default="scope">
      <Expand
        :key="scope.row ? scope.row._rowKey : scope.$index"
        :row="scope.row"
        :render="itemColumns.render"
        :index="scope.$index"
      />
      <template v-if="itemColumns.children && !itemColumns.isHide">
        <!-- 使用自身组件会导致勾选同比环比列表显示异常暂时这样写,目前列表只支持最多两层 -->
        <template v-for="(item, index) of itemColumns.children">
          <el-table-column
            :sortable="item.sortable ? true : false"
            :filter-multiple="false"
            :key="index"
            :prop="item.key"
            :fixed="item.fixed"
            :align="item.align ? item.align : 'center'"
            :column-key="item.columnKey"
            :label="item.title"
            :filters="item.filters"
            :filtered-value="item.filteredValue"
            :width="item.width"
            :minWidth="item.minWidth"
            v-if="!item.isHide"
          >
            <template #header="headerScope" v-if="item.renderHeader">
              <Expand :row="headerScope.column" :render="item.renderHeader" />
            </template>
            <template #default="defaultScope">
              <Expand
                :key="defaultScope.row ? defaultScope.row._rowKey : index"
                :row="defaultScope.row"
                :render="item.render"
                :index="defaultScope.$index"
              />
            </template>
          </el-table-column>
        </template>
      </template>
    </template>
  </el-table-column>
</template>

<script>
import Expand from './expands'

export default {
  name: 'TreeTableColumn',
  components: { Expand },
  props: {
    itemColumns: {
      type: Object,
      default: () => {
        return {}
      }
    }
  }
}
</script>

<style scoped></style>
