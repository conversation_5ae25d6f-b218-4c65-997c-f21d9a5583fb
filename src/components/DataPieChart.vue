<template>
  <div class="outer-box">
    <div :id="idRef" style="width: 100%; height: 100%"></div>
  </div>
</template>

<script setup lang="ts">
import * as echarts from "echarts";
import { onMounted, defineProps, ref, watch } from "vue";
const EchartName = ref<any>(null);

const props = defineProps<{
  idRef: string;
  seriesData: any;
}>();

watch(
  () => props.seriesData,
  () => {
    setEcharts();
  },
);
onMounted(() => {
  setEcharts();
});
const setEcharts = () => {
  var chartDom = document.getElementById(props.idRef)!;
  EchartName.value = echarts.init(chartDom);
  var option: any;
  option = {
    title: {
     text: "{a|" + props.seriesData.unin + "}\n{c|" + props.seriesData.valText + "}",
      left: "center",
      top: "center",
      textStyle: {
        rich: {
          a: {
            fontSize: 20,
            color: props.seriesData.zbColor,
            fontWeight: "600",
            padding: [5, 0, 0, 0],
          },
          c: {
            fontSize: 16,
            color: "#666",
            padding: [5, 0],
          },
        },
      },
    },
    color: [props.seriesData.bgColor],
    series: [
      {
        type: "pie",
        radius: ["64%", "84%"],
        itemStyle: {
          normal: {
            label: {
              show: false,
            },
            labelLine: {
              show: false,
            },
          },
        },
        hoverAnimation: false,
        data: [
          {
            value: (100 - props.seriesData.valNum),
            name: "其他",
            itemStyle: {
              normal: {
                color: props.seriesData.zbColor,
                label: {
                  show: false,
                },
                labelLine: {
                  show: false,
                },
              },
            },
          },
          {
            name: "当前占比",
            value: props.seriesData.valNum,
          },
        ],
      },
    ],
  };
  option && EchartName.value.setOption(option);
};
</script>

<style lang="scss" scoped>
.outer-box {
  width: 100% !important;
  height: 100%;
  >div {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
  }
}
</style>
