<script lang="ts" setup>
import { computed } from "vue";
import { hexToRgba } from "@/utils/color";

const props = defineProps<{
  //16进制数据,不能是rgba的
  color: string;
  name: string;
}>();

const textStyle = computed(() => {
  return {
    color: props.color,
  };
});

const dotStyle = computed(() => {
  return {
    backgroundColor: props.color,
  };
});

const bgColorStyle = computed(() => {
  return {
    backgroundColor: hexToRgba(props.color, 0.1),
  };
});
</script>

<template>
  <div
    class="flex h-[21px] items-center justify-center gap-[2px] text-nowrap rounded px-[6px] text-[12px] leading-none"
    :style="bgColorStyle"
  >
    <div class="size-[6px] rounded-full" :style="dotStyle"></div>
    <div :style="textStyle">{{ props.name }}</div>
  </div>
</template>

<style scoped lang="scss"></style>
