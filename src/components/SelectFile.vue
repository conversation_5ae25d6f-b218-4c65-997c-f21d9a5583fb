<template>
  <div class="upload-file" :class="disabled ? 'disabled' : ''">
    <el-upload
      :before-upload="handleUpload"
      v-if="!disabled"
      :accept="accept"
      @click="onClick"
      :multiple="multiple"
      :drag="drag"
      action="//localhost"
    >
      <slot>
        <el-button type="primary" icon="ios-link" class="btn">{{ title }}</el-button>
      </slot>
    </el-upload>
    <div class="file-list" v-if="showFileList">
      <div
        class="file-item"
        @click="download(item)"
        v-for="(item, index) in showFileShow"
        :key="index"
      >
        <i class="el-icon-link"></i>
        {{ item.name }}
        <i
          class="el-icon-close"
          @click.stop="removeFile(item)"
          v-if="!disabled && isShowDel"
          style="color: #cc0000"
        ></i>
      </div>
    </div>
  </div>
</template>

<script>
// import {ajaxDownload} from '@/util/apiUtil'

export default {
  name: 'SelectFile',
  props: {
    //文字
    title: {
      type: String,
      default: '上传附件'
    },
    //选择的文件类型
    accept: {
      type: String
    },
    showFileList: {
      type: Boolean,
      default: true
    },
    //输出需要上传的文件
    outFiles: [File, Array],
    //需要回显的文件列表 格式:{name:'文件名',path:'路径名'}
    inFiles: {
      type: Array,
      default: () => {
        return []
      }
    },
    //删除的回显文件对象列表
    delInFiles: {
      type: Array,
      default: () => {
        return []
      }
    },
    isDownload: {
      type: Boolean,
      default: true
    },
    //是否多选
    multiple: {
      type: Boolean,
      default: false
    },
    drag: {
      type: Boolean,
      default: false
    },
    isShowDel: {
      type: Boolean,
      default: true
    },
    disabled: Boolean,
    maxSize: {
      type: Number,
      default: null
    }, // 文件限制大小，单位kb
    tipDesc: {
      type: String,
      default: null
    } // 提示文字
  },
  data() {
    return {
      //原生File对象
      fileList: []
    }
  },
  computed: {
    //显示的文件列表
    showFileShow() {
      if (!this.multiple && this.fileList.length) {
        return [...this.fileList]
      }
      return [...this.inFiles, ...this.fileList]
    }
  },
  watch: {
    fileList:{
      handler: function(data) {
        console.log(data,1222222)
        if (this.multiple) {
          this.$emit('update:out-files', data)
        } else {
          this.$emit('update:out-files', data[0])
        }
      },
      deep: true
    }
  },
  methods: {
    //点击选择文件时清除之前选择的所有文件,不动回显的文件列表
    onClick() {
      if (!this.multiple) {
        this.fileList = []
      }
    },
    download(file) {
      this.$emit('click-file-name', file)
      if (!this.isDownload) {
        return
      }
      //todo 选择的文件暂时不支持点击下载
      if (!(file instanceof File)) {
        // ajaxDownload('/file/download', {path: file.path, fileName: file.name + file.suffix})
      }
    },
    removeFile(file) {
      //原生对象直接移除文件列表,不是原生对象添加到移除列表中
      if (file instanceof File) {
        const fileList = this.fileList
        fileList.splice(fileList.indexOf(file), 1)
      } else {
        const inFiles = this.inFiles
        inFiles.splice(inFiles.indexOf(file), 1)
        //eslint-disable-next-line
        this.delInFiles.push(file)
        this.$emit('update:delInFiles', this.delInFiles)
      }
    },
    handleUpload(file) {
      console.log(file)
      // 校验上传文件大小，单位kb
      let size = file.size / 1024 // kb
      if (this.maxSize && size > this.maxSize) {
        console.log(this.tipDesc)
        this.$message({
          message: this.tipDesc || `文件大小不能超过${this.maxSize / 1024}M`,
          type: 'warning'
        })
        return false
      }
      //todo 验证文件;
      this.fileList.push(file)
      console.log(this.fileList,22222)
      return false
    },
    clearFiles() {
      this.fileList = []
    }
  }
}
</script>

<style scoped lang="scss">
.upload-file {
  flex-grow: 1;

  &.disabled {
    .file-list {
      .file-item {
        margin-left: 2rem;
      }
    }
  }

  .ivu-upload {
    display: inline-block;
  }

  .file-list {
    display: inline-block;

    .file-item {
      display: inline-block;
      margin-left: 5rem;
      color: var(--el-color-primary);
      font-size: 1.3rem;
      cursor: pointer;
    }
  }
}
</style>
