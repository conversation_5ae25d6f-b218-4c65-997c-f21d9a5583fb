<template>
  <div class="outer-box">
    <div :id="idRef" style="width: 100%; height: 100%"></div>
  </div>
</template>

<script setup lang="ts">
import * as echarts from "echarts";
import { onMounted, defineProps, ref, watch } from "vue";
type EChartsOption = echarts.EChartsOption;
const EchartName = ref<any>(null);

const props = defineProps<{
  idRef: string;
  pieData: any;
  // title: string;
  // percentageNum?: string | number;
  // dataArray: any[];
  // colorArray?: string[];
  // legendHide?: boolean; //是否开启legend
  // positionType: string; //1代表两侧，2代表中间
  // fontSize?: string;
  // leftSize?: string;
  // legendLeftSize?: string;
  // legendfontSize?: string;
  // seriesCenter?: any;
}>();

// const colorChoiceArr = ref<string[]>([]);
watch(
  () => props.pieData.seriesData,
  (newValue, oldValue) => {
    setEcharts();
  },
);

onMounted(() => {
  setEcharts();
});

const setEcharts = () => {
  var chartDom = document.getElementById(props.idRef)!;

  // 检查是否已经有图表实例存在
  // if (EchartName.value) {
  //   // 清除已有的图表实例
  //   EchartName.value.dispose();
  // }

  EchartName.value = echarts.init(chartDom);

  var option: EChartsOption;

  option = {
    tooltip: {
      trigger: "item",
    },
    legend: {
      bottom: "0%",
      left: "center",
    },
    color: props.pieData.color,
    series: [
      {
        name: "",
        type: "pie",
        radius: ["50%", "70%"],
        center: ["50%", "40%"],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: "center",
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 16,
            fontWeight: "bold",
          },
        },
        labelLine: {
          show: false,
        },
        // data: [
        //   { value: 1048, name: "Engine" },
        //   { value: 735, name: "Direct" },
        // ],
        data: props.pieData.seriesData,
      },
    ],
  };

  // option.color = colorChoiceArr.value;
  // if (props.colorArray != null) {
  //   option.color = props.colorArray;
  // }

  option && EchartName.value.setOption(option);
};
</script>

<style lang="scss" scoped>
.outer-box {
  width: 100%;
  height: 100%;
  // >div {
  //   width: 100% !important;
  //   height: 100%;
  //   display: flex;
  //   justify-content: center;
  // }
  // box-shadow: inset 0px -10px 10px 0px rgba(0, 155, 255, 0.1);
  //   background-color: rgba(0, 125, 153, 0.3);
}
</style>
