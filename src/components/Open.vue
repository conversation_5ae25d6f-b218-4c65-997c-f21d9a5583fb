<template>
  <el-dialog
    class="custom-open"
    v-model="modalShow"
    :show-close="showClose"
    v-bind="$attrs"
    draggable
    :fullscreen="isFullscreen"
    :width="width + 'px'"
    :top="top"
    v-if="modalShow"
    :custom-class="className"
    @close="$emit('on-close')"
    :close-on-click-modal="closeModal"
    :title="title"
  >
    <template #header="{ close }">
      <div class="relative h-54px flex items-center justify-between pl-15px pr-15px">
        <slot name="title">
          {{ title }}
        </slot>
        <div
          class="absolute right-15px top-[50%] h-54px flex translate-y-[-50%] items-center justify-between"
        >
          <Icon
            class="is-hover mr-10px cursor-pointer"
            :icon="isFullscreen ? 'radix-icons:exit-full-screen' : 'radix-icons:enter-full-screen'"
            color="var(--el-color-info)"
            hover-color="var(--el-color-primary)"
            @click="toggleFull"
          />
          <Icon
            class="is-hover cursor-pointer"
            icon="ep:close"
            hover-color="var(--el-color-primary)"
            color="var(--el-color-info)"
            @click="close"
          />
        </div>
      </div>
    </template>
    <slot></slot>
    <template #footer>
      <div class="dialog-footer" v-if="!hideFoot">
        <el-button type="primary" class="btu save-btn" v-if="!hideSave" @click="save"
          >{{ saveText }}
        </el-button>
        <el-button v-show="!hideClose" class="btu close-btn" @click="close"
          >{{ closeText }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { cloneDeep } from 'lodash-es'

export default {
  name: 'Open',
  setup() {
    const isFullscreen = ref(false)

    const toggleFull = () => {
      isFullscreen.value = !unref(isFullscreen)
    }
    return {
      isFullscreen,
      toggleFull
    }
  },
  props: {
    saveText: {
      type: String,
      default: '确定'
    },
    closeText: {
      type: String,
      default: '取消'
    },
    hideSave: {
      type: Boolean,
      default: false
    },
    hideClose: {
      type: Boolean,
      default: false
    },
    hideFoot: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '信息'
    },
    width: {
      type: [Number, String],
      default: 410
    },
    className: {
      type: String
    },
    showClose: {
      type: Boolean,
      default: false
    },
    closeModal: {
      type: Boolean,
      default: false
    }
  },
  emits: ['on-save', 'on-close'],
  data() {
    return {
      modalShow: false,
      top: '15vh',
      oldData: null
    }
  },
  methods: {
    save() {
      this.$emit('on-save')
    },
    close() {
      this.$emit('on-close')
      this.modalShow = false
      window.removeEventListener('resize', this.dialogPosition)
    },
    dialogPosition() {
      if (document.body.clientHeight <= 600) {
        this.top = '2vh'
      } else if (document.body.clientHeight > 600 && document.body.clientHeight <= 700) {
        this.top = '10vh'
      } else {
        this.top = '15vh'
      }
    },
    open() {
      //初始化父组件中的data值
      //需要将this.$parent.$options.data函数的this指向this.$parent再执行 保证函数中的this指向正常
      //修复表单校验自定义函数报错的问题

      // 弹窗关闭时自动还原组件数据
      if (this.$ && this.$.parent && this.$.parent.ctx && this.$.parent.ctx.resetOpenData) {
        this.$.parent.ctx.resetOpenData(cloneDeep(this.oldData))
      }

      //修复验证信息直接提示的问题
      this.modalShow = false
      this.$nextTick(() => {
        //打开弹窗之前显示弹窗
        this.modalShow = true
        this.$nextTick(() => {
          //@ts-ignore
          this.isOpen = true
          this.dialogPosition()
          window.addEventListener('resize', this.dialogPosition)
        })
      })
    }
  },
  created() {
    //记录组件初始data值
    if (this.$ && this.$.parent) {
      this.oldData = cloneDeep(this.$.parent.data)
    }
    //不存在默认重置时提供统一重置弹窗状态
    if (this.$ && this.$.parent && this.$.parent.ctx && !this.$.parent.ctx.resetOpenData) {
      function reset(data) {
        for (const key in data) {
          this[key] = data[key]
        }
      }

      this.$.parent.ctx.resetOpenData = reset.bind(this.$.parent.proxy)
    }
  }
}
</script>

<style lang="scss">
.custom-open {
  padding: 0;

  .#{$elNamespace}-overlay-dialog {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .#{$elNamespace}-dialog {
    margin: 0 !important;

    &__header {
      height: 54px;
      padding: 0;
      margin-right: 0 !important;
      border-bottom: 1px solid var(--el-border-color);
    }

    &__body {
      padding: 15px !important;
    }

    &__footer {
      text-align: right;
      padding: 10px 20px;
      border-top: 1px solid var(--el-border-color);
    }

    &__headerbtn {
      top: 0;
    }
  }
}
</style>
