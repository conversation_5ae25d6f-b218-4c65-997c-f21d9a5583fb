<script setup lang="ts">
import { computed, ref } from "vue";
import OpenComponents from "@/components/Open.vue";

const fileList = ref<
  {
    name: string;
    address: string;
  }[]
>([]);

const imgFilePath = computed(() => {
  return (fileList.value || []).map((item) => {
    return item.address || "";
  });
});

const openRef = ref<typeof OpenComponents.prototype>();

function open(list: typeof fileList.value) {
  openRef.value?.open();
  fileList.value = list;
}

defineExpose({ open });
</script>

<template>
  <OpenComponents ref="openRef" hide-foot title="查看图片" :width="500">
    <div
      class="grid grid-cols-[repeat(auto-fill,_100px)] justify-between gap-[10px] overflow-hidden"
    >
      <div class="flex flex-col gap-1" v-for="(item, index) in fileList" :key="index">
        <el-image
          class="size-[100px] rounded overflow-hidden"
          :src="imgFilePath[index]"
          fit="cover"
          :preview-src-list="imgFilePath"
          :initial-index="index"
        />
        <div class="truncate">{{ item.name }}</div>
      </div>
    </div>
  </OpenComponents>
</template>

<style scoped lang="scss"></style>
