<template>
  <div class="outer-box">
    <div :id="idRef" style="width: 100%; height: 100%"></div>
  </div>
</template>

<script setup lang="ts">
import * as echarts from "echarts";
import { onMounted, onActivated, onBeforeUnmount, defineProps, ref, watch } from "vue";

const EchartName = ref<any>(null);

const props = defineProps<{
  idRef: string;
  lineData: any;
}>();

const setEcharts = () => {
  const chartDom = document.getElementById(props.idRef);
  if (!chartDom) {
    console.error(`Element with id ${props.idRef} not found`);
    return;
  }

  try {
    if (!EchartName.value) {
      EchartName.value = echarts.init(chartDom);
    }

    const option: any = {
      grid: {
        left: '2%',
        right: '4%',
        bottom: '0%',
        top: '10%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: props.lineData.xData
      },
      yAxis: {
        type: 'value',
        axisLine: {
          show: false,
        },
        axisLabel: {
          textStyle: {
            color: "#666",
          },
        },
        axisTick: {
          show: false,
        },
      },
      series: [
        {
          data: props.lineData.yData,
          type: 'line',
          areaStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(
                0,
                0,
                0,
                1,
                [
                  {
                    offset: 0,
                    color: props.lineData.lineAreaColor,
                  },
                  {
                    offset: 1,
                    color: props.lineData.lineAreaColor,
                  },
                ],
                false
              ),
              shadowColor: "rgba(53,142,215, 0.7)",
            },
          },
          lineStyle: {
            normal: {
              color: props.lineData.lineColor,
            },
            borderColor: props.lineData.lineColor,
          },
        }
      ]
    };

    option && EchartName.value.setOption(option);
  } catch (error) {
    console.error('Error initializing or setting ECharts options:', error);
  }
};

const resizeHandler = () => {
  if (EchartName.value) {
    EchartName.value.resize();
  }
};

watch(
  () => props.lineData.yData,
  () => {
    setEcharts();
  },
);

onMounted(() => {
  setEcharts();
  window.addEventListener('resize', resizeHandler);
});

onActivated(() => {
  setEcharts();
});

onBeforeUnmount(() => {
  window.removeEventListener('resize', resizeHandler);
});
</script>

<style lang="scss" scoped>
.outer-box {
  width: 100%;
  height: 100%;
  >div {
    width: 100% !important;
    height: 100%;
    // display: flex;
    // justify-content: center;
  }
  // box-shadow: inset 0px -10px 10px 0px rgba(0, 155, 255, 0.1);
  //   background-color: rgba(0, 125, 153, 0.3);
}

.outer-box {
  width: 100%;
  height: 100%;
  > div {
    width: 100% !important;
    height: 100%;
  }
}

.chart-container {
  width: 100%;
  height: 100%;
}
</style>
