<template>
  <div class="outer-box">
    <div :id="idRef" style="width: 100%; height: 100%"></div>
  </div>
</template>

<script setup lang="ts">
import * as echarts from "echarts";
import { onMounted, defineProps, ref, watch } from "vue";
type EChartsOption = echarts.EChartsOption;
const EchartName = ref<any>(null);

const props = defineProps<{
  idRef: string;
  braData: Object;
}>();

watch(
  () => props.braData.yData,
  (newValue, oldValue) => {
    setEcharts();
  },
);

onMounted(() => {
  setEcharts();
});

const setEcharts = () => {
  var chartDom = document.getElementById(props.idRef)!;

  // 检查是否已经有图表实例存在
  // if (EchartName.value) {
  //   // 清除已有的图表实例
  //   EchartName.value.dispose();
  // }

  EchartName.value = echarts.init(chartDom);

  var option: EChartsOption;
    option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '2%',
      right: '3%',
      bottom: '0%',
      top: '10%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: props.braData.xData,
        axisTick: {
          alignWithLabel: true
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        axisLine: {
          show: false,
        },
        axisLabel: {
          // margin: 20,
          textStyle: {
            color: "#666",
          },
        },
        axisTick: {
          show: false,
        },
      }
    ],
    series: [
      {
        name: '',
        type: 'bar',
        barWidth: '25px',
        color: '#009688',
        itemStyle: { normal: { color: "#2d57cc" } },
        label: {
          normal: {
            show: true,
            position: "top",
            textStyle: { color: "#2d57cc" },
          },
        },
        barWidth: '30%',
        data: props.braData.yData
      }
    ]
  };
  // option.color = colorChoiceArr.value;
  // if (props.colorArray != null) {
  //   option.color = props.colorArray;
  // }

  option && EchartName.value.setOption(option);
};
</script>

<style lang="scss" scoped>
.outer-box {
  width: 100%;
  height: 100%;
  // box-shadow: inset 0px -10px 10px 0px rgba(0, 155, 255, 0.1);
  //   background-color: rgba(0, 125, 153, 0.3);
}
</style>
