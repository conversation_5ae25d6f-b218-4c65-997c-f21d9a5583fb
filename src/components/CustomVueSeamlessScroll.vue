<template>
  <div>
    <vue-seamless-scroll
      class="seamless-warp"
      :style="{ height: height + 'px' }"
      :data="listData"
      :class-option="optionSingleHeight"
    >
      <div class="list">
        <div
          class="item"
          v-for="(item, index) in listData"
          :key="index"
        >
          <div class="text">
            <span v-if="index === 0"
              ><img src="@/assets/images/icon/icon-a.png" alt=""
            /></span>
            <span v-else-if="index === 1"
              ><img src="@/assets/images/icon/icon-b.png" alt=""
            /></span>
            <span v-else-if="index === 2"
              ><img src="@/assets/images/icon/icon-c.png" alt=""
            /></span>
            <span v-else>{{ index + 1 }}</span>
            {{ item.name }}
          </div>
          <div class="num">{{ item.value }}</div>
        </div>
      </div>
    </vue-seamless-scroll>
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType } from "vue";
interface Item {
  name: string;
  value: number;
}
export default defineComponent({
  props: {
    listData: {
      type: Array as PropType<Item[]>,
      required: true,
    },
    height: {
      type: Number,
      default: 190,
    }
  },
  setup() {
    const optionSingleHeight = {
      step: 0.1, // 滚动速度
      limitMoveNum: 5, // 开始无缝滚动的数据量 这里是5，当数据量小于5的时候不会显示滚动
      hoverStop: true, // 是否开启鼠标悬停停止滚动
      direction: 1, // 0向下 1向上 2向左 3向右
      openWatch: true, // 开启data监听，data值发生变化之后会自动重新计算
      singleHeight: 0, // 单步运动停止的高度高度(默认0是无缝不停止的)
      waitTime: 1000, // 单步运动停止的时间(默认1000ms)
    };
    return {
      optionSingleHeight,
    };
  },
});
</script>

<style scoped lang="scss">
.seamless-warp {
  height: 190px;
  overflow: hidden;
}
.item {
  list-style: none;
  padding: 0;
  margin: 0;
}
.item li {
  height: 30px;
  line-height: 30px;
  text-align: center;
  border-bottom: 1px solid #ccc;
}

.list {
  margin-top: 15px;
  // background: #eee;
  // height: 190px;
  .scroller {
    height: 190px;
  }
  .item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    .text {
      width: calc(100% - 50px);
      // background: #ccc;
      // 单行省略号
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      span {
        margin-right: 7px;
        width: 24px;
        height: 24px;
        display: inline-block;
        text-align: center;
        img {
          width: 24px;
          height: 24px;
          display: inline-block;
          vertical-align: middle;
        }
      }
    }
    .num {
      color: #2d57cc;
      font-size: 15px;
      font-weight: 600;
    }
  }
}
</style>
