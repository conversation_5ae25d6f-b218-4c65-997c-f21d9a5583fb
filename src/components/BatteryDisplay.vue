<script setup lang="ts">
import { computed } from "vue";

const props = defineProps<{
  //电量
  battery: number;
}>();

const color = computed(() => {
  return props.battery <= 20 ? "#E04F48" : "#36B336";
});
</script>

<template>
  <div
    class="flex flex-1 items-center gap-[2px]"
    :style="{
      '--battery-color': color,
      '--battery-width': `${props.battery ? (props.battery / 100) * 13.5 : 0}`,
    }"
  >
    <!-- <Battery/> -->
    <div class="text-[12px]" :style="{ color: color }">
      {{ props.battery }}%
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
