<template>
  <div class="upload">
    <el-upload
      action="//localhost"
      list-type="picture-card"
      accept=".jpg,.jpeg,.png,.JPG,.PNG,JPEG"
      :before-upload="beforeUpload"
    >
      <div class="trigger">
        <Icon icon="ep:camera" />
        <div class="tips">点击上传</div>
      </div>
    </el-upload>
  </div>
</template>
<script lang="ts" setup>
import _ from 'lodash-es'
import request from '@/config/axios'
import { sysType } from '@/utils/sysType'
import { ElLoading } from 'element-plus'

const props = withDefaults(
  defineProps<{
    type?: string
  }>(),
  {
    type: 'picture'
  }
)

const emits = defineEmits<{
  (e: 'on-add-file', data: any): void
}>()

const message = useMessage()

async function beforeUpload(file: File) {
  //限制大小
  const isLt5M = file.size / 1024 / 1024 < 5
  if (!isLt5M) {
    message.error('上传图片大小不能超过 5MB!')
    return false
  }

  ElLoading.service({
    lock: true,
    text: '上传中'
  })
  try {
    const params = {
      file: file,
      fileType: props.type as any
    }
    const res = await request.upload({
      url: `/${sysType}/file/uploadFile`,
      data: params
    })
    let suffix = _.last(res.data.split('.'))
    let name = _.last(res.data.split('/'))
    console.log(file.name, 222)
    emits('on-add-file', {
      type: props.type,
      suffix: '.' + suffix,
      name: file.name || name,
      address: res.data
    })
  } finally {
    ElLoading.service().close()
  }
  return false
}
</script>

<style scoped lang="scss">
.upload {
  ::v-deep .el-upload-list__item {
    &.is-ready {
      width: 100px;
      height: 100px;
    }
  }

  ::v-deep .el-upload {
    width: 100px;
    height: 100px;
    position: relative;

    &.el-upload--picture-card {
      line-height: 0;
      border-radius: 4px;
      border: 1px dashed #d9d9d9;
      background: #ffffff;

      i {
        font-size: 22px;
      }
    }

    .el-icon-camera {
      display: block;
    }

    .trigger {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      gap: 20px;

      .tips {
        font-size: 13px;
      }
    }
  }
}
</style>
