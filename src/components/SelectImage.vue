<template>
  <div class="upload">
    <el-upload
      v-if="!imageUrl"
      action="//localhost"
      :list-type="listType"
      :multiple="multiple"
      :auto-upload="true"
      :limit="limit"
      :accept="accept"
      :disabled="isView"
      :before-upload="beforeUpload"
      @on-remove="handleRemove"
    >
      <div class="trigger">
        <Icon icon="ep:camera" />
        <div class="tips">{{ upText }}</div>
      </div>
    </el-upload>

    <div class="image" v-else>
      <div class="mask">
        <Icon icon="ep:zoom-in" @click="dialogVisible = true" />
        <Icon icon="ep:download" v-if="isView" @click="download" />
        <Icon icon="ep:delete" v-else @click="handleRemove" />
      </div>
      <el-image :src="imageUrl" radius="6" fit="cover" />
    </div>
    <el-dialog width="50%" v-model="dialogVisible">
      <el-image :src="imageUrl" alt="" />
    </el-dialog>
    <span class="imgTips" v-if="showTips">请上传图片格式文件，单个文件不超过5M。</span>
  </div>
</template>
<script>
export default {
  props: {
    type: {
      type: String,
      default: ''
    },
    isView: {
      type: Boolean,
      default: false
    },
    accept: {
      type: String,
      default: '.jpg,.jpeg,.png,.JPG,.PNG,JPEG'
    },
    file: {
      type: [String, Object],
      default: ''
    },
    showTips: {
      type: Boolean,
      default: false
    },
    upText: {
      type: String,
      default: '上传图片'
    },
    listType: {
      type: String,
      default: 'picture-card'
    },
    multiple: {
      type: Boolean,
      default: false
    },
    limit: {
      type: Number,
      default: 1
    },
    width: {
      type: String,
      default: '150px'
    },
    height: {
      type: String,
      default: '150px'
    },
    borderradius: {
      type: String,
      default: '8px'
    }
  },
  data() {
    return {
      imageUrl: '',
      dialogVisible: false,
      disabled: false
    }
  },
  watch: {
    file: {
      handler(value) {
        if (value) {
          if (typeof value === 'string') {
            this.imageUrl = 'data:image/jpeg;base64,' + value
          } else {
            this.getBase64(value)
          }
        }
      },
      deep: true
    }
  },
  methods: {
    handleRemove() {
      this.imageUrl = ''
      this.$emit('on-file-change', { file: '', type: this.type })
    },
    beforeUpload(file) {
      console.log(file, 33333)
      //限制大小
      const isLt5M = file.size / 1024 / 1024 < 5
      if (!isLt5M) {
        this.$notify.error('上传图片大小不能超过 5MB!')
        return false
      }
      this.getBase64(file)
      this.$emit('on-file-change', { file: file, type: this.type })
      return false
    },
    // 图片转base64
    getBase64(file) {
      let reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onload = (e) => {
        if (e) {
          this.imageUrl = e.target.result
        }
      }
    },
    download() {
      this.$emit('download', this.imageUrl)
    }
  }
}
</script>

<style scoped lang="scss">
.upload {
  width: auto;

  .imgTips {
    margin-left: 102px;
    position: relative;
    top: -41px;
    font-size: 13px;
    color: #505050;
  }

  .image {
    width: 82px;
    height: 82px;
    border: 1px dashed #c0ccda;
    border-radius: v-bind(borderradius);
    cursor: pointer;
    position: relative;

    .mask {
      display: flex;
      justify-content: center;
      align-items: center;
      position: absolute;
      width: 82px;
      height: 82px;
      left: 0;
      top: 0;
      border: 1px dashed #c0ccda;
      border-radius: v-bind(borderradius);
      opacity: 0;
      background-color: rgba(0, 0, 0, 0.5);
      transition: opacity 0.3s;
      z-index: 9;

      :deep(.el-icon) {
        z-index: 99;
        color: #fff;
        margin: 0 6px;
      }
    }

    :deep(.el-image__inner) {
      width: 80px;
      height: 80px;
      border-radius: v-bind(borderradius);
    }

    :deep(.el-image) {
      border-radius: v-bind(borderradius);
      //background-color: #b8b7b7;
    }
  }

  .image:hover {
    .mask {
      opacity: 1;
    }
  }

  :deep(.el-upload-list__item) {
    &.is-ready {
      width: 82px;
      height: 82px;
    }
  }

  :deep(.el-upload) {
    width: 82px;
    height: 82px;
    position: relative;

    &.el-upload--picture-card {
      line-height: 0;
      border-radius: 4px;
      border: 1px dashed #d9d9d9;
      background: #ffffff;

      i {
        font-size: 22px;
      }
    }

    .el-icon-camera {
      display: block;
    }

    .trigger {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      gap: 20px;

      .tips {
        font-size: 13px;
      }
    }
  }

  :deep(.el-dialog__body) {
    display: flex;
    justify-content: center;
    align-items: center;

    .el-image {
      //width: 200px;
      //height: 200px;
      background-color: #505050;
    }
  }
}
</style>
