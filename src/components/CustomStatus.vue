<template>
  <div class="status-wrap">
    <span v-if="props.title" class="status-title">
      <i class="circle" :style="{ backgroundColor: props.color }"></i>
      {{ props.title }}
    </span>
    <span v-else>-</span>
  </div>
</template>

<script setup lang="ts">
const props = defineProps<{
  title: string;
  color: string;
}>();
</script>

<style scoped lang="scss">
.status-wrap {
  font-size: 13px;
  display: inline-block;
  color: #505050;

  .circle {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 4px;
  }

  .status-title {
    display: flex;
    align-items: center;
  }
}
</style>
