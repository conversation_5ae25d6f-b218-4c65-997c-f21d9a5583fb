<script setup lang="ts">
import { regionData } from './areaData'

import { useVModel } from '@vueuse/core'
import { computed } from 'vue'

const props = defineProps<{
  modelValue: any
}>()

const emits = defineEmits<{
  (e: 'update:modelValue'): void
}>()

const selfValue = useVModel(props, 'modelValue', emits)

const setValue = computed({
  get() {
    const { province, city, county } = selfValue.value
    const res: any[] = []
    if (province) {
      res.push(province)
      if (city) {
        res.push(city)
        if (county) {
          res.push(county)
        }
      }
    }
    return res
  },
  set(val: any) {
    const [province, city, county] = val
    selfValue.value = {
      province: province || '',
      city: city || '',
      county: county || ''
    }
  }
})
</script>

<template>
  <el-cascader
    v-model="setValue"
    :options="regionData"
    :props="{ value: 'label' }"
    placeholder="请选择（省市区）"
    style="width: 100%"
  />
</template>

<style scoped lang="scss"></style>
