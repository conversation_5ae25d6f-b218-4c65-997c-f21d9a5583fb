import { computed, ref } from 'vue'
import { defineStore } from 'pinia'
import { tms_web } from '@/api/ccms'
import { useAppStore } from '@/store/modules/app'
import { store } from '@/store'
import { DICT_TYPE, getDictOptions } from '@/utils/dict'

/**
 * 系统设置信息
 */
export const useSysConfigStore = defineStore('sysConfigStore', () => {
  const sysConfig = ref<
    GetApiRes<typeof tms_web.systemConfig_get_post> & {
      logoFile?: any
      iconFile?: any
    }
  >({})

  const { setTitle, setMenuTitle } = useAppStore()

  const logoUrl = computed(() => {
    if (!sysConfig.value.logo) {
      return ''
    }
    return 'data:image/jpeg;base64,' + sysConfig.value.logo
  })

  const iconUrl = computed(() => {
    if (!sysConfig.value.icon) {
      return ''
    }
    return 'data:image/jpeg;base64,' + sysConfig.value.icon
  })

  /**
   * 同步系统设置
   */
  async function syncSysConfig() {
    const res = await tms_web.systemConfig_get_post({})
    res.data = res.data || {}

    const { logo, icon, accountType, openColdHot, ...data } = res.data

    sysConfig.value = {
      ...data,
      logo,
      icon,
      logoFile: logo,
      iconFile: icon,
      accountType: accountType ? accountType : getDictOptions(DICT_TYPE.BANK_ACCOUNT_TYPE)[0].value,
      openColdHot: openColdHot ? openColdHot : getDictOptions(DICT_TYPE.OPEN_CLOSE)[0].value
    }
    //设置浏览器窗口标题为系统名称
    if (sysConfig.value.name) {
      setTitle(sysConfig.value.name)
      setMenuTitle(sysConfig.value.name)
    }
    //设置浏览器icon
    if (sysConfig.value.icon) {
      //同时切换logo图
      const link = document.querySelector("link[rel*='icon']") as HTMLLinkElement
      //设置为浏览器icon
      link.href = iconUrl.value
    }
  }

  return {
    sysConfig,
    logoUrl,
    iconUrl,
    syncSysConfig
  }
})

export const useSysConfigStoreWithOut = () => {
  return useSysConfigStore(store)
}
