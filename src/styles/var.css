:root {
  --login-bg-color: #293146;

  --left-menu-max-width: 200px;

  --left-menu-min-width: 64px;

  --left-menu-bg-color: #2D57CC;

  --left-menu-bg-light-color: #2D57CC;
  --primary-color: #2D57CC;

  --left-menu-bg-active-color: var(--el-color-primary);

  --left-menu-text-color: #bfcbd9;

  --left-menu-text-active-color: #fff;

  --left-menu-collapse-bg-active-color: var(--el-color-primary);
  /* left menu end */

  /* logo start */
  --logo-height: 50px;

  --logo-title-text-color: #fff;
  /* logo end */

  /* header start */
  --top-header-bg-color: '#fff';

  --top-header-text-color: 'inherit';

  --top-header-hover-color: #f6f6f6;

  --top-tool-height: var(--logo-height);

  --top-tool-p-x: 0;

  --tags-view-height: 30px;
  /* header start */

  /* tab menu start */
  --tab-menu-max-width: 80px;

  --tab-menu-min-width: 30px;

  --tab-menu-collapse-height: 36px;
  /* tab menu end */

  --app-content-padding: 15px;

  --app-content-bg-color: #EDEFF5;

  --app-footer-height: 50px;

  --transition-time-02: 0.2s;
  --el-table-header-bg-color: #ddd!important;
  --el-card-border-radius: 10px;

  --vxe-ui-font-color: #999999;
  --vxe-ui-font-primary-color: #2D57CC;
}

.dark {
  --app-content-bg-color: var(--el-bg-color);
}

html,
body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: Source Han Sans SC;
  
  --vxe-ui-font-primary-color: #2D57CC;
}

*,
:after,
:before {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
.el-table th.el-table__cell,.el-table.is-scrolling-none th.el-table-fixed-column--right,.el-table__header-wrapper tr th.el-table-fixed-column--right,.el-table.is-scrolling-right th.el-table-fixed-column--right{
  background-color: #E8ECF2;
  color: #333;
}
.el-table .el-button.is-plain{
  border: none;
}
.el-card{
  border-radius: 10px;
  border: none;
  padding: 10px;
}