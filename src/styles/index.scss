@use './var.css';
@use './FormCreate/index.scss';
@use './theme.scss';
@use 'element-plus/theme-chalk/dark/css-vars.css';

.reset-margin [class*='el-icon'] + span {
  margin-left: 2px !important;
}

// 解决抽屉弹出时，body宽度变化的问题
.el-popup-parent--hidden {
  width: 100% !important;
}

// 解决表格内容超过表格总宽度后，横向滚动条前端顶不到表格边缘的问题
.el-scrollbar__bar {
  display: flex;
  justify-content: flex-start;
}

/* nprogress 适配 element-plus 的主题色 */
#nprogress {
  & .bar {
    background-color: var(--el-color-primary) !important;
  }

  & .peg {
    box-shadow:
      0 0 10px var(--el-color-primary),
      0 0 5px var(--el-color-primary) !important;
  }

  & .spinner-icon {
    border-top-color: var(--el-color-primary);
    border-left-color: var(--el-color-primary);
  }
}
.text-gray {
  color: #666;
}

.df {
  display: flex;
}

.jcc {
  display: flex;
  justify-content: center;
}

.jcfe, .pagin-box {
  display: flex;
  justify-content: flex-end;
}

.aic {
  display: flex;
  align-items: center;
}

.aife {
  display: flex;
  align-items: flex-end;
}

.jccaic {
  display: flex;
  justify-content: center;
  align-items: center;
}

.aicjcsb, .table-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.aifsjcsb {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}

.aifejcsb {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
}

.cdf, .table-box {
  display: flex;
  flex-direction: column;
}

.flex1 {
  flex: 1;
}

.fs12 {
  font-size: 12px;
}

.fs13 {
  font-size: 13px;
}

.fs14 {
  font-size: 14px;
}

.fs16 {
  font-size: 16px;
}

.fs18 {
  font-size: 18px;
}

.padding{
  padding: 20px;
}
.padding-sm{
  padding: 10px;
}
.margin-xs {
  margin: 5px;
}

.margin-sm {
  margin: 10px;
}

.margin {
  margin: 15px;
}

.margin-lg {
  margin: 20px;
}

.margin-xl {
  margin: 25px;
}

.margin-top-xs {
  margin-top: 5px;
}

.margin-top-sm {
  margin-top: 10px;
}

.margin-top {
  margin-top: 15px;
}

.margin-top-lg {
  margin-top: 20px;
}

.margin-top-xl {
  margin-top: 25px;
}

.margin-right-xs {
  margin-right: 5px;
}

.margin-right-sm {
  margin-right: 10px;
}

.margin-right {
  margin-right: 15px;
}

.margin-right-lg {
  margin-right: 20px;
}

.margin-right-xl {
  margin-right: 25px;
}

.margin-bottom-xs {
  margin-bottom: 5px;
}

.margin-bottom-sm {
  margin-bottom: 10px;
}

.margin-bottom {
  margin-bottom: 15px;
}

.margin-bottom-lg {
  margin-bottom: 20px;
}

.margin-bottom-xl {
  margin-bottom: 25px;
}

.margin-left-xs {
  margin-left: 5px;
}

.margin-left-sm {
  margin-left: 10px;
}

.margin-left {
  margin-left: 15px;
}

.margin-left-lg {
  margin-left: 20px;
}

.margin-left-xl {
  margin-left: 25px;
}

.margin-lr-xs {
  margin-left: 5px;
  margin-right: 5px;
}

.margin-lr-sm {
  margin-left: 10px;
  margin-right: 10px;
}

.margin-lr {
  margin-left: 15px;
  margin-right: 15px;
}

.margin-lr-lg {
  margin-left: 20px;
  margin-right: 20px;
}

.margin-lr-xl {
  margin-left: 25px;
  margin-right: 25px;
}

.margin-tb-xs {
  margin-top: 5px;
  margin-bottom: 5px;
}

.margin-tb-sm {
  margin-top: 10px;
  margin-bottom: 10px;
}

.margin-tb {
  margin-top: 15px;
  margin-bottom: 15px;
}

.margin-tb-lg {
  margin-top: 20px;
  margin-bottom: 20px;
}

.margin-tb-xl {
  margin-top: 25px;
  margin-bottom: 25px;
}

.bg-primary {
  background-color: #4795F5;
  color: #ffffff;
}

.bg-green {
  background-color: #39b54a;
  color: #ffffff;
}

.bg-cyan {
  background-color: #1cbbb4;
  color: #ffffff;
}

.bg-blue {
  background-color: #4795F5;
  color: #ffffff;
}

.bg-purple {
  background-color: #763feb;
  color: #ffffff;
}

.bg-mauve {
  background-color: #9c26b0;
  color: #ffffff;
}

.bg-pink {
  background-color: #e03997;
  color: #ffffff;
}

.bg-brown {
  background-color: #a5673f;
  color: #ffffff;
}

.bg-grey {
  background-color: #8799a3;
  color: #ffffff;
}

.bg-gray {
  background-color: #f0f0f0;
  color: #333333;
}

.bg-black {
  background-color: #333333;
  color: #ffffff;
}

.bg-white {
  background-color: #ffffff;
}

.solid-top {
  border-top: 1px solid #ebeef5;
}

.solid-bottom {
  border-bottom: 1px solid #ebeef5;
}

.solid-left {
  border-left: 1px solid #ebeef5;
}

.solid-right {
  border-right: 1px solid #ebeef5;
}

.text-bold {
  font-weight: bold;
}

.gap-2 {
  gap: 0.5rem;
}

.overflow-hidden {
  overflow: hidden;
}
.card{
  background-color: #fff;
  border-radius: 10px;
  padding: 20px;
  margin: 15px;
}
.full-height{
  height:calc(100vh - var(--top-tool-height) - var(--tags-view-height) - var(--app-footer-height) - 20px);
  .el-card__body{
    height: 100%;
    padding: 0 10px!important;
  }
}
.vxe-tools--wrapper{
  margin-right: 10px;
}
.vxe-grid{
  .vxe-button.type--button.theme--info:not(.is--disabled){
    background-color: rgba(45,87,204,0.1)!important;
    width: 36px;
    height: 36px;
    border: none!important;
    .vxe-button--icon{
      color: var(--el-color-primary);
      // font-weight: 800;
      font-size: 1.2em;
      line-height: 1.2em;
    }
  }
  .vxe-tools--operate{
    .vxe-button{
      background-color: rgba(45,87,204,0.1)!important;
      border-radius: 5px!important;
      border: none!important;
      width: 36px;
      height: 36px;
      .vxe-button--icon{
        color: var(--el-color-primary);
        // font-weight: 800;
        font-size: 1.2em;
        line-height: 1.2em;
      }
    }
  }

}