import { service } from './service'

import { config } from './config'
import { ElLoading } from 'element-plus'

const { default_headers } = config

const request = (option: any) => {
  const { headersType, headers, ...otherOption } = option
  return service({
    ...otherOption,
    headers: {
      'Content-Type': headersType || default_headers,
      ...headers
    }
  })
}
export default {
  get: async <T = any>(option: any) => {
    const res = await request({ method: 'GET', ...option })
    return res.data as unknown as T
  },
  post: async <T = any>(option: any) => {
    //旧系统关于启用遮罩效果的逻辑
    let loading: any = null
    if (option && option.load && option.load.fullLoading) {
      loading = ElLoading.service({
        lock: true,
        text: option.load.msg || '加载中...',
        target: option.load.el
      })
    }
    try {
      const res = await request({ method: 'POST', ...option })
      return res.data as unknown as T
    } catch (e) {
      throw e
    } finally {
      loading && loading.close()
    }
  },
  postOriginal: async (option: any) => {
    const res = await request({ method: 'POST', ...option })
    return res
  },
  delete: async <T = any>(option: any) => {
    const res = await request({ method: 'DELETE', ...option })
    return res.data as unknown as T
  },
  put: async <T = any>(option: any) => {
    const res = await request({ method: 'PUT', ...option })
    return res.data as unknown as T
  },
  download: async <T = any>(option: any) => {
    const res = await request({ method: 'GET', responseType: 'blob', ...option })
    return res as unknown as Promise<T>
  },
  upload: async <T = any>(option: any) => {
    option.headersType = 'multipart/form-data'
    const res = await request({ method: 'POST', ...option })
    return res as unknown as Promise<T>
  }
}
